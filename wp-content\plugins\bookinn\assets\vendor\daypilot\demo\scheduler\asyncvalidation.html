﻿<!DOCTYPE html>
<html>
<head>
    <title>Asynchronous Validation (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <style>
        .scheduler_default_cellparent, .scheduler_default_cell.scheduler_default_cell_business.scheduler_default_cellparent {
            background: #ddd;
        }

    </style>

    <div class="note">Event resizing and moving is validated on drop using an asynchronous HTTP call. Read more about <a href="https://doc.daypilot.org/scheduler/event-moving-validation/">event moving validation</a> and <a href="https://doc.daypilot.org/scheduler/event-resizing-validation/">event resizing validation</a>.</div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMMM yyyy"},
                {groupBy: "Cell", format: "d"}
            ],
            resources: [
              {name: "Room 1", id: "A"},
              {name: "Room 2", id: "B"},
              {name: "Room 3", id: "C"},
              {name: "Room 4", id: "D"},
              {name: "Person 1", id: "E"},
              {name: "Person 2", id: "F"},
              {name: "Person 3", id: "G"},
              {name: "Person 4", id: "H"},
              {name: "Tool 1", id: "I"},
              {name: "Tool 2", id: "J"},
              {name: "Tool 3", id: "K"},
              {name: "Tool 4", id: "L"}
            ],
            onEventMove: async args => {
                args.async = true;
                const {data} = await DayPilot.Http.get("asyncvalidation.txt");

                if (data.error) {
                    await DayPilot.Modal.alert(data.message);
                    args.preventDefault();
                }
                args.loaded();
            },
            onEventResize: async args => {
                args.async = true;
                const {data} = await DayPilot.Http.get("asyncvalidation.txt");

                if (data.error) {
                    await DayPilot.Modal.alert(data.message);
                    args.preventDefault();
                }
                args.loaded();
            },
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
            height: 300
        });
        dp.init();
        dp.scrollTo("2025-03-25");

        const app = {
            init() {
                this.loadEventData();
            },
            loadEventData() {
                // generate and load events
                const events = [];

                for (let i = 0; i < 15; i++) {
                    const duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const durationDays = Math.floor(Math.random() * 6 + 2); // 2 to 7
                    const start = Math.floor(Math.random() * 6) + 2; // 2 to 7

                    const resId = String.fromCharCode(65 + i);

                    const event = {
                        start: new DayPilot.Date("2025-03-25T00:00:00").addDays(start),
                        end: new DayPilot.Date("2025-03-25T12:00:00").addDays(start).addDays(durationDays).addHours(duration),
                        id: DayPilot.guid(),
                        resource: resId,
                        text: `Event ${i + 1}`
                    };

                    events.push(event);
                }
                dp.update({events});
            },
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

