﻿<!DOCTYPE html>
<html>
<head>
    <title>Days View (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note"><b>Note:</b> Read more about the calendar <a href="https://doc.daypilot.org/calendar/days-view/">days
        view</a> [doc.daypilot.org].
    </div>


    <div id="dp"></div>

    <div id="print"></div>

    <script type="text/javascript">

        var dp = new DayPilot.Calendar("dp");

        // view
        dp.startDate = "2023-03-27";
        dp.viewType = "Days";
        dp.days = 5;

        // event creating
        dp.onTimeRangeSelected = function (args) {
            var name = prompt("New event name:", "Event");
            if (!name) return;
            var e = new DayPilot.Event({
                start: args.start,
                end: args.end,
                id: DayPilot.guid(),
                text: name
            });
            dp.events.add(e);
            dp.clearSelection();
        };

        dp.onEventClick = function (args) {
            alert("clicked: " + args.e.id());
        };

        dp.events.list = [
            {
                start: new DayPilot.Date("2023-03-28T12:00:00"),
                end: new DayPilot.Date("2023-03-28T12:00:00").addHours(3),
                id: DayPilot.guid(),
                text: "Special event"
            }
        ];

        dp.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

