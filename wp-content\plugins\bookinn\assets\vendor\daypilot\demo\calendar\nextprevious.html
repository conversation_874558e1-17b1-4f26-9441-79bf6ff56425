﻿<!DOCTYPE html>
<html>
<head>
    <title>Next/Previous (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->
    <div class="note"><b>Note:</b> Read more about the <a
        href="https://doc.daypilot.org/calendar/next-and-previous-buttons/">next/previous buttons</a> [doc.daypilot.org].
    </div>


    <div class="tools">
        <a href="#" id="previous">Previous</a>
        <a href="#" id="next">Next</a>
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Calendar("dp", {
            viewType: "Week",
            startDate: new DayPilot.Date("2021-03-25"),
            onTimeRangeSelected: async function (args) {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                const name = modal.result;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    text: name
                });

            }
        });
        dp.init();


        const app = {
            elements: {
                previous: document.getElementById('previous'),
                next: document.getElementById('next')
            },
            init() {
                this.addEventHandlers();
                this.loadEventData();
            },
            addEventHandlers() {
                this.elements.previous.addEventListener('click', (ev) => {
                    ev.preventDefault();
                    this.previous();
                });
                this.elements.next.addEventListener('click', (ev) => {
                    ev.preventDefault();
                    this.next();
                });
            },
            previous() {
                const startDate = dp.startDate.addDays(-7);
                dp.update({startDate: startDate});
            },
            next() {
                const startDate = dp.startDate.addDays(7);
                dp.update({startDate: startDate});
            },
            loadEventData() {
                const events = [
                    {
                        start: new DayPilot.Date("2021-03-25T12:00:00"),
                        end: new DayPilot.Date("2021-03-25T12:00:00").addHours(3),
                        id: DayPilot.guid(),
                        text: "Special event"
                    }
                ];
                dp.update({events: events});
            }
        };
        app.init();

    </script>


    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

