﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};!function(){function e(e){var t=DayPilot.Date.Cache.Ticks;if(t[e])return DayPilot.Stats.cacheHitsTicks+=1,t[e];var a,r=new Date(e),o=r.getUTCMilliseconds();a=0===o?"":o<10?".00"+o:o<100?".0"+o:"."+o;var n=r.getUTCSeconds();n<10&&(n="0"+n);var d=r.getUTCMinutes();d<10&&(d="0"+d);var i=r.getUTCHours();i<10&&(i="0"+i);var l=r.getUTCDate();l<10&&(l="0"+l);var s=r.getUTCMonth()+1;s<10&&(s="0"+s);var c=r.getUTCFullYear();if(c<=0)throw"The minimum year supported is 1.";c<10?c="000"+c:c<100?c="00"+c:c<1e3&&(c="0"+c);var u=c+"-"+s+"-"+l+"T"+i+":"+d+":"+n+a;return t[e]=u,u}function t(e,t){return!DayPilot.Util.isNullOrUndefined(e)&&(!DayPilot.Util.isNullOrUndefined(t)&&e.toLocaleLowerCase()===t.toLocaleLowerCase())}function a(e){e=Math.min(e,255),e=Math.max(e,0);var t=e.toString(16);return e<16?"0"+t:t}if("undefined"==typeof DayPilot.$){"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),DayPilot.$=function(e){return document.getElementById(e)},Object.defineProperty(DayPilot,"isKhtml",{get:function(){return"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("KHTML")!==-1}}),DayPilot.touch={},DayPilot.touch.start="touchstart",DayPilot.touch.move="touchmove",DayPilot.touch.end="touchend",DayPilot.mo2=function(e,t){if("undefined"!=typeof t.offsetX){var a={x:t.offsetX+1,y:t.offsetY+1};if(!e)return a;for(var r=t.srcElement;r&&r!==e;)"SPAN"!==r.tagName&&(a.x+=r.offsetLeft,r.offsetTop>0&&(a.y+=r.offsetTop-r.scrollTop)),r=r.offsetParent;return r?a:null}if("undefined"!=typeof t.layerX){var a={x:t.layerX,y:t.layerY,src:t.target};if(!e)return a;for(var r=t.target;r&&"absolute"!==r.style.position&&"relative"!==r.style.position;)r=r.parentNode,DayPilot.isKhtml&&(a.y+=r.scrollTop);for(;r&&r!==e;)a.x+=r.offsetLeft,a.y+=r.offsetTop-r.scrollTop,r=r.offsetParent;return r?a:null}return null},DayPilot.mo3=function(e,t){var a,r=DayPilot.page(t);if(r)if(e){var o=DayPilot.abs(e);if(!o)throw new Error("no abs");a={x:r.x-o.x,y:r.y-o.y}}else a={x:r.x,y:r.y};else if(a=DayPilot.mo2(e,t),!a)return null;return a.shift=t.shiftKey,a.meta=t.metaKey,a.ctrl=t.ctrlKey,a.alt=t.altKey,a},DayPilot.browser={},Object.defineProperty(DayPilot.browser,"hover",{get:function(){return!window.matchMedia("(any-hover: none)").matches}}),DayPilot.touch={},DayPilot.debounce=function(e,t){var a;return function(){var r=this,o=arguments,n=function(){a=null,e.apply(r,o)};clearTimeout(a),a=setTimeout(n,t)}},DayPilot.page=function(e){var t=e.changedTouches&&e.changedTouches[0]?e.changedTouches[0]:e;return"undefined"!=typeof t.pageX?{x:t.pageX,y:t.pageY}:"undefined"!=typeof e.clientX&&document.body&&document.documentElement?{x:e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y:e.clientY+document.body.scrollTop+document.documentElement.scrollTop}:null},DayPilot.abs=function(e,t){if(!e)return null;if(e.getBoundingClientRect){var a=DayPilot.absBoundingClientBased(e);if(t){var r=DayPilot.absOffsetBased(e,!1),t=DayPilot.absOffsetBased(e,!0);a.x+=t.x-r.x,a.y+=t.y-r.y,a.w=t.w,a.h=t.h}return a}return DayPilot.absOffsetBased(e,t)},DayPilot.absBoundingClientBased=function(e){var t=e.getBoundingClientRect();return{x:t.left+window.pageXOffset,y:t.top+window.pageYOffset,w:e.clientWidth,h:e.clientHeight,toString:function(){return"x:"+this.x+" y:"+this.y+" w:"+this.w+" h:"+this.h}}},DayPilot.absOffsetBased=function(e,t){for(var a={x:e.offsetLeft,y:e.offsetTop,w:e.clientWidth,h:e.clientHeight,toString:function(){return"x:"+this.x+" y:"+this.y+" w:"+this.w+" h:"+this.h}};e.offsetParent;)e=e.offsetParent,a.x-=e.scrollLeft,a.y-=e.scrollTop,t&&(a.x<0&&(a.w+=a.x,a.x=0),a.y<0&&(a.h+=a.y,a.y=0),e.scrollLeft>0&&a.x+a.w>e.clientWidth&&(a.w-=a.x+a.w-e.clientWidth),e.scrollTop&&a.y+a.h>e.clientHeight&&(a.h-=a.y+a.h-e.clientHeight)),a.x+=e.offsetLeft,a.y+=e.offsetTop;var r=DayPilot.pageOffset();return a.x+=r.x,a.y+=r.y,a},DayPilot.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},DayPilot.distance=function(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},DayPilot.sheet=function(){function e(){for(var e=document.querySelectorAll("style[nonce]"),t=0;t<e.length;t++){var a=e[t];if(a.nonce)return a.nonce}if(document.currentScript&&document.currentScript.nonce)return document.currentScript.nonce;for(var r=document.querySelectorAll("script[nonce]"),o=0;o<r.length;o++){var n=r[o];if(n.nonce)return n.nonce}return""}if("undefined"==typeof window){var t={};return t.add=function(){},t.commit=function(){},t}var a=document.createElement("style");a.nonce=e(),a.styleSheet||a.appendChild(document.createTextNode("")),(document.head||document.getElementsByTagName("head")[0]).appendChild(a);var r=!!a.styleSheet,t={};return t.rules=[],t.commit=function(){r&&(a.styleSheet.cssText=this.rules.join("\n"))},t.add=function(e,t,o){return r?void this.rules.push(e+"{"+t+"}"):void(a.sheet.insertRule?("undefined"==typeof o&&(o=a.sheet.cssRules.length),a.sheet.insertRule(e+"{"+t+"}",o)):a.sheet.addRule&&a.sheet.addRule(e,t,o))},t},DayPilot.gs=function(e,t){return window.getComputedStyle(e,null).getPropertyValue(t)||""},DayPilot.StyleReader=function(e){this.get=function(t){return e?DayPilot.gs(e,t):null},this.getPx=function(e){var t=this.get(e);return t.indexOf("px")===-1?void 0:parseInt(t,10)}},function(){if(!DayPilot.Global.defaultCss){var e=DayPilot.sheet();e.add(".menu_default_main","user-select:none; font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;font-size: 13px;border: 1px solid #dddddd;background-color: white;padding: 0px;cursor: default;background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAABCAIAAABG0om7AAAAKXRFWHRDcmVhdGlvbiBUaW1lAHBvIDEwIDUgMjAxMCAyMjozMzo1OSArMDEwMGzy7+IAAAAHdElNRQfaBQoUJAesj4VUAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAABGdBTUEAALGPC/xhBQAAABVJREFUeNpj/P//PwO1weMnT2RlZAAYuwX/4oA3BgAAAABJRU5ErkJggg==);background-repeat: repeat-y;xborder-radius: 5px;-moz-box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);-webkit-box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);"),e.add(".menu_default_main, .menu_default_main *, .menu_default_main *:before, .menu_default_main *:after","box-sizing: content-box;"),e.add(".menu_default_title","background-color: #f2f2f2;border-bottom: 1px solid gray;padding: 4px 4px 4px 37px;"),e.add(".menu_default_main a","padding: 2px 2px 2px 35px;color: black;text-decoration: none;cursor: default;"),e.add(".menu_default_main.menu_default_withchildren a","padding: 2px 35px 2px 35px;"),e.add(".menu_default_main a img","margin-left: 6px;margin-top: 2px;"),e.add(".menu_default_item_text","display: block;height: 20px;line-height: 20px; overflow:hidden;padding-left: 2px;padding-right: 20px; white-space: nowrap;"),e.add(".menu_default_main a:hover","background-color: #f3f3f3;"),e.add(".menu_default_main div div","border-top: 1px solid #dddddd;margin-top: 2px;margin-bottom: 2px;margin-left: 28px;"),e.add(".menu_default_main a.menu_default_item_disabled","color: #ccc"),e.add(".menu_default_item_haschildren.menu_default_item_haschildren_active","background-color: #f3f3f3;"),e.add(".menu_default_item_haschildren a:before","content: ''; border-width: 5px; border-color: transparent transparent transparent #666; border-style: solid; width: 0px; height:0px; position: absolute; right: 5px; margin-top: 5px;"),e.add(".menu_default_item_icon","position: absolute; top:0px; left: 0px; padding: 2px 2px 2px 8px;"),e.add(".menu_default_item a i","height: 20px;line-height: 20px;"),e.add(".menu_default_item .menu_default_item_symbol","width: 18px; height: 18px; color: #999; margin-left: 6px;margin-top: 2px;"),e.add(".menubar_default_main","border-bottom: 1px solid #ccc; font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif; font-size: 13px; user-select:none;"),e.add(".menubar_default_item","display: inline-block;  padding: 6px 10px; cursor: default;"),e.add(".menubar_default_item:hover","background-color: #f2f2f2;"),e.add(".menubar_default_item_active","background-color: #f2f2f2;"),e.add(".calendar_default_main","  --dp-calendar-border-color: #c0c0c0;  --dp-calendar-font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;  --dp-calendar-font-size: 13px;  --dp-calendar-header-bg-color: #f3f3f3;  --dp-calendar-header-color: #333;  --dp-calendar-colheader-padding: 0px;  --dp-calendar-rowheader-font-size: 16pt;  --dp-calendar-rowheader-padding: 3px;  --dp-calendar-cell-bg-color: #f9f9f9;  --dp-calendar-cell-business-bg-color: #ffffff;  --dp-calendar-cell-border-color: #ddd;  --dp-calendar-colheader-horizontal-align: center;  --dp-calendar-colheader-vertical-align: center;  --dp-calendar-allday-event-color: #333;  --dp-calendar-allday-event-border-color: #999;  --dp-calendar-allday-event-border: 1px solid var(--dp-calendar-allday-event-border-color);  --dp-calendar-allday-event-border-radius: 0px;  --dp-calendar-allday-event-bg-top-color: #ffffff;  --dp-calendar-allday-event-bg-bottom-color: #eeeeee;  --dp-calendar-allday-event-background: linear-gradient(to bottom, var(--dp-calendar-allday-event-bg-top-color) 0%, var(--dp-calendar-allday-event-bg-bottom-color) 100%);  --dp-calendar-allday-event-box-shadow: none;  --dp-calendar-allday-event-padding: 4px;  --dp-calendar-allday-event-horizontal-align: flex-start;  --dp-calendar-event-color: #333;  --dp-calendar-event-border-color: #999;  --dp-calendar-event-border: 1px solid var(--dp-calendar-event-border-color);  --dp-calendar-event-border-radius: 0px;  --dp-calendar-event-box-shadow: none;  --dp-calendar-event-bg-top-color: #ffffff;  --dp-calendar-event-bg-bottom-color: #eeeeee;  --dp-calendar-event-background: linear-gradient(to bottom, var(--dp-calendar-event-bg-top-color) 0%, var(--dp-calendar-event-bg-bottom-color) 100%);  --dp-calendar-event-bar-bg-color: #9dc8e8;  --dp-calendar-event-bar-color: #1066a8;  --dp-calendar-event-bar-width: 6px;  --dp-calendar-event-bar-left: 0px;  --dp-calendar-event-bar-bottom: 0px;  --dp-calendar-event-bar-top: 0px;  --dp-calendar-event-bar-display: block;  --dp-calendar-event-padding: 2px;  --dp-calendar-event-padding-left: 8px;  --dp-calendar-message-bg-color: #ffa216;  --dp-calendar-message-color: #ffffff;  --dp-calendar-message-padding: 10px;  --dp-calendar-message-opacity: 0.9;  --dp-calendar-selected-event-bg-color: #ddd;  --dp-calendar-shadow-color: #bbbbbb;  --dp-calendar-shadow-border-color: #888888;  --dp-calendar-forbidden-shadow-border-color: #cc0000;  --dp-calendar-forbidden-shadow-bg-color: #cc4125;  --dp-calendar-now-indicator-color: red;  --dp-calendar-scroll-bg-color: #f3f3f3;"),e.add(".calendar_default_main *, .calendar_default_main *:before, .calendar_default_main *:after","box-sizing: content-box;"),e.add(".calendar_default_main","border:1px solid var(--dp-calendar-border-color); font-family:var(--dp-calendar-font-family); font-size:var(--dp-calendar-font-size);"),e.add(".calendar_default_rowheader_inner, .calendar_default_cornerright_inner, .calendar_default_corner_inner, .calendar_default_colheader_inner, .calendar_default_alldayheader_inner","color: var(--dp-calendar-header-color); background: var(--dp-calendar-header-bg-color);"),e.add(".calendar_default_colheader_back","background: var(--dp-calendar-header-bg-color); border-bottom: 1px solid red;"),e.add(".calendar_default_colheader_back_inner","position: absolute; inset: 0; border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_cornerright_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_direction_rtl .calendar_default_cornerright_inner","border-right: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_rowheader_inner","font-size: var(--dp-calendar-rowheader-font-size); text-align: right; position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color); padding: var(--dp-calendar-rowheader-padding);"),e.add(".calendar_default_rowheader_simple .calendar_default_rowheader_inner","font-size: inherit; display: flex; align-items: center; justify-content: center; white-space: nowrap;"),e.add(".calendar_default_direction_rtl .calendar_default_rowheader_inner","border-right: none;"),e.add(".calendar_default_corner_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_direction_rtl .calendar_default_corner_inner","border-right: none;"),e.add(".calendar_default_rowheader_minutes","font-size: 10px; vertical-align: super; padding-left: 2px; padding-right: 2px;"),e.add(".calendar_default_colheader_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; padding: var(--dp-calendar-colheader-padding);border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color); display: flex; align-items: var(--dp-calendar-colheader-vertical-align); justify-content: var(--dp-calendar-colheader-horizontal-align);"),e.add(".calendar_default_cell_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-cell-border-color); border-bottom: 1px solid var(--dp-calendar-cell-border-color); background: var(--dp-calendar-cell-bg-color);"),e.add(".calendar_default_cell_business .calendar_default_cell_inner","background: var(--dp-calendar-cell-business-bg-color);"),e.add(".calendar_default_alldayheader_inner","text-align: center; position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_message","opacity: var(--dp-calendar-message-opacity); padding: var(--dp-calendar-message-padding); color: var(--dp-calendar-message-color); background: var(--dp-calendar-message-bg-color);"),e.add(".calendar_default_event_inner","color: var(--dp-calendar-event-color); border: var(--dp-calendar-event-border); border-radius: var(--dp-calendar-event-border-radius); background: var(--dp-calendar-event-background);"),e.add(".calendar_default_alldayevent","box-shadow: var(--dp-calendar-allday-event-box-shadow); border-radius: var(--dp-calendar-allday-event-border-radius);"),e.add(".calendar_default_alldayevent_inner","color: var(--dp-calendar-allday-event-color); border: var(--dp-calendar-allday-event-border); border-radius: var(--dp-calendar-allday-event-border-radius); background: var(--dp-calendar-allday-event-background);"),e.add(".calendar_default_event_bar","display: var(--dp-calendar-event-bar-display); top: var(--dp-calendar-event-bar-top); bottom: var(--dp-calendar-event-bar-bottom); left: var(--dp-calendar-event-bar-left); width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-bg-color);"),e.add(".calendar_default_direction_rtl .calendar_default_event_bar","top: 0px; bottom: 0px; right: 0px; width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-bg-color);"),e.add(".calendar_default_event_bar_inner","position: absolute; width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-color);"),e.add(".calendar_default_selected .calendar_default_event_inner","background: var(--dp-calendar-selected-event-bg-color);"),e.add(".calendar_default_alldayevent_inner","position: absolute; top: 0px; bottom: 0px; left: 0px; right: 0px; overflow: hidden; padding: var(--dp-calendar-allday-event-padding); margin-right: 0px; display: flex; align-items: center; justify-content: var(--dp-calendar-allday-event-horizontal-align);"),e.add(".calendar_default_event_withheader .calendar_default_event_inner","padding-top: 15px;"),e.add(".calendar_default_event","box-shadow: var(--dp-calendar-event-box-shadow); border-radius: var(--dp-calendar-event-border-radius); cursor: default;"),e.add(".calendar_default_event_inner","position: absolute; overflow: hidden; top: 0px; bottom: 0px; left: 0px; right: 0px; padding: var(--dp-calendar-event-padding) var(--dp-calendar-event-padding) var(--dp-calendar-event-padding) var(--dp-calendar-event-padding-left);"),e.add(".calendar_default_direction_rtl .calendar_default_event_inner","padding: 2px 8px 2px 2px;"),e.add(".calendar_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-calendar-shadow-color); border: 1px solid var(--dp-calendar-shadow-border-color); opacity: 0.5; height: 100%;"),e.add(".calendar_default_shadow","box-shadow: 0 2px 5px rgba(0,0,0,0.2);"),e.add(".calendar_default_shadow_forbidden:after","content: ''; position: absolute; top: 5px; left: calc(50% - 10px); border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; background-image: url('data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 viewBox=%270 0 20 20%27%3E%3Ccircle cx=%2710%27 cy=%2710%27 r=%279%27 fill=%27%23cc0000aa%27 /%3E%3Cline x1=%275%27 y1=%275%27 x2=%2715%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3Cline x1=%2715%27 y1=%275%27 x2=%275%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: center; background-size: contain;"),e.add(".calendar_default_shadow_forbidden .calendar_default_shadow_inner","border: 1px solid var(--dp-calendar-forbidden-shadow-border-color); background: var(--dp-calendar-forbidden-shadow-bg-color);"),e.add(".calendar_default_event_delete","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAI5JREFUKFNtkLERgCAMRbmzdK8s4gAUlhYOYEHJEJYOYOEwDmGBPxC4kOPfvePy84MGR0RJ2N1A8H3N6DATwSQ57m2ql8NBG+AEM7D+UW+wjdfUPgerYNgB5gOLRHqhcasg84C2QxPMtrUhSqQIhg7ypy9VM2EUZPI/4rQ7rGxqo9sadTegw+UdjeDLAKUfhbaQUVPIfJYAAAAASUVORK5CYII=) center center no-repeat; opacity: 0.6; cursor: pointer;"),e.add(".calendar_default_event_delete:hover","opacity: 1; -ms-filter: none;"),e.add(".calendar_default_scroll_up","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAB3RJTUUH2wESDiYcrhwCiQAAAAlwSFlzAAAuIwAALiMBeKU/dgAAAARnQU1BAACxjwv8YQUAAACcSURBVHjaY2AgF9wWsTW6yGMlhi7OhC7AyMDQzMnBXIpFHAFuCtuaMTP+P8nA8P/b1x//FfW/HHuF1UQmxv+NUP1c3OxMVVhNvCVi683E8H8LXOY/w9+fTH81tF8fv4NiIpBRj+YoZtZ/LDUoJmKYhsVUpv0MDiyMDP96sIYV0FS2/8z9ICaLlOhvS4b/jC//MzC8xBG0vJeF7GQBlK0xdiUzCtsAAAAASUVORK5CYII=);"),e.add(".calendar_default_scroll_down","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAACqSURBVChTY7wpam3L9J+xmQEP+PGPKZZxP4MDi4zI78uMDIwa2NT+Z2DYovrmiC+TI8OBP/8ZmEqwGvif4e8vxr+FIDkmEKH25vBWBgbG0+iK/zEwLtF+ffwOXCGI8Y+BoRFFIdC030x/WmBiYBNhpgLdswNJ8RSYaSgmgk39z1gPUfj/29ef/9rwhQTDHRHbrbdEbLvRFcGthkkAra/9/uMvhkK8piNLAgCRpTnNn4AEmAAAAABJRU5ErkJggg==);"),e.add(".calendar_default_now","background-color: var(--dp-calendar-now-indicator-color);"),e.add(".calendar_default_now:before","content: ''; top: -5px; border-width: 5px; border-color: transparent transparent transparent var(--dp-calendar-now-indicator-color); border-style: solid; width: 0px; height: 0px; position: absolute; -moz-transform: scale(.9999);"),e.add(".calendar_default_shadow_top","box-sizing: border-box; padding: 2px; border: 1px solid var(--dp-calendar-border-color); background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); pointer-events: none;"),e.add(".calendar_default_shadow_bottom","box-sizing: border-box; padding: 2px; border: 1px solid var(--dp-calendar-border-color); background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); pointer-events: none;"),e.add(".calendar_default_crosshair_vertical, .calendar_default_crosshair_horizontal, .calendar_default_crosshair_left, .calendar_default_crosshair_top","background-color: gray; opacity: 0.2;"),e.add(".calendar_default_loading","background-color: orange; color: white; padding: 2px;"),e.add(".calendar_default_scroll","background-color: var(--dp-calendar-header-bg-color);"),e.add(".calendar_default_event_moving_source","opacity: 0.5;"),e.add(".calendar_default_colmove_handle","background-repeat: no-repeat; background-position: center center; background-color: #ccc; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAKCAYAAACT+/8OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAClJREFUGFdj+P//P4O9vX2Bg4NDP4gNFgBytgPxebgAMsYuQGMz/jMAAFsTZDPYJlDHAAAAAElFTkSuQmCC); cursor: move;"),e.add(".calendar_default_colheader:hover .calendar_default_colheader_splitter","background-color: #c0c0c0;"),e.add(".calendar_default_colmove_source","background-color: black; opacity: 0.5;"),e.add(".calendar_default_colmove_position_before","box-sizing: border-box; border-left: 2px solid #999999;"),e.add(".calendar_default_colmove_position_before:before","content: ''; border-width: 6px; border-color: transparent #999999 transparent transparent; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_after","box-sizing: border-box; border-right: 2px solid #999999;"),e.add(".calendar_default_colmove_position_after:before","content: ''; border-width: 6px; border-color: transparent transparent transparent #999999; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_child","box-sizing: border-box; border-bottom: 2px solid #999999;"),e.add(".calendar_default_colmove_position_child:before","content: ''; border-width: 6px; border-color: #999999 transparent transparent transparent; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_forbidden","border-top: 2px solid red;"),e.add(".calendar_default_colheader .calendar_default_colheader_splitter:hover","background-color: #999999;"),e.add(".calendar_default_block","background-color: #808080; opacity: 0.5;"),e.add(".month_default_main","  --dp-month-border-color: #c0c0c0;  --dp-month-font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;  --dp-month-font-size: 13px;  --dp-month-cell-border-color: #ddd;  --dp-month-cell-bg-color: #f9f9f9;  --dp-month-cell-business-bg-color: #ffffff;  --dp-month-event-color: #333;  --dp-month-event-border-color: #999;  --dp-month-event-border: 1px solid var(--dp-month-event-border-color);  --dp-month-event-bg-top-color: #ffffff;  --dp-month-event-bg-bottom-color: #eeeeee;  --dp-month-event-background: linear-gradient(to bottom, var(--dp-month-event-bg-top-color) 0%, var(--dp-month-event-bg-bottom-color) 100%);  --dp-month-event-horizontal-align: flex-start;  --dp-month-event-vertical-align: center;  --dp-month-event-padding: 2px;  --dp-month-event-padding-left: 10px;  --dp-month-event-padding-rtl: 2px 10px 2px 1px;  --dp-month-event-border-radius: 0px;  --dp-month-event-box-shadow: none;  --dp-month-event-bar-top: 1px;  --dp-month-event-bar-left: 2px;  --dp-month-event-bar-bottom: 1px;  --dp-month-event-bar-width: 6px;  --dp-month-event-bar-color: #1066a8;  --dp-month-event-bar-display: block;  --dp-month-header-bg-color: #f3f3f3;  --dp-month-header-color: #333;  --dp-month-header-horizontal-align: center;  --dp-month-header-vertical-align: center;  --dp-month-header-padding: 0px;  --dp-month-message-bg-color: #ffa216;  --dp-month-message-color: #ffffff;  --dp-month-message-padding: 10px;  --dp-month-selected-event-bg-color: #ddd;  --dp-month-shadow-color: #bbbbbb;  --dp-month-shadow-border-color: #888888;"),e.add(".month_default_main *, .month_default_main *:before, .month_default_main *:after","box-sizing: content-box; "),e.add(".month_default_main","border: 1px solid var(--dp-month-border-color); font-family: var(--dp-month-font-family); font-size: var(--dp-month-font-size); color: #333; "),e.add(".month_default_cell_inner","border-right: 1px solid var(--dp-month-cell-border-color); border-bottom: 1px solid var(--dp-month-cell-border-color); position: absolute; top: 0; left: 0; bottom: 0; right: 0; background-color: var(--dp-month-cell-bg-color); "),e.add(".month_default_cell_business .month_default_cell_inner","background-color: var(--dp-month-cell-business-bg-color); "),e.add(".month_default_cell_header","text-align: right; padding: 4px; box-sizing: border-box; "),e.add(".month_default_header_inner","position: absolute; inset: 0; border-right: 1px solid var(--dp-month-border-color); border-bottom: 1px solid var(--dp-month-border-color); cursor: default; color: var(--dp-month-header-color); background: var(--dp-month-header-bg-color); overflow: hidden; display: flex; align-items: var(--dp-month-header-vertical-align); justify-content: var(--dp-month-header-horizontal-align); padding: var(--dp-month-header-padding);"),e.add(".month_default_message","opacity: 0.9; color: var(--dp-month-message-color); background: var(--dp-month-message-bg-color); padding: var(--dp-month-message-padding); "),e.add(".month_default_event","border-radius: var(--dp-month-event-border-radius); box-shadow: var(--dp-month-event-box-shadow); "),e.add(".month_default_event_inner","position: absolute; top: 0; bottom: 0; left: 1px; right: 1px; overflow: hidden; padding: var(--dp-month-event-padding) var(--dp-month-event-padding) var(--dp-month-event-padding) var(--dp-month-event-padding-left); color: var(--dp-month-event-color); background: var(--dp-month-event-background); border: var(--dp-month-event-border); border-radius: var(--dp-month-event-border-radius); display: flex; align-items: var(--dp-month-event-vertical-align); justify-content: var(--dp-month-event-horizontal-align); "),e.add(".month_default_direction_rtl .month_default_event_inner","right: 2px; padding: var(--dp-month-event-padding-rtl); "),e.add(".month_default_event_continueright .month_default_event_inner","border-top-right-radius: 0; border-bottom-right-radius: 0; border-right-style: dotted; "),e.add(".month_default_event_continueleft .month_default_event_inner","border-top-left-radius: 0; border-bottom-left-radius: 0; border-left-style: dotted; "),e.add(".month_default_event_bar","display: var(--dp-month-event-bar-display); top: var(--dp-month-event-bar-top); bottom: var(--dp-month-event-bar-bottom); left: var(--dp-month-event-bar-left); width: var(--dp-month-event-bar-width); "),e.add(".month_default_direction_rtl .month_default_event_bar","top: 1px; bottom: 1px; right: 3px; width: var(--dp-month-event-bar-width); "),e.add(".month_default_event_bar_inner","position: absolute; width: var(--dp-month-event-bar-width); background-color: var(--dp-month-event-bar-color); "),e.add(".month_default_event_continueleft .month_default_event_bar","display: none; "),e.add(".month_default_selected .month_default_event_inner","background: var(--dp-month-selected-event-bg-color); "),e.add(".month_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-month-shadow-color); border: 1px solid var(--dp-month-shadow-border-color); opacity: 0.5; height: 100%; "),e.add(".month_default_shadow","box-shadow: 0 2px 5px rgba(0, 0, 0, .2); "),e.add(".month_default_event_delete","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAI5JREFUKFNtkLERgCAMRbmzdK8s4gAUlhYOYEHJEJYOYOEwDmGBPxC4kOPfvePy84MGR0RJ2N1A8H3N6DATwSQ57m2ql8NBG+AEM7D+UW+wjdfUPgerYNgB5gOLRHqhcasg84C2QxPMtrUhSqQIhg7ypy9VM2EUZPI/4rQ7rGxqo9sadTegw+UdjeDLAKUfhbaQUVPIfJYAAAAASUVORK5CYII=) center center no-repeat; opacity: 0.6; cursor: pointer; "),e.add(".month_default_event_delete:hover","opacity: 1; "),e.add(".month_default_event_timeleft","color: #ccc; font-size: 11px; display: flex; align-items: center; "),e.add(".month_default_event_timeright","color: #ccc; font-size: 11px; display: flex; align-items: center; justify-content: end; "),e.add(".month_default_loading","background-color: orange; color: white; padding: 2px; "),e.add(".month_default_shadow_forbidden:after","content: ''; position: absolute; top: calc(50% - 10px); left: 10px; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; background-image: url('data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 viewBox=%270 0 20 20%27%3E%3Ccircle cx=%2710%27 cy=%2710%27 r=%279%27 fill=%27%23cc0000aa%27 /%3E%3Cline x1=%275%27 y1=%275%27 x2=%2715%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3Cline x1=%2715%27 y1=%275%27 x2=%275%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: center; background-size: contain; "),e.add(".month_default_shadow_forbidden .month_default_shadow_inner","border: 1px solid #cc0000; background: #cc4125; "),e.add(".navigator_default_main","  --dp-nav-border-color: #c0c0c0;  --dp-nav-font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;  --dp-nav-font-size: 12px;  --dp-nav-title-color: #333;  --dp-nav-title-bg-color: #f3f3f3;  --dp-nav-dayheader-color: #333;  --dp-nav-dayheader-bg-color: #ffffff;  --dp-nav-weeknumber-color: #999;  --dp-nav-weeknumber-bg-color: #ffffff;  --dp-nav-day-color: #000;  --dp-nav-day-bg-color: #ffffff;  --dp-nav-dayother-color: gray;  --dp-nav-dayother-bg-color: #ffffff;  --dp-nav-weekend-bg-color: #f0f0f0;  --dp-nav-select-bg-color: #FFE794;  --dp-nav-text-align: center;"),e.add(".navigator_default_main *, .navigator_default_main *:before, .navigator_default_main *:after","box-sizing: content-box;"),e.add(".navigator_default_main","border-left: 1px solid var(--dp-nav-border-color);border-right: 1px solid var(--dp-nav-border-color);border-bottom: 1px solid var(--dp-nav-border-color);background-color: white;color: var(--dp-nav-day-color);box-sizing: content-box;"),e.add(".navigator_default_month","font-family: var(--dp-nav-font-family);font-size: var(--dp-nav-font-size);"),e.add(".navigator_default_day","color: var(--dp-nav-day-color); background-color: var(--dp-nav-day-bg-color);"),e.add(".navigator_default_weekend","background-color: var(--dp-nav-weekend-bg-color);"),e.add(".navigator_default_dayheader","color: var(--dp-nav-dayheader-color);background-color: var(--dp-nav-dayheader-bg-color);text-align: var(--dp-nav-text-align);padding: 0px;"),e.add(".navigator_default_line","border-bottom: 1px solid var(--dp-nav-border-color);"),e.add(".navigator_default_dayother","color: var(--dp-nav-dayother-color); background-color: var(--dp-nav-dayother-bg-color);"),e.add(".navigator_default_todaybox","border: 1px solid red;"),e.add(".navigator_default_title, .navigator_default_titleleft, .navigator_default_titleright","box-sizing: border-box; border-top: 1px solid var(--dp-nav-border-color);border-bottom: 1px solid var(--dp-nav-border-color);color: var(--dp-nav-title-color);background: var(--dp-nav-title-bg-color);text-align: var(--dp-nav-text-align);"),e.add(".navigator_default_busy","font-weight: bold;"),e.add(".navigator_default_cell","text-align: var(--dp-nav-text-align);"),e.add(".navigator_default_select .navigator_default_cell_box","background-color: var(--dp-nav-select-bg-color);opacity: 0.5;"),e.add(".navigator_default_weeknumber","text-align: var(--dp-nav-text-align);color: var(--dp-nav-weeknumber-color);background: var(--dp-nav-weeknumber-bg-color);"),
e.add(".navigator_default_cell_text","cursor: pointer;"),e.add(".navigator_default_todaysection","box-sizing: border-box; display: flex; align-items: center; justify-content: center; border-top: 1px solid var(--dp-nav-border-color);"),e.add(".navigator_default_todaysection_button","cursor: pointer; color: #333; background-color: #f0f0f0; border: 1px solid var(--dp-nav-border-color); padding: 5px 10px; border-radius: 0px; "),e.add(".scheduler_default_main",'  --dp-scheduler-border-color: #c0c0c0;  --dp-scheduler-border-inner-color: #e0e0e0;  --dp-scheduler-cell-bg-color: #f9f9f9;  --dp-scheduler-cell-business-bg-color: #ffffff;  --dp-scheduler-event-background: linear-gradient(to bottom, var(--dp-scheduler-event-bg-top-color) 0%, var(--dp-scheduler-event-bg-bottom-color) 100%);  --dp-scheduler-event-bg-bottom-color: #eeeeee;  --dp-scheduler-event-bg-top-color: #ffffff;  --dp-scheduler-event-bar-bg-color: #9dc8e8;  --dp-scheduler-event-bar-color: #1066a8;  --dp-scheduler-event-bar-display: block;  --dp-scheduler-event-bar-height: 4px;  --dp-scheduler-event-bar-left: 0px;  --dp-scheduler-event-bar-right: 0px;  --dp-scheduler-event-bar-top: 0px;  --dp-scheduler-event-border: 1px solid var(--dp-scheduler-event-border-color);  --dp-scheduler-event-border-color: #ccc;  --dp-scheduler-event-border-radius: 0px;  --dp-scheduler-event-box-shadow: none;  --dp-scheduler-event-color: #333;  --dp-scheduler-event-horizontal-align: flex-start;  --dp-scheduler-event-milestone-color: #38761d;  --dp-scheduler-event-padding: 2px;  --dp-scheduler-event-selected-bg-color: #ddd;  --dp-scheduler-event-vertical-align: center;  --dp-scheduler-focus-outline-color: red;  --dp-scheduler-font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;  --dp-scheduler-font-size: 13px;  --dp-scheduler-grid-line-break-color: #999;  --dp-scheduler-grid-line-color: #eee;  --dp-scheduler-header-bg-color: #f3f3f3;  --dp-scheduler-header-color: #333;  --dp-scheduler-link-color: #cc0000;  --dp-scheduler-message-bg-color: #ffa216;  --dp-scheduler-message-color: #ffffff;  --dp-scheduler-message-padding: 10px;  --dp-scheduler-rowheader-padding: 7px;  --dp-scheduler-rowheader-vertical-align: center;  --dp-scheduler-selectionrectangle-color: #1066a8;  --dp-scheduler-shadow-border-color: #888888;  --dp-scheduler-shadow-color: #bbbbbb;  --dp-scheduler-timeheader-horizontal-align: center;  --dp-scheduler-timeheader-padding: 0px;  --dp-scheduler-timeheader-vertical-align: center;'),e.add(".scheduler_default_main *, .scheduler_default_main *:before, .scheduler_default_main *:after","box-sizing: content-box;"),e.add(".scheduler_default_main, .scheduler_default_main svg text","box-sizing: content-box; border: 1px solid var(--dp-scheduler-border-color); font-family: var(--dp-scheduler-font-family); font-size: var(--dp-scheduler-font-size);"),e.add(".scheduler_default_selected .scheduler_default_event_inner","background: var(--dp-scheduler-event-selected-bg-color);"),e.add(".scheduler_default_timeheader_scroll","background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_message","opacity: 0.9; padding: var(--dp-scheduler-message-padding); color: var(--dp-scheduler-message-color); background: var(--dp-scheduler-message-bg-color);"),e.add(".scheduler_default_timeheadergroup,.scheduler_default_timeheadercol","color: var(--dp-scheduler-header-color); background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_rowheader,.scheduler_default_corner","color: var(--dp-scheduler-header-color); background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_rowheader.scheduler_default_rowheader_selected","background-color: #aaa; background-image: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_rowheader_inner","position: absolute; left: 0px; right: 0px; top: 0px; bottom: 0px; border-right: 1px solid var(--dp-scheduler-border-inner-color); padding: var(--dp-scheduler-rowheader-padding); display: flex; align-items: var(--dp-scheduler-rowheader-vertical-align);"),e.add(".scheduler_default_timeheadergroup_inner, .scheduler_default_timeheadercol_inner","position: absolute; left: 0; right: 0; top: 0; bottom: 0;  border-right: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_timeheadergroup_inner","border-bottom: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_timeheadergroup_inner, .scheduler_default_timeheadercol_inner, .scheduler_default_timeheader_float","display: flex; align-items: var(--dp-scheduler-timeheader-vertical-align); justify-content: var(--dp-scheduler-timeheader-horizontal-align); padding: var(--dp-scheduler-timeheader-padding);"),e.add(".scheduler_default_divider, .scheduler_default_splitter","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_divider_horizontal","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_matrix_vertical_line","background-color: var(--dp-scheduler-grid-line-color);"),e.add(".scheduler_default_matrix_vertical_break","background-color: var(--dp-scheduler-grid-line-break-color);"),e.add(".scheduler_default_matrix_horizontal_line","background-color: var(--dp-scheduler-grid-line-color);"),e.add(".scheduler_default_resourcedivider","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-scheduler-shadow-color); border: 1px solid var(--dp-scheduler-shadow-border-color); border-radius: var(--dp-scheduler-event-border-radius); opacity: 0.5; height: 100%;"),e.add(".scheduler_default_shadow","box-shadow: 0 2px 5px rgba(0,0,0,.2); border-radius: var(--dp-scheduler-event-border-radius);"),e.add(".scheduler_default_event","font-size: var(--dp-scheduler-font-size); color: var(--dp-scheduler-event-color); border-radius: var(--dp-scheduler-event-border-radius); box-shadow: var(--dp-scheduler-event-box-shadow);"),e.add(".scheduler_default_event_inner","position: absolute; top: 0px; left: 0px; right: 0px; bottom: 0px; border-radius: var(--dp-scheduler-event-border-radius); padding: var(--dp-scheduler-event-padding); overflow: hidden; border: var(--dp-scheduler-event-border); display: flex; align-items: var(--dp-scheduler-event-vertical-align); justify-content: var(--dp-scheduler-event-horizontal-align); background: var(--dp-scheduler-event-background);"),e.add(".scheduler_default_event_bar","display: var(--dp-scheduler-event-bar-display);top: var(--dp-scheduler-event-bar-top); left: var(--dp-scheduler-event-bar-left); right: var(--dp-scheduler-event-bar-right); height: var(--dp-scheduler-event-bar-height); background-color: var(--dp-scheduler-event-bar-bg-color);"),e.add(".scheduler_default_event_bar_inner","position:absolute; height: var(--dp-scheduler-event-bar-height); background-color: var(--dp-scheduler-event-bar-color);"),e.add(".scheduler_default_event_float","display: flex; align-items: center;"),e.add(".scheduler_default_event_float_inner","padding: var(--dp-scheduler-event-padding) var(--dp-scheduler-event-padding) var(--dp-scheduler-event-padding) 8px; position: relative;"),e.add(".scheduler_default_event_float_inner:after",'content:""; border-color: transparent #666 transparent transparent; border-style:solid; border-width:5px; width:0; height:0; position:absolute; top: calc(50% - 5px); left:-4px;'),e.add(".scheduler_default_event_focus","outline: var(--dp-scheduler-focus-outline-color) 2px solid; z-index: 100; opacity: 0.5;"),e.add(".scheduler_default_columnheader_inner","font-weight: bold;"),e.add(".scheduler_default_columnheader_splitter","box-sizing: border-box; border-right: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_columnheader_splitter:hover","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_columnheader_cell_inner","position: absolute; left: 0px; right: 0px; top: 0px; bottom: 0px; padding: 2px; display: flex; align-items: center;"),e.add(".scheduler_default_cell","background-color: var(--dp-scheduler-cell-bg-color);"),e.add(".scheduler_default_cell.scheduler_default_cell_business","background-color: var(--dp-scheduler-cell-business-bg-color);"),e.add(".scheduler_default_cell.scheduler_default_cell_business.scheduler_default_cell_selected, .scheduler_default_cell.scheduler_default_cell_selected","background-color: #ccc; background-image: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_tree_image_no_children",""),e.add(".scheduler_default_tree_image_expand","background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGQ9J00gMS41IDAuNSBMIDYuNSA1IEwgMS41IDkuNScgc3R5bGU9J2ZpbGw6bm9uZTtzdHJva2U6Izk5OTk5OTtzdHJva2Utd2lkdGg6MjtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLWxpbmVjYXA6YnV0dCcgLz48L3N2Zz4=);"),e.add(".scheduler_default_tree_image_collapse","background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMS41IEwgNSA2LjUgTCA5LjUgMS41JyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojOTk5OTk5O3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==);"),e.add(".scheduler_default_event_delete","background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTInIGhlaWdodD0nMTInIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMC41IEwgMTEuNSAxMS41IE0gMC41IDExLjUgTCAxMS41IDAuNScgc3R5bGU9J2ZpbGw6bm9uZTtzdHJva2U6IzQ2NDY0NjtzdHJva2Utd2lkdGg6MztzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLWxpbmVjYXA6YnV0dCcgLz48L3N2Zz4=) no-repeat center center; opacity: 0.6; cursor: pointer;"),e.add(".scheduler_default_event_delete:hover","opacity: 1;"),e.add(".scheduler_default_rowmove_handle","background-repeat: no-repeat; background-position: center center; background-color: #ccc; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAKCAYAAACT+/8OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAClJREFUGFdj+P//P4O9vX2Bg4NDP4gNFgBytgPxebgAMsYuQGMz/jMAAFsTZDPYJlDHAAAAAElFTkSuQmCC); cursor: move;"),e.add(".scheduler_default_rowmove_source","background-color: black; opacity: 0.2;"),e.add(".scheduler_default_rowmove_position_before, .scheduler_default_rowmove_position_after","background-color: #999; height: 2px;"),e.add(".scheduler_default_rowmove_position_child","margin-left: 20px; background-color: #999; height: 2px;"),e.add(".scheduler_default_rowmove_position_forbidden","background-color: #cc0000; height: 2px; margin-left: 20px;"),e.add(".scheduler_default_link_horizontal","border-bottom-style: solid; border-bottom-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_vertical","border-right-style: solid; border-right-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_arrow_right:before","content: ''; border-width: 6px; border-color: transparent transparent transparent var(--dp-scheduler-link-color); border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_left:before","content: ''; border-width: 6px; border-color: transparent var(--dp-scheduler-link-color) transparent transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_down:before","content: ''; border-width: 6px; border-color: var(--dp-scheduler-link-color) transparent transparent transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_up:before","content: ''; border-width: 6px; border-color: transparent transparent var(--dp-scheduler-link-color) transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_horizontal","border-bottom-color: #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_vertical","border-right-color: #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_right:before","border-color: transparent transparent transparent #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_left:before","border-color: transparent #aaaaaa transparent transparent;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_down:before","border-color: #aaaaaa transparent transparent transparent;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_up:before","border-color: transparent transparent #aaaaaa transparent;"),e.add(".scheduler_default_block","background-color: #808080; opacity: 0.5;"),e.add(".scheduler_default_main .scheduler_default_event_group","box-sizing: border-box; font-size: 13px; color: #666; padding: 2px; overflow:hidden; border:1px solid var(--dp-scheduler-event-border-color); background-color: #fff; display: flex; align-items: center; white-space: nowrap;"),e.add(".scheduler_default_main .scheduler_default_header_icon","box-sizing: border-box; border: 1px solid var(--dp-scheduler-border-color); background-color: var(--dp-scheduler-header-bg-color); color: var(--dp-scheduler-header-color);"),e.add(".scheduler_default_header_icon:hover","background-color: #ccc;"),e.add(".scheduler_default_header_icon_hide:before","content: '\\00AB';"),e.add(".scheduler_default_header_icon_show:before","content: '\\00BB';"),e.add(".scheduler_default_row_new .scheduler_default_rowheader_inner","padding-left: 10px; color: #666; cursor: text; background-position: 0px 50%; background-repeat: no-repeat; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABUSURBVChTY0ACslAaK2CC0iCQDMSlECYmQFYIAl1AjFUxukIQwKoYm0IQwFCMSyEIaEJpMMClcD4Qp0CYEIBNIUzRPzAPCtAVYlWEDgyAGIdTGBgAbqEJYyjqa3oAAAAASUVORK5CYII=);"),e.add(".scheduler_default_row_new .scheduler_default_rowheader_inner:hover","background: white; color: white;"),e.add(".scheduler_default_rowheader textarea","padding: 3px;"),e.add(".scheduler_default_rowheader_scroll","cursor: default; background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_shadow_forbidden .scheduler_default_shadow_inner, .scheduler_default_shadow_overlap .scheduler_default_shadow_inner","border: 1px solid #cc0000; background: #cc4125;"),e.add(".scheduler_default_event_moving_source","opacity: 0.5;"),e.add(".scheduler_default_linkpoint","background-color: white; border: 1px solid gray; border-radius: 5px;"),e.add(".scheduler_default_linkpoint.scheduler_default_linkpoint_hover","background-color: black;"),e.add(".scheduler_default_event.scheduler_default_event_version .scheduler_default_event_inner","overflow:hidden; background-color: #cfdde8; background-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_crosshair_vertical, .scheduler_default_crosshair_horizontal, .scheduler_default_crosshair_left, .scheduler_default_crosshair_top","background-color: gray; opacity: 0.2;"),e.add(".scheduler_default_link_dot","border-radius: 10px; background-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_task_milestone .scheduler_default_event_inner","position:absolute; top:16%; left:16%; right:16%; bottom:16%; background: var(--dp-scheduler-event-milestone-color); border: 0px none; transform: rotate(45deg); filter: none;"),e.add(".scheduler_default_event_left, .scheduler_default_event_right","white-space: nowrap; color: #666; cursor: default; display: flex; align-items: center;");e.add(".scheduler_default_main:focus","outline: none;");e.add(".scheduler_default_cell_focus","outline: var(--dp-scheduler-focus-outline-color) 2px solid; outline-offset: -2px; z-index: 100; opacity: 0.5;"),e.add(".scheduler_default_cell_focus.scheduler_default_cell_focus_top","border-top: 4px solid var(--dp-scheduler-focus-outline-color);"),e.add(".scheduler_default_cell_focus.scheduler_default_cell_focus_bottom","border-bottom: 4px solid var(--dp-scheduler-focus-outline-color);"),e.add(".scheduler_default_selectionrectangle","background-color: var(--dp-scheduler-selectionrectangle-color); border: 1px solid #000033; opacity: 0.4;"),e.add(".scheduler_default_link_shadow","border:1px solid black;"),e.add(".scheduler_default_link_shadow_circle","background-color:black;"),e.add(".scheduler_default_event_move_left","box-sizing: border-box; padding: 2px; border: 1px solid #ccc; background: #fff; background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); display: flex; align-items: center;"),e.add(".scheduler_default_event_move_right","box-sizing: border-box; padding: 2px; border: 1px solid #ccc; background: #fff; background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); display: flex; align-items: center;"),e.add(".scheduler_default_link_hover","box-shadow: 0px 0px 2px 2px rgba(255, 0, 0, 0.3)"),e.add(".scheduler_default_sorticon","opacity: 0.2;background-position: center center; background-repeat: no-repeat; cursor: pointer;"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_asc","background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBvbHlnb24gcG9pbnRzPSI1IDEuNSwgMTAgMTAsIDAgMTAiLz48L3N2Zz4=');"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_desc","background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBvbHlnb24gcG9pbnRzPSI1IDguNSwgMCAwLCAxMCAwIi8+PC9zdmc+');"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_active","opacity: 1;"),e.add(".scheduler_default_loading","background-color: orange; color: white; padding: 2px;"),e.add(".scheduler_default_link_curve","stroke: var(--dp-scheduler-link-color); fill: none; stroke-width: 2;"),e.add(".scheduler_default_link_curve:hover","stroke-opacity: 0.5;"),e.add(".scheduler_default_link_curve_dot","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_marker","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_text","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve","stroke: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_dot","fill: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_marker","fill: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_text","fill: #aaaaaa;"),e.commit(),DayPilot.Global.defaultCss=!0}}(),DayPilot.doc=function(){var e=document.documentElement;return e&&e.clientHeight?e:document.body},DayPilot.sh=function(e){return e?e.offsetHeight-e.clientHeight:0},DayPilot.guid=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return""+e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},DayPilot.ua=function(e){if(!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.ua() - array required");var t=[];return e.forEach(function(e){DayPilot.contains(t,e)||t.push(e)}),t},DayPilot.pageOffset=function(){if("undefined"!=typeof pageXOffset)return{x:pageXOffset,y:pageYOffset};var e=DayPilot.doc();return{x:e.scrollLeft,y:e.scrollTop}},DayPilot.indexOf=function(e,t){if(!e||!e.length)return-1;for(var a=0;a<e.length;a++)if(e[a]===t)return a;return-1},DayPilot.contains=function(e,t){if(2!==arguments.length)throw new DayPilot.Exception("DayPilot.contains() requires two arguments.");return!!e&&(e===t&&!DayPilot.isArray(e)||DayPilot.indexOf(e,t)!==-1)},DayPilot.ac=function(e,t){if(!t)var t=[];for(var a=0;e.children&&a<e.children.length;a++)t.push(e.children[a]),DayPilot.ac(e.children[a],t);return t},DayPilot.rfa=function(e,t){var a=DayPilot.indexOf(e,t);a!==-1&&e.splice(a,1)},DayPilot.mc=function(e){return e.pageX||e.pageY?{x:e.pageX,y:e.pageY}:{x:e.clientX+document.documentElement.scrollLeft,y:e.clientY+document.documentElement.scrollTop}},DayPilot.Stats={},DayPilot.Stats.eventObjects=0,DayPilot.Stats.dateObjects=0,DayPilot.Stats.cacheHitsCtor=0,DayPilot.Stats.cacheHitsParsing=0,DayPilot.Stats.cacheHitsTicks=0,DayPilot.re=function(e,t,a){a&&t&&e&&e.addEventListener(t,a,!1)},DayPilot.rePassive=function(e,t,a){a&&t&&e&&e.addEventListener(t,a,{"passive":!0})},DayPilot.reNonPassive=function(e,t,a){a&&t&&e&&e.addEventListener(t,a,{"passive":!1})},DayPilot.ue=function(e,t,a){e.removeEventListener(t,a,!1)},DayPilot.pu=function(e){var t,a,r,o=e.attributes;if(o)for(a=o.length,t=0;t<a;t+=1)o[t]&&(r=o[t].name,"function"==typeof e[r]&&(e[r]=null));if(o=e.childNodes)for(a=o.length,t=0;t<a;t+=1){DayPilot.pu(e.childNodes[t])}},DayPilot.de=function(e){if(e)if(DayPilot.isArray(e))for(var t=0;t<e.length;t++)DayPilot.de(e[t]);else e.parentNode&&e.parentNode.removeChild(e)},DayPilot.sw=function(e){return e?e.offsetWidth-e.clientWidth:0},DayPilot.am=function(){return"undefined"==typeof angular?null:(DayPilot.am.cached||(DayPilot.am.cached=angular.module("daypilot",[])),DayPilot.am.cached)},DayPilot.Selection=function(e,t,a,r){this.type="selection",this.start=e.isDayPilotDate?e:new DayPilot.Date(e),this.end=t.isDayPilotDate?t:new DayPilot.Date(t),this.resource=a,this.root=r,this.toJSON=function(e){var t={};return t.start=this.start,t.end=this.end,t.resource=this.resource,t}},DayPilot.request=function(e,t,a,r){var o=DayPilot.createXmlHttp();o&&(o.open("POST",e,!0),o.setRequestHeader("Content-type","text/plain"),o.onreadystatechange=function(){if(4===o.readyState)return 200!==o.status&&304!==o.status?void(r?r(o):window.console&&console.log("HTTP error "+o.status)):void t(o)},4!==o.readyState&&("object"==typeof a&&(a=JSON.stringify(a)),o.send(a)))},DayPilot.ajax=function(e){if(!e)throw new DayPilot.Exception("Parameter object required.");if("string"!=typeof e.url)throw new DayPilot.Exception("The parameter object must have 'url' property.");var t=DayPilot.createXmlHttp();if(!t)throw new DayPilot.Exception("Unable to create XMLHttpRequest object");var a="object"==typeof e.data,r=e.data,o=e.method||(e.data?"POST":"GET"),n=e.success||function(){},d=e.error||function(){},i=e.url,l=e.contentType||(a?"application/json":"text/plain"),s=e.headers||{};t.open(o,i,!0),t.setRequestHeader("Content-type",l),DayPilot.Util.ownPropsAsArray(s).forEach(function(e){t.setRequestHeader(e.key,e.val)}),t.onreadystatechange=function(){if(4===t.readyState)if(200===t.status||201===t.status||204===t.status||304===t.status){var e={};e.request=t,t.responseText&&(e.data=JSON.parse(t.responseText)),n(e)}else if(d){var e={};e.request=t,d(e)}else window.console&&console.log("HTTP error "+t.status)},4!==t.readyState&&(a&&(r=JSON.stringify(r)),t.send(r))},DayPilot.createXmlHttp=function(){return new XMLHttpRequest},DayPilot.Http={},DayPilot.Http.ajax=function(e){DayPilot.ajax(e)},DayPilot.Http.get=function(e,t){return t=t||{},new Promise(function(a,r){var o={};o.url=e,o.method="GET",o.success=function(e){a(e)},o.error=function(e){r(e)},o.contentType=t.contentType,o.headers=t.headers,DayPilot.ajax(o)})},DayPilot.Http.post=function(e,t,a){return a=a||{},new Promise(function(r,o){var n={};n.url=e,n.method="POST",n.data=t,n.success=function(e){r(e)},n.error=function(e){o(e)},n.contentType=a.contentType,n.headers=a.headers,DayPilot.ajax(n)})},DayPilot.Http.put=function(e,t,a){return a=a||{},new Promise(function(r,o){var n={};n.url=e,n.method="PUT",n.data=t,n.success=function(e){r(e)},n.error=function(e){o(e)},n.contentType=a.contentType,n.headers=a.headers,DayPilot.ajax(n)})},DayPilot.Http.delete=function(e,t){return t=t||{},new Promise(function(a,r){var o={};o.url=e,o.method="DELETE",o.success=function(e){a(e)},o.error=function(e){r(e)},o.contentType=t.contentType,o.headers=t.headers,DayPilot.ajax(o)})},DayPilot.Util={},DayPilot.Util.addClass=function(e,t){if(e){if(!e.className)return void(e.className=t);new RegExp("(^|\\s)"+t+"($|\\s)").test(e.className)||(e.className=e.className+" "+t)}},DayPilot.Util.removeClass=function(e,t){if(e){var a=new RegExp("(^|\\s)"+t+"($|\\s)");e.className=e.className.replace(a," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}},DayPilot.Util.copyProps=function(e,t,a){if(t||(t={}),!e)return t;if("undefined"==typeof a)for(var r in e)e.hasOwnProperty(r)&&"undefined"!=typeof e[r]&&(t[r]=e[r]);else for(var o=0;o<a.length;o++){var r=a[o];"undefined"!=typeof e[r]&&(t[r]=e[r])}return t},DayPilot.Util.ownPropsAsArray=function(e){var t=[];if(!e)return t;for(var a in e)if(e.hasOwnProperty(a)){var r={};r.key=a,r.val=e[a],t.push(r)}return t},DayPilot.Util.atLeast=function(e,t){return Math.max(e,t)},DayPilot.Util.mouseButton=function(e){var t={};switch(e.button){case 0:t.left=!0;break;case 1:t.middle=!0;break;case 2:t.right=!0}return t},DayPilot.Util.replaceCharAt=function(e,t,a){return e.substr(0,t)+a+e.substr(t+a.length)},DayPilot.Util.isNullOrUndefined=function(e){return null===e||"undefined"==typeof e},DayPilot.Util.escapeHtml=function(e){var t=document.createElement("div");return t.innerText=e,t.innerHTML},DayPilot.Util.escapeTextHtml=function(e,t){return DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":DayPilot.Util.escapeHtml(e):t},DayPilot.Util.isSameEvent=function(e,t){return!(!e||!t)&&(e=e instanceof DayPilot.Event?e.data:e,t=t instanceof DayPilot.Event?t.data:t,e===t||!DayPilot.Util.isNullOrUndefined(e.id)&&e.id===t.id)},DayPilot.Util.overlaps=function(e,t,a,r){return!(t<=a||e>=r)},DayPilot.Util.isVueVNode=function(e){return!!e&&(DayPilot.isArray(e)?DayPilot.Util.isVueVNode(e[0]):e["__v_isVNode"])},DayPilot.Util.isMouseEvent=function(e){return!!e&&("mouse"===e.pointerType||e instanceof MouseEvent)},DayPilot.Areas={},DayPilot.Areas.attach=function(e,t,a){var a=a||{},o=a.areas,n=a.allowed||function(){return!0},d=a.offsetX||0;o=r(t,o),o&&DayPilot.isArray(o)&&0!==o.length&&(DayPilot.re(e,"mousemove",function(r){e.active||e.areasDisabled||!n()||DayPilot.Areas.showAreas(e,t,r,o,{"offsetX":d,"eventDiv":a.eventDiv})}),DayPilot.re(e,"mouseleave",function(t){DayPilot.Areas.hideAreas(e,t)}),o.forEach(function(r){if(DayPilot.Areas.isVisible(r)){var o=DayPilot.Areas.createArea(e,t,r,{"offsetX":d,"eventDiv":a.eventDiv});e.appendChild(o)}}))},DayPilot.Areas.disable=function(e){e.areasDisabled=!0,Array.from(e.childNodes).filter(function(e){return e.isActiveArea&&!e.area.start}).forEach(function(e){e.c=e.style.display,e.style.display="none"})},DayPilot.Areas.enable=function(e){e.areasDisabled=!1,Array.from(e.childNodes).filter(function(e){return e.isActiveArea&&!e.area.start}).forEach(function(e){e.c?e.style.display=e.c:e.style.display=""})},DayPilot.Areas.remove=function(e){var t=Array.from(e.childNodes).filter(function(e){return e.isActiveArea});DayPilot.de(t)},DayPilot.Areas.isVisible=function(e){var t=e.visibility||e.v||"Visible";return"Visible"===t||"TouchVisible"===t&&!DayPilot.browser.hover},DayPilot.Areas.copy=function(e){return DayPilot.isArray(e)?e.map(function(e){return DayPilot.Util.copyProps(e,{})}):[]};var r=function(e,t){return DayPilot.isArray(t)||(t=e.areas,t||(e.cache?t=e.cache.areas:e.data&&(t=e.data.areas))),t};DayPilot.Areas.showAreas=function(e,t,a,r,o){if(!DayPilot.Global.resizing&&!DayPilot.Global.moving&&!DayPilot.Global.selecting&&!e.active&&DayPilot.browser.hover){if(DayPilot.Areas.all&&DayPilot.Areas.all.length>0)for(var n=0;n<DayPilot.Areas.all.length;n++){var d=DayPilot.Areas.all[n];d!==e&&DayPilot.Areas.hideAreas(d,a)}if(e.active={},DayPilot.isArray(r)||(r=t.areas,r||(t.cache?r=t.cache.areas:t.data&&(r=t.data.areas))),r&&0!==r.length&&!(e.areas&&e.areas.length>0)){e.areas=[];for(var n=0;n<r.length;n++){var i=r[n];if(!DayPilot.Areas.isVisible(i)){var l=DayPilot.Areas.createArea(e,t,i,o);e.areas.push(l),e.appendChild(l),DayPilot.Areas.all.push(e)}}e.active.children=DayPilot.ac(e)}}},DayPilot.Areas.createArea=function(e,t,a,r){function o(e,t,a){var r={};return r.area=e,r.source=t,r.originalEvent=a,r.preventDefault=function(){r.preventDefault.value=!0},"function"==typeof e.onClick&&e.onClick(r),r}function n(e,t,a){DayPilot.Bubble&&DayPilot.Bubble.touchPosition(a),e.calendar.bubble&&e.calendar.bubble.showEvent(e,!0)}function d(e,t,a,r){DayPilot.Menu&&DayPilot.Menu.touchPosition(r);var o=a.contextMenu||a.menu;if(o instanceof DayPilot.Menu||(t.isEvent&&t.client.contextMenu()?o=t.client.contextMenu():t.isEvent&&t.calendar.contextMenu&&(o=t.calendar.contextMenu)),o&&o.show){var n={"type":"area","div":e,"e":t,"area":a,"a":s};o.show(t,{"initiator":n})}}function i(e){return"string"==typeof e&&isNaN(e)?e:"undefined"!=typeof e?e+"px":void 0}var r=r||{},l=(r.offsetX||0,r.eventDiv||e),s=document.createElement("div");s.isActiveArea=!0,s.area=a,s.setAttribute("unselectable","on");var c=a.w||a.width,u=a.h||a.height,f=a.cssClass||a.css||a.className;if("undefined"!=typeof a.style&&s.setAttribute("style",a.style),s.style.position="absolute",s.style.width=i(c),s.style.height=i(u),s.style.right=i(a.right),s.style.top=i(a.top),s.style.left=i(a.left),s.style.bottom=i(a.bottom),s.style.borderRadius=i(a.borderRadius),"undefined"!=typeof a.html||"undefined"!=typeof a.text)s.innerHTML=DayPilot.Util.escapeTextHtml(a.text,a.html);else if(a.icon){var h=document.createElement("i");h.className=a.icon,s.appendChild(h)}else if(a.image){var p=document.createElement("img");p.src=a.image,s.appendChild(p)}else if(a.symbol){var m="http://www.w3.org/2000/svg",y=document.createElementNS(m,"svg");y.setAttribute("width","100%"),y.setAttribute("height","100%");var b=document.createElementNS(m,"use");b.setAttribute("href",a.symbol),y.appendChild(b),s.appendChild(y)}if(f&&(s.className=f),a.toolTip&&s.setAttribute("title",a.toolTip),a.backColor&&(s.style.background=a.backColor),a.background&&(s.style.background=a.background),a.fontColor&&(s.style.color=a.fontColor),a.padding&&(s.style.padding=a.padding+"px",s.style.boxSizing="border-box"),a.verticalAlignment)switch(s.style.display="flex",a.verticalAlignment){case"center":s.style.alignItems="center";break;case"top":s.style.alignItems="flex-start";break;case"bottom":s.style.alignItems="flex-end"}if(a.horizontalAlignment)switch(s.style.display="flex",a.horizontalAlignment){case"right":s.style.justifyContent="flex-end";break;case"left":s.style.justifyContent="flex-start";break;case"center":s.style.justifyContent="center"}if("ResizeEnd"===a.action||"ResizeStart"===a.action||"Move"===a.action){if(t.calendar.isCalendar)switch(a.action){case"ResizeEnd":a.cursor="s-resize",a.dpBorder="bottom";break;case"ResizeStart":a.cursor="n-resize",a.dpBorder="top";break;case"Move":a.cursor="move"}if(t.calendar.isScheduler||t.calendar.isMonth)switch(a.action){case"ResizeEnd":a.cursor="e-resize",a.dpBorder="right";break;case"ResizeStart":a.cursor="w-resize",a.dpBorder="left";break;case"Move":a.cursor="move"}s.onmousemove=function(e,t,a){return function(r){t.calendar.internal&&t.calendar.internal.dragInProgress&&t.calendar.internal.dragInProgress()||(r.cancelBubble=!0,e.style.cursor=a.cursor,a.dpBorder&&(e.dpBorder=a.dpBorder))}}(l,t,a),s.onmouseout=function(e,t,a){return function(t){e.style.cursor=""}}(l,t,a)}if(("ResizeEnd"===a.action||"ResizeStart"===a.action)&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,a){return function(r){r.cancelBubble=!0;var o=t.calendar.internal.touch,n=r.touches?r.touches[0]:r,d={x:n.pageX,y:n.pageY};t.calendar.coords=o.relativeCoords(r),o.preventEventTap=!0,t.calendar.isScheduler?o.startResizing(e,"ResizeEnd"===a.action?"right":"left"):t.calendar.isCalendar&&o.startResizing(e,"ResizeEnd"===a.action?"bottom":"top",d)}}(l,t,a);DayPilot.rePassive(s,DayPilot.touch.start,g)}if("ContextMenu"===a.action&&t.isEvent&&t.calendar.internal.touch){
var g=function(e,t,a){return function(r){r.cancelBubble=!0,r.preventDefault(),d(e,t,a,r),t.calendar.internal.touch.preventEventTap=!0}}(l,t,a),v=function(e,t,a){return function(e){e.cancelBubble=!0,e.preventDefault()}}(l,t,a);DayPilot.reNonPassive(s,DayPilot.touch.start,g),DayPilot.reNonPassive(s,DayPilot.touch.end,v)}if("Bubble"===a.action&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,a){return function(e){e.cancelBubble=!0,e.preventDefault();var r=o(a,t,e);if(!r.preventDefault.value){n(t,a,e);t.calendar.internal.touch.preventEventTap=!0,"function"==typeof a.onClicked&&a.onClicked(r)}}}(l,t,a),v=function(e,t,a){return function(e){e.cancelBubble=!0,e.preventDefault()}}(l,t,a);DayPilot.reNonPassive(s,DayPilot.touch.start,g),DayPilot.reNonPassive(s,DayPilot.touch.end,v)}if("Move"===a.action&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,a){return function(a){a.cancelBubble=!0;var r=t.calendar.internal.touch,o=a.touches?a.touches[0]:a,n={x:o.pageX,y:o.pageY};t.calendar.coords=r.relativeCoords(a),DayPilot.Global&&DayPilot.Global.touch&&(DayPilot.Global.touch.active=!0),r.preventEventTap=!0,r.startMoving(e,n)}}(l,t,a);DayPilot.rePassive(s,DayPilot.touch.start,g)}if("Bubble"===a.action&&t.isEvent?(s.onmousemove=function(e,t,a){return function(e){a.bubble?a.bubble.showEvent(t,!0):t.calendar.bubble&&t.calendar.bubble.showEvent(t,!0)}}(e,t,a),s.onmouseout=function(e,t,a){return function(e){"undefined"!=typeof DayPilot.Bubble&&(a.bubble?a.bubble.hideOnMouseOut():t.calendar.bubble&&t.calendar.bubble.hideOnMouseOut())}}(e,t,a)):"Bubble"===a.action&&t.isRow?(s.onmousemove=function(e,t,a){return function(e){a.bubble?a.bubble.showResource(t,!0):t.calendar.resourceBubble&&t.calendar.resourceBubble.showResource(t,!0)}}(e,t,a),s.onmouseout=function(e,t,a){return function(e){"undefined"!=typeof DayPilot.Bubble&&(a.bubble?a.bubble.hideOnMouseOut():t.calendar.resourceBubble&&t.calendar.resourceBubble.hideOnMouseOut())}}(e,t,a)):"Bubble"===a.action&&"undefined"!=typeof DayPilot.Bubble&&a.bubble instanceof DayPilot.Bubble&&(s.onmousemove=function(e,t,a){return function(e){a.bubble.showHtml(null,null)}}(e,t,a),s.onmouseout=function(e,t,a){return function(e){"undefined"!=typeof DayPilot.Bubble&&a.bubble&&a.bubble.hideOnMouseOut()}}(e,t,a)),"HoverMenu"===a.action&&(s.onmousemove=function(e,t,a){return function(e){var r=a.menu;r&&r.show&&(r.visible?r.source&&"undefined"!=typeof r.source.id&&r.source.id!==t.id&&r.show(t):r.show(t),r.cancelHideTimeout())}}(e,t,a),s.onmouseout=function(e,t,a){return function(e){var t=a.menu;t&&t.hideOnMouseOver&&t.delayedHide()}}(e,t,a)),"None"===a.action){var g=function(e,t,a){return function(e){var r=o(a,t,e);"function"==typeof a.onClicked&&a.onClicked(r),e.preventDefault(),e.stopPropagation()}}(l,t,a);DayPilot.reNonPassive(s,DayPilot.touch.start,g)}return s.onmousedown=function(e,t,a){return function(r){if("function"==typeof a.onmousedown&&a.onmousedown(r),"function"==typeof a.mousedown){var o={};o.area=a,o.div=e,o.originalEvent=r,o.source=t,a.mousedown(o)}if("Move"===a.action&&t.isRow){var n=t.$.row;(0,t.calendar.internal.rowStartMoving)(n)}"undefined"!=typeof DayPilot.Bubble&&DayPilot.Bubble.hideActive(),"Move"===a.action&&(DayPilot.Global.movingAreaData=a.data),"Move"===a.action&&t.isEvent&&t.calendar.internal&&t.calendar.internal.startMoving&&t.calendar.internal.startMoving(e,r);"Move"!==a.action&&"ResizeEnd"!==a.action&&"ResizeStart"!==a.action&&a.action&&"Default"!==a.action&&(r.preventDefault(),r.cancelBubble=!0)}}(e,t,a),s.onclick=function(e,t,a){return function(r){var n=o(a,t,r);if(!n.preventDefault.value){switch(a.action){case"ContextMenu":d(e,t,a,r),r.cancelBubble=!0;break;case"None":r.cancelBubble=!0}"function"==typeof a.onClicked&&a.onClicked(n)}}}(e,t,a),"function"==typeof a.onMouseEnter&&s.addEventListener("mouseenter",function(e,t,a){return function(e){var r={};r.area=a,r.source=t,r.originalEvent=e,a.onMouseEnter(r)}}(e,t,a)),"function"==typeof a.onMouseLeave&&s.addEventListener("mouseleave",function(e,t,a){return function(e){var r={};r.area=a,r.source=t,r.originalEvent=e,a.onMouseLeave(r)}}(e,t,a)),s},DayPilot.Areas.all=[],DayPilot.Areas.hideAreas=function(e,t){if(e&&e&&e.active){var a=e.active,r=e.areas;if(a&&a.children&&t){var o=t.toElement||t.relatedTarget;if(~DayPilot.indexOf(a.children,o))return}if(!r||0===r.length)return void(e.active=null);DayPilot.de(r),e.active=null,e.areas=[],DayPilot.rfa(DayPilot.Areas.all,e),a.children=null}},DayPilot.Areas.hideAll=function(e){if(DayPilot.Areas.all&&0!==DayPilot.Areas.all.length)for(var t=0;t<DayPilot.Areas.all.length;t++)DayPilot.Areas.hideAreas(DayPilot.Areas.all[t],e)},DayPilot.Exception=function(e){return new Error(e)},DayPilot.Locale=function(e,t){if(this.id=e,this.dayNames=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],this.dayNamesShort=["Su","Mo","Tu","We","Th","Fr","Sa"],this.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"],this.datePattern="M/d/yyyy",this.timePattern="H:mm",this.dateTimePattern="M/d/yyyy H:mm",this.timeFormat="Clock12Hours",this.weekStarts=0,t)for(var a in t)this[a]=t[a]},DayPilot.Locale.all={},DayPilot.Locale.find=function(e){if(!e)return null;var t=e.toLowerCase();return t.length>2&&(t=DayPilot.Util.replaceCharAt(t,2,"-")),DayPilot.Locale.all[t]},DayPilot.Locale.register=function(e){DayPilot.Locale.all[e.id]=e},DayPilot.Locale.register(new DayPilot.Locale("ca-es",{"dayNames":["diumenge","dilluns","dimarts","dimecres","dijous","divendres","dissabte"],"dayNamesShort":["dg","dl","dt","dc","dj","dv","ds"],"monthNames":["gener","febrer","març","abril","maig","juny","juliol","agost","setembre","octubre","novembre","desembre",""],"monthNamesShort":["gen.","febr.","març","abr.","maig","juny","jul.","ag.","set.","oct.","nov.","des.",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("cs-cz",{"dayNames":["neděle","pondělí","úterý","středa","čtvrtek","pátek","sobota"],"dayNamesShort":["ne","po","út","st","čt","pá","so"],"monthNames":["leden","únor","březen","duben","květen","červen","červenec","srpen","září","říjen","listopad","prosinec",""],"monthNamesShort":["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII",""],"timePattern":"H:mm","datePattern":"d. M. yyyy","dateTimePattern":"d. M. yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("da-dk",{"dayNames":["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],"dayNamesShort":["sø","ma","ti","on","to","fr","lø"],"monthNames":["januar","februar","marts","april","maj","juni","juli","august","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"dd-MM-yyyy","dateTimePattern":"dd-MM-yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-at",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Jänner","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jän","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-ch",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-de",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-lu",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-au",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"d/MM/yyyy","dateTimePattern":"d/MM/yyyy h:mm tt","timeFormat":"Clock12Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-ca",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd h:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("en-gb",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-us",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"M/d/yyyy","dateTimePattern":"M/d/yyyy h:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("es-es",{"dayNames":["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],"dayNamesShort":["D","L","M","X","J","V","S"],"monthNames":["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre",""],"monthNamesShort":["ene.","feb.","mar.","abr.","may.","jun.","jul.","ago.","sep.","oct.","nov.","dic.",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("es-mx",{"dayNames":["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],"dayNamesShort":["do.","lu.","ma.","mi.","ju.","vi.","sá."],"monthNames":["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre",""],"monthNamesShort":["ene.","feb.","mar.","abr.","may.","jun.","jul.","ago.","sep.","oct.","nov.","dic.",""],"timePattern":"hh:mm tt","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy hh:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("eu-es",{"dayNames":["igandea","astelehena","asteartea","asteazkena","osteguna","ostirala","larunbata"],"dayNamesShort":["ig","al","as","az","og","or","lr"],"monthNames":["urtarrila","otsaila","martxoa","apirila","maiatza","ekaina","uztaila","abuztua","iraila","urria","azaroa","abendua",""],"monthNamesShort":["urt.","ots.","mar.","api.","mai.","eka.","uzt.","abu.","ira.","urr.","aza.","abe.",""],"timePattern":"H:mm","datePattern":"yyyy/MM/dd","dateTimePattern":"yyyy/MM/dd H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fi-fi",{"dayNames":["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"],"dayNamesShort":["su","ma","ti","ke","to","pe","la"],"monthNames":["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kesäkuu","heinäkuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu",""],"monthNamesShort":["tammi","helmi","maalis","huhti","touko","kesä","heinä","elo","syys","loka","marras","joulu",""],"timePattern":"H:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-be",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd-MM-yy","dateTimePattern":"dd-MM-yy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-ca",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("fr-ch",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-fr",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-lu",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("gl-es",{"dayNames":["domingo","luns","martes","mércores","xoves","venres","sábado"],"dayNamesShort":["do","lu","ma","mé","xo","ve","sá"],"monthNames":["xaneiro","febreiro","marzo","abril","maio","xuño","xullo","agosto","setembro","outubro","novembro","decembro",""],"monthNamesShort":["xan","feb","mar","abr","maio","xuño","xul","ago","set","out","nov","dec",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("it-it",{"dayNames":["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],"dayNamesShort":["do","lu","ma","me","gi","ve","sa"],"monthNames":["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre",""],"monthNamesShort":["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("it-ch",{"dayNames":["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],"dayNamesShort":["do","lu","ma","me","gi","ve","sa"],"monthNames":["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre",""],"monthNamesShort":["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("ja-jp",{"dayNames":["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"],"dayNamesShort":["日","月","火","水","木","金","土"],"monthNames":["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"H:mm","datePattern":"yyyy/MM/dd","dateTimePattern":"yyyy/MM/dd H:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("ko-kr",{"dayNames":["일요일","월요일","화요일","수요일","목요일","금요일","토요일"],"dayNamesShort":["일","월","화","수","목","금","토"],"monthNames":["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"tt h:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd tt h:mm","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("nb-no",{"dayNames":["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],"dayNamesShort":["sø","ma","ti","on","to","fr","lø"],"monthNames":["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember",""],"monthNamesShort":["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nl-nl",{"dayNames":["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],"dayNamesShort":["zo","ma","di","wo","do","vr","za"],"monthNames":["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"d-M-yyyy","dateTimePattern":"d-M-yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nl-be",{"dayNames":["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],"dayNamesShort":["zo","ma","di","wo","do","vr","za"],"monthNames":["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"H:mm","datePattern":"d/MM/yyyy","dateTimePattern":"d/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nn-no",{"dayNames":["søndag","måndag","tysdag","onsdag","torsdag","fredag","laurdag"],"dayNamesShort":["sø","må","ty","on","to","fr","la"],"monthNames":["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember",""],"monthNamesShort":["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("pt-br",{"dayNames":["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],"dayNamesShort":["D","S","T","Q","Q","S","S"],"monthNames":["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro",""],"monthNamesShort":["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("pl-pl",{"dayNames":["niedziela","poniedziałek","wtorek","środa","czwartek","piątek","sobota"],"dayNamesShort":["N","Pn","Wt","Śr","Cz","Pt","So"],"monthNames":["styczeń","luty","marzec","kwiecień","maj","czerwiec","lipiec","sierpień","wrzesień","październik","listopad","grudzień",""],"monthNamesShort":["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","paź","lis","gru",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("pt-pt",{"dayNames":["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],"dayNamesShort":["D","S","T","Q","Q","S","S"],"monthNames":["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro",""],"monthNamesShort":["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("ro-ro",{"dayNames":["duminică","luni","marți","miercuri","joi","vineri","sâmbătă"],"dayNamesShort":["D","L","Ma","Mi","J","V","S"],"monthNames":["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie",""],"monthNamesShort":["ian.","feb.","mar.","apr.","mai.","iun.","iul.","aug.","sep.","oct.","nov.","dec.",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("ru-ru",{"dayNames":["воскресенье","понедельник","вторник","среда","четверг","пятница","суббота"],"dayNamesShort":["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],"monthNames":["Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь",""],"monthNamesShort":["янв","фев","мар","апр","май","июн","июл","авг","сен","окт","ноя","дек",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("sk-sk",{"dayNames":["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"],"dayNamesShort":["ne","po","ut","st","št","pi","so"],"monthNames":["január","február","marec","apríl","máj","jún","júl","august","september","október","november","december",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"H:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("sv-se",{"dayNames":["söndag","måndag","tisdag","onsdag","torsdag","fredag","lördag"],"dayNamesShort":["sö","må","ti","on","to","fr","lö"],"monthNames":["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("tr-tr",{"dayNames":["Pazar","Pazartesi","Salı","Çarşamba","Perşembe","Cuma","Cumartesi"],"dayNamesShort":["Pz","Pt","Sa","Ça","Pe","Cu","Ct"],"monthNames":["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık",""],"monthNamesShort":["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara",""],"timePattern":"HH:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("uk-ua",{"dayNames":["неділя","понеділок","вівторок","середа","четвер","п'ятниця","субота"],"dayNamesShort":["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],"monthNames":["січень","лютий","березень","квітень","травень","червень","липень","серпень","вересень","жовтень","листопад","грудень",""],"monthNamesShort":["Січ","Лют","Бер","Кві","Тра","Чер","Лип","Сер","Вер","Жов","Лис","Гру",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("zh-cn",{"dayNames":["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],"dayNamesShort":["日","一","二","三","四","五","六"],"monthNames":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"monthNamesShort":["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月",""],"timePattern":"H:mm","datePattern":"yyyy/M/d","dateTimePattern":"yyyy/M/d H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("zh-tw",{"dayNames":["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],"dayNamesShort":["日","一","二","三","四","五","六"],"monthNames":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"monthNamesShort":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"timePattern":"tt hh:mm","datePattern":"yyyy/M/d","dateTimePattern":"yyyy/M/d tt hh:mm","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.US=DayPilot.Locale.find("en-us"),DayPilot.Switcher=function(e){function t(e,t,r){var o={};o.start=e,o.end=t,o.day=r,o.target=a.k.control,o.preventDefault=function(){this.preventDefault.value=!0};var n=a.H;n&&n.start===o.start&&n.end===o.end&&n.day===o.day&&n.target===o.target||(a.H=o,"function"==typeof a.onChange&&(a.onChange(o),o.preventDefault.value)||"function"==typeof a.onTimeRangeSelect&&(a.onTimeRangeSelect(o),o.preventDefault.value)||(a.k.s(a.l),"function"==typeof a.onChanged&&a.onChanged(o),"function"==typeof a.onTimeRangeSelected&&a.onTimeRangeSelected(o)))}var a=this;this.f=[],this.i=[],this.j={},this.selectedClass=null,this.syncScrollbar=!0,this.k=null,this.l=DayPilot.Date.today(),this.m=null,this.onChange=null,this.onChanged=null,this.onSelect=null,this.j.updateMode=function(e){var t=a.j.control;t&&(t.selectMode=e,t.select(a.l))},this.addView=function(e,t){var r;if("string"==typeof e){if(r=document.getElementById(e),!r)throw"Element not found: "+e}else r=e;var o=r,n={};return n.n=!0,n.o=o.id,n.control=o,n.p=t||{},n.q=function(){o.hide?o.hide():o.nav&&o.nav.top?o.nav.top.style.display="none":o.style.display="none"},n.s=function(e){(function(){return!!o.backendUrl||!("function"!=typeof WebForm_DoCallback||!o.uniqueID)})()?o.commandCallBack&&o.commandCallBack("navigate",{"day":e}):(o.startDate=e,o.update())},n.t=function(){a.u(),o.show?o.show():o.nav&&o.nav.top?o.nav.top.style.display="":o.style.display=""},n.z=function(){if(n.p.navigatorSelectMode)return n.p.navigatorSelectMode;if(o.isCalendar)switch(o.viewType){case"Day":return"day";case"Week":return"week";case"WorkWeek":return"week";default:return"day"}else if(o.isMonth)switch(o.viewType){case"Month":return"month";case"Weeks":return"week";default:return"day"}return"day"},this.f.push(n),n},this.addTrigger=function(e,t){var r;if("string"==typeof e){if(r=document.getElementById(e),!r)throw"Element not found: "+e}else r=e;var o=this.A(t);o||(o=this.addView(t));var n={};return n.B=!0,n.C=r,n.o=r.id,n.D=o,n.E=function(e){a.show(n),a.F(n),e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},DayPilot.re(r,"click",n.E),this.i.push(n),n},this.addButton=this.addTrigger,this.select=function(e){var t=this.G(e);t?t.E():this.i.length>0&&this.i[0].E()},this.G=function(e){for(var t=0;t<this.i.length;t++){var a=this.i[t];if(a.o===e)return a}return null},this.F=function(e){if(this.selectedClass){for(var t=0;t<this.i.length;t++){var a=this.i[t];DayPilot.Util.removeClass(a.C,this.selectedClass)}DayPilot.Util.addClass(e.C,this.selectedClass)}},this.addNavigator=function(e){a.j.control=e,e.timeRangeSelectedHandling="JavaScript",e.onTimeRangeSelected=function(){var r,o,n;if(1===e.api)r=arguments[0],o=arguments[1],n=arguments[2];else{var d=arguments[0];r=d.start,o=d.end,n=d.day}a.l=n,t(r,o,n)}},this.show=function(e){var r,o;if(e.B)o=e,r=o.D;else if(r=e.n?e:this.A(e),this.k===r)return;if(a.onSelect){var n={};n.source=o?o.C:null,n.target=r.control,a.onSelect(n)}if(a.syncScrollbar){var d=this.k&&this.k.control;d&&d.isCalendar&&(a.m=d.getScrollY())}this.k=r,r.t(),null!==a.m&&r.control.isCalendar&&r.control.setScrollY(a.m);var i=r.z();a.j.updateMode(i),t(a.j.control.selectionStart,a.j.control.selectionEnd.addDays(1),a.j.control.selectionDay)},this.A=function(e){for(var t=0;t<this.f.length;t++)if(this.f[t].control===e)return this.f[t];return null},this.u=function(){for(var e=0;e<this.f.length;e++)this.f[e].q()},Object.defineProperty(this,"active",{get:function(){return a.k}}),this.events={},this.events.load=function(e,t,r){if(!a.k||!a.k.control)throw"DayPilot.Switcher.events.load(): Active view not found";a.k.control.events.load(e,t,r)},this.H=null,this.I=function(){if(e)for(var t in e)if("triggers"===t){var r=e.triggers||[];r.forEach(function(e){a.addTrigger(e.id,e.view)})}else"navigator"===t?a.addNavigator(e.navigator):a[t]=e[t]},this.I()},DayPilot.Duration=function(e){var t=this,a=864e5,r=36e5,o=6e4,n=1e3;if(2===arguments.length){var d=arguments[0],i=arguments[1];if(!(d instanceof DayPilot.Date)&&"string"!=typeof d)throw"DayPilot.Duration(): Invalid start argument, DayPilot.Date expected";if(!(i instanceof DayPilot.Date)&&"string"!=typeof i)throw"DayPilot.Duration(): Invalid end argument, DayPilot.Date expected";"string"==typeof d&&(d=new DayPilot.Date(d)),"string"==typeof i&&(i=new DayPilot.Date(i)),e=i.getTime()-d.getTime()}return this.ticks=e,DayPilot.Date.Cache.DurationCtor[""+e]?DayPilot.Date.Cache.DurationCtor[""+e]:(DayPilot.Date.Cache.DurationCtor[""+e]=this,this.toString=function(e){if(!e)return t.days()+"."+t.hours()+":"+t.minutes()+":"+t.seconds()+"."+t.milliseconds();var a=t.minutes();a=(a<10?"0":"")+a;var r=e;return r=r.replace("mm",a),r=r.replace("m",t.minutes()),r=r.replace("H",t.hours()),r=r.replace("h",t.hours()),r=r.replace("d",t.days()),r=r.replace("s",t.seconds())},this.totalHours=function(){return t.ticks/r},this.totalDays=function(){return t.ticks/a},this.totalMinutes=function(){return t.ticks/o},this.totalSeconds=function(){return t.ticks/n},this.days=function(){
return Math.floor(t.totalDays())},this.hours=function(){var e=t.ticks-t.days()*a;return Math.floor(e/r)},this.minutes=function(){var e=t.ticks-Math.floor(t.totalHours())*r;return Math.floor(e/o)},this.seconds=function(){var e=t.ticks-Math.floor(t.totalMinutes())*o;return Math.floor(e/n)},void(this.milliseconds=function(){return t.ticks%n}))},DayPilot.Duration.weeks=function(e){return new DayPilot.Duration(1e3*e*60*60*24*7)},DayPilot.Duration.days=function(e){return new DayPilot.Duration(1e3*e*60*60*24)},DayPilot.Duration.hours=function(e){return new DayPilot.Duration(1e3*e*60*60)},DayPilot.Duration.minutes=function(e){return new DayPilot.Duration(1e3*e*60)},DayPilot.Duration.seconds=function(e){return new DayPilot.Duration(1e3*e)},DayPilot.TimeSpan=function(){throw"Please use DayPilot.Duration class instead of DayPilot.TimeSpan."};try{DayPilot.TimeSpan.prototype=Object.create(DayPilot.Duration.prototype)}catch(e){}DayPilot.Date=function(t,a){if(t instanceof DayPilot.Date)return t;var r;DayPilot.Util.isNullOrUndefined(t)&&(r=DayPilot.DateUtil.fromLocal().getTime(),t=r);var o=DayPilot.Date.Cache.Ctor;if(o[t])return DayPilot.Stats.cacheHitsCtor+=1,o[t];var n=!1;if("string"==typeof t||t instanceof String)r=DayPilot.DateUtil.fromStringSortable(t,a).getTime(),n=!0;else if("number"==typeof t||t instanceof Number){if(isNaN(t))throw"Cannot create DayPilot.Date from NaN";r=t}else{if(!(t instanceof Date))throw"Unrecognized parameter: use Date, number or string in ISO 8601 format";r=a?DayPilot.DateUtil.fromLocal(t).getTime():t.getTime()}var d=e(r);return o[d]?o[d]:(o[d]=this,o[r]=this,n&&d!==t&&DayPilot.DateUtil.hasTzSpec(t)&&(o[t]=this),Object.defineProperty?(Object.defineProperty(this,"ticks",{get:function(){return r}}),Object.defineProperty(this,"value",{"value":d,"writable":!1,"enumerable":!0})):(this.ticks=r,this.value=d),DayPilot.Date.Config.legacyShowD&&(this.d=new Date(r)),void(DayPilot.Stats.dateObjects+=1))},DayPilot.Date.Config={},DayPilot.Date.Config.legacyShowD=!1,DayPilot.Date.Cache={},DayPilot.Date.Cache.Parsing={},DayPilot.Date.Cache.Ctor={},DayPilot.Date.Cache.Ticks={},DayPilot.Date.Cache.DurationCtor={},DayPilot.Date.Cache.clear=function(){DayPilot.Date.Cache.Parsing={},DayPilot.Date.Cache.Ctor={},DayPilot.Date.Cache.Ticks={},DayPilot.Date.Cache.DurationCtor={}},DayPilot.Date.prototype.addDays=function(e){return e?new DayPilot.Date(this.ticks+24*e*60*60*1e3):this},DayPilot.Date.prototype.addHours=function(e){return e?this.addTime(60*e*60*1e3):this},DayPilot.Date.prototype.addMilliseconds=function(e){return e?this.addTime(e):this},DayPilot.Date.prototype.addMinutes=function(e){return e?this.addTime(60*e*1e3):this},DayPilot.Date.prototype.addMonths=function(e){if(!e)return this;var t=new Date(this.ticks),a=t.getUTCFullYear(),r=t.getUTCMonth()+1;if(e>0){for(;e>=12;)e-=12,a++;e>12-r?(a++,r=e-(12-r)):r+=e}else{for(;e<=-12;)e+=12,a--;r+e<=0?(a--,r=12+r+e):r+=e}var o=new Date(t.getTime());o.setUTCDate(1),o.setUTCFullYear(a),o.setUTCMonth(r-1);var n=new DayPilot.Date(o).daysInMonth();return o.setUTCDate(Math.min(n,t.getUTCDate())),new DayPilot.Date(o)},DayPilot.Date.prototype.addSeconds=function(e){return e?this.addTime(1e3*e):this},DayPilot.Date.prototype.addTime=function(e){return e?(e instanceof DayPilot.Duration&&(e=e.ticks),new DayPilot.Date(this.ticks+e)):this},DayPilot.Date.prototype.addYears=function(e){var t=new Date(this.ticks),a=new Date(this.ticks),r=this.getYear()+e,o=this.getMonth();a.setUTCDate(1),a.setUTCFullYear(r),a.setUTCMonth(o);var n=new DayPilot.Date(a).daysInMonth();return a.setUTCDate(Math.min(n,t.getUTCDate())),new DayPilot.Date(a)},DayPilot.Date.prototype.dayOfWeek=function(){return new Date(this.ticks).getUTCDay()},DayPilot.Date.prototype.getDayOfWeek=function(){return new Date(this.ticks).getUTCDay()},DayPilot.Date.prototype.getDayOfYear=function(){var e=this.firstDayOfYear();return DayPilot.DateUtil.daysDiff(e,this)+1},DayPilot.Date.prototype.daysInMonth=function(){var e=new Date(this.ticks),t=e.getUTCMonth()+1,a=e.getUTCFullYear(),r=[31,28,31,30,31,30,31,31,30,31,30,31];return 2!==t?r[t-1]:a%4!==0?r[1]:a%100===0&&a%400!==0?r[1]:r[1]+1},DayPilot.Date.prototype.daysInYear=function(){var e=this.getYear();return e%4!==0?365:e%100===0&&e%400!==0?365:366},DayPilot.Date.prototype.dayOfYear=function(){return Math.ceil((this.getDatePart().getTime()-this.firstDayOfYear().getTime())/864e5)+1},DayPilot.Date.prototype.equals=function(e){if(null===e)return!1;if(e instanceof DayPilot.Date)return this===e;throw"The parameter must be a DayPilot.Date object (DayPilot.Date.equals())"},DayPilot.Date.prototype.firstDayOfMonth=function(){var e=new Date;return e.setUTCFullYear(this.getYear(),this.getMonth(),1),e.setUTCHours(0),e.setUTCMinutes(0),e.setUTCSeconds(0),e.setUTCMilliseconds(0),new DayPilot.Date(e)},DayPilot.Date.prototype.firstDayOfYear=function(){var e=this.getYear(),t=new Date;return t.setUTCFullYear(e,0,1),t.setUTCHours(0),t.setUTCMinutes(0),t.setUTCSeconds(0),t.setUTCMilliseconds(0),new DayPilot.Date(t)},DayPilot.Date.prototype.firstDayOfWeek=function(e){var t=this;if(e instanceof DayPilot.Locale)e=e.weekStarts;else if("string"==typeof e&&DayPilot.Locale.find(e)){var a=DayPilot.Locale.find(e);e=a.weekStarts}else e=e||0;for(var r=t.dayOfWeek();r!==e;)t=t.addDays(-1),r=t.dayOfWeek();return new DayPilot.Date(t)},DayPilot.Date.prototype.getDay=function(){return new Date(this.ticks).getUTCDate()},DayPilot.Date.prototype.getDatePart=function(){var e=new Date(this.ticks);return e.setUTCHours(0),e.setUTCMinutes(0),e.setUTCSeconds(0),e.setUTCMilliseconds(0),new DayPilot.Date(e)},DayPilot.Date.prototype.getYear=function(){return new Date(this.ticks).getUTCFullYear()},DayPilot.Date.prototype.getHours=function(){return new Date(this.ticks).getUTCHours()},DayPilot.Date.prototype.getMilliseconds=function(){return new Date(this.ticks).getUTCMilliseconds()},DayPilot.Date.prototype.getMinutes=function(){return new Date(this.ticks).getUTCMinutes()},DayPilot.Date.prototype.getMonth=function(){return new Date(this.ticks).getUTCMonth()},DayPilot.Date.prototype.getSeconds=function(){return new Date(this.ticks).getUTCSeconds()},DayPilot.Date.prototype.getTotalTicks=function(){return this.getTime()},DayPilot.Date.prototype.getTime=function(){return this.ticks},DayPilot.Date.prototype.getTimePart=function(){var e=this.getDatePart();return DayPilot.DateUtil.diff(this,e)},DayPilot.Date.prototype.lastDayOfMonth=function(){var e=new Date(this.firstDayOfMonth().getTime()),t=this.daysInMonth();return e.setUTCDate(t),new DayPilot.Date(e)},DayPilot.Date.prototype.weekNumber=function(){var e=this.firstDayOfYear(),t=(this.getTime()-e.getTime())/864e5;return Math.ceil((t+e.dayOfWeek()+1)/7)},DayPilot.Date.prototype.weekNumberISO=function(){var e=!1,t=this.dayOfYear(),a=this.firstDayOfYear().dayOfWeek(),r=this.firstDayOfYear().addYears(1).addDays(-1).dayOfWeek();0===a&&(a=7),0===r&&(r=7);var o=8-a;4!==a&&4!==r||(e=!0);var n=Math.ceil((t-o)/7),d=n;return o>=4&&(d+=1),d>52&&!e&&(d=1),0===d&&(d=this.firstDayOfYear().addDays(-1).weekNumberISO()),d},DayPilot.Date.prototype.toDateLocal=function(){var e=new Date(this.ticks),t=new Date;return t.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),t.setHours(e.getUTCHours()),t.setMinutes(e.getUTCMinutes()),t.setSeconds(e.getUTCSeconds()),t.setMilliseconds(e.getUTCMilliseconds()),t},DayPilot.Date.prototype.toDate=function(){return new Date(this.ticks)},DayPilot.Date.prototype.toJSON=function(){return this.value},DayPilot.Date.prototype.toString=function(e,t){return e?new o(e,t).print(this):this.toStringSortable()},DayPilot.Date.prototype.toStringSortable=function(){return e(this.ticks)},DayPilot.Date.parse=function(e,t,a){return new o(t,a).parse(e)};DayPilot.Date.today=function(){return new DayPilot.Date(DayPilot.DateUtil.localToday(),!0)},DayPilot.Date.now=function(){return new DayPilot.Date},DayPilot.Date.fromYearMonthDay=function(e,t,a){t=t||1,a=a||1;var r=new Date(0);return r.setUTCFullYear(e),r.setUTCMonth(t-1),r.setUTCDate(a),new DayPilot.Date(r)},DayPilot.DateUtil={},DayPilot.DateUtil.fromStringSortable=function(e,t){if(!e)throw"Can't create DayPilot.Date from an empty string";var a=e.length,r=10===a,o=19===a,n=a>19;if(!r&&!o&&!n)throw"Invalid string format (use '2010-01-01' or '2010-01-01T00:00:00'): "+e;if(DayPilot.Date.Cache.Parsing[e]&&!t)return DayPilot.Stats.cacheHitsParsing+=1,DayPilot.Date.Cache.Parsing[e];var d=e.substring(0,4),i=e.substring(5,7),l=e.substring(8,10),s=new Date(0);if(s.setUTCFullYear(d,i-1,l),r)return DayPilot.Date.Cache.Parsing[e]=s,s;var c=e.substring(11,13),u=e.substring(14,16),f=e.substring(17,19);if(s.setUTCHours(c),s.setUTCMinutes(u),s.setUTCSeconds(f),o)return DayPilot.Date.Cache.Parsing[e]=s,s;var h=e[19],p=0;if("."===h){var m=parseInt(e.substring(20,23));s.setUTCMilliseconds(m),p=DayPilot.DateUtil.getTzOffsetMinutes(e.substring(23))}else p=DayPilot.DateUtil.getTzOffsetMinutes(e.substring(19));var y=new DayPilot.Date(s);return t||(y=y.addMinutes(-p)),s=y.toDate(),DayPilot.Date.Cache.Parsing[e]=s,s},DayPilot.DateUtil.getTzOffsetMinutes=function(e){if(DayPilot.Util.isNullOrUndefined(e)||""===e)return 0;if("Z"===e)return 0;var t=e[0],a=parseInt(e.substring(1,3)),r=parseInt(e.substring(4)),o=60*a+r;if("-"===t)return-o;if("+"===t)return o;throw"Invalid timezone spec: "+e},DayPilot.DateUtil.hasTzSpec=function(e){return!!e.indexOf("+")||!!e.indexOf("-")},DayPilot.DateUtil.daysDiff=function(e,t){if(e&&t||function(){throw"two parameters required"}(),e=new DayPilot.Date(e),t=new DayPilot.Date(t),e.getTime()>t.getTime())return null;for(var a=0,r=e.getDatePart(),o=t.getDatePart();r<o;)r=r.addDays(1),a++;return a},DayPilot.DateUtil.daysSpan=function(e,t){if(e&&t||function(){throw"two parameters required"}(),e=new DayPilot.Date(e),t=new DayPilot.Date(t),e.getTime()===t.getTime())return 0;var a=DayPilot.DateUtil.daysDiff(e,t);return t.getTime()==t.getDatePart().getTime()&&a--,a},DayPilot.DateUtil.diff=function(e,t){if(!(e&&t&&e.getTime&&t.getTime))throw"Both compared objects must be Date objects (DayPilot.Date.diff).";return e.getTime()-t.getTime()},DayPilot.DateUtil.fromLocal=function(e){e||(e=new Date);var t=new Date;return t.setUTCFullYear(e.getFullYear(),e.getMonth(),e.getDate()),t.setUTCHours(e.getHours()),t.setUTCMinutes(e.getMinutes()),t.setUTCSeconds(e.getSeconds()),t.setUTCMilliseconds(e.getMilliseconds()),t},DayPilot.DateUtil.localToday=function(){var e=new Date;return e.setHours(0),e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),e},DayPilot.DateUtil.hours=function(e,t){var a=e.getUTCMinutes();a<10&&(a="0"+a);var r=e.getUTCHours();if(t){var o=r<12,r=r%12;0===r&&(r=12);return r+":"+a+" "+(o?"AM":"PM")}return r+":"+a},DayPilot.DateUtil.max=function(e,t){return e.getTime()>t.getTime()?e:t},DayPilot.DateUtil.min=function(e,t){return e.getTime()<t.getTime()?e:t};var o=function(e,a){"string"==typeof a&&(a=DayPilot.Locale.find(a));var a=a||DayPilot.Locale.US,r=[{"seq":"yyyy","expr":"[0-9]{4,4}","str":function(e){return e.getYear()}},{"seq":"yy","expr":"[0-9]{2,2}","str":function(e){return e.getYear()%100}},{"seq":"mm","expr":"[0-9]{2,2}","str":function(e){var t=e.getMinutes();return t<10?"0"+t:t}},{"seq":"m","expr":"[0-9]{1,2}","str":function(e){return e.getMinutes()}},{"seq":"HH","expr":"[0-9]{2,2}","str":function(e){var t=e.getHours();return t<10?"0"+t:t}},{"seq":"H","expr":"[0-9]{1,2}","str":function(e){return e.getHours()}},{"seq":"hh","expr":"[0-9]{2,2}","str":function(e){var t=e.getHours(),t=t%12;0===t&&(t=12);var a=t;return a<10?"0"+a:a}},{"seq":"h","expr":"[0-9]{1,2}","str":function(e){var t=e.getHours(),t=t%12;return 0===t&&(t=12),t}},{"seq":"ss","expr":"[0-9]{2,2}","str":function(e){var t=e.getSeconds();return t<10?"0"+t:t}},{"seq":"s","expr":"[0-9]{1,2}","str":function(e){return e.getSeconds()}},{"seq":"MMMM","expr":"[^\\s0-9]*","str":function(e){return a.monthNames[e.getMonth()]},"transform":function(e){var r=DayPilot.indexOf(a.monthNames,e,t);return r<0?null:r+1}},{"seq":"MMM","expr":"[^\\s0-9]*","str":function(e){return a.monthNamesShort[e.getMonth()]},"transform":function(e){var r=DayPilot.indexOf(a.monthNamesShort,e,t);return r<0?null:r+1}},{"seq":"MM","expr":"[0-9]{2,2}","str":function(e){var t=e.getMonth()+1;return t<10?"0"+t:t}},{"seq":"M","expr":"[0-9]{1,2}","str":function(e){return e.getMonth()+1}},{"seq":"dddd","expr":"[^\\s0-9]*","str":function(e){return a.dayNames[e.getDayOfWeek()]}},{"seq":"ddd","expr":"[^\\s0-9]*","str":function(e){return a.dayNamesShort[e.getDayOfWeek()]}},{"seq":"dd","expr":"[0-9]{2,2}","str":function(e){var t=e.getDay();return t<10?"0"+t:t}},{"seq":"%d","expr":"[0-9]{1,2}","str":function(e){return e.getDay()}},{"seq":"d","expr":"[0-9]{1,2}","str":function(e){return e.getDay()}},{"seq":"tt","expr":"(AM|PM|am|pm)","str":function(e){return e.getHours()<12?"AM":"PM"},"transform":function(e){return e.toUpperCase()}}],o=function(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")};this.init=function(){this.year=this.findSequence("yyyy"),this.month=this.findSequence("MMMM")||this.findSequence("MMM")||this.findSequence("MM")||this.findSequence("M"),this.day=this.findSequence("dd")||this.findSequence("d"),this.hours=this.findSequence("HH")||this.findSequence("H"),this.minutes=this.findSequence("mm")||this.findSequence("m"),this.seconds=this.findSequence("ss")||this.findSequence("s"),this.ampm=this.findSequence("tt"),this.hours12=this.findSequence("hh")||this.findSequence("h")},this.findSequence=function(t){function a(e){return parseInt(e)}return e.indexOf(t)===-1?null:{"findValue":function(n){for(var d=o(e),i=null,l=0;l<r.length;l++){var s=(r[l].length,t===r[l].seq),c=r[l].expr;s&&(c="("+c+")",i=r[l].transform),d=d.replace(r[l].seq,c)}d="^"+d+"$";try{var u=new RegExp(d),f=u.exec(n);return f?(i=i||a)(f[1]):null}catch(e){throw"unable to create regex from: "+d}}}},this.print=function(t){for(var a=function(e){for(var t=0;t<r.length;t++)if(r[t]&&r[t].seq===e)return r[t];return null},o=e.length<=0,n=0,d=[];!o;){var i=e.substring(n),l=/%?(.)\1*/.exec(i);if(l&&l.length>0){var s=l[0],c=a(s);c?d.push(c):d.push(s),n+=s.length,o=e.length<=n}else o=!0}for(var u=0;u<d.length;u++){var f=d[u];"string"!=typeof f&&(d[u]=f.str(t))}return d.join("")},this.parse=function(e){var t=this.year.findValue(e);if(!t)return null;var a=this.month.findValue(e);if(DayPilot.Util.isNullOrUndefined(a))return null;if(a>12||a<1)return null;var r=this.day.findValue(e),o=DayPilot.Date.fromYearMonthDay(t,a).daysInMonth();if(r<1||r>o)return null;var n=this.hours?this.hours.findValue(e):0,d=this.minutes?this.minutes.findValue(e):0,i=this.seconds?this.seconds.findValue(e):0,l=this.ampm?this.ampm.findValue(e):null;if(this.ampm&&this.hours12){var s=this.hours12.findValue(e);if(s<1||s>12)return null;n="PM"===l?12===s?12:s+12:12===s?0:s}if(n<0||n>23)return null;if(d<0||d>59)return null;if(i<0||i>59)return null;var c=new Date;return c.setUTCFullYear(t,a-1,r),c.setUTCHours(n),c.setUTCMinutes(d),c.setUTCSeconds(i),c.setUTCMilliseconds(0),new DayPilot.Date(c)},this.init()};DayPilot.ColorUtil={},DayPilot.ColorUtil.hexToRgb=function(e){if(!/^#[0-9a-f]{6}$/i.test(e))throw new DayPilot.Exception("Invalid color, only full hex color string accepted, eg. '#ffaaff'.");return e=e.replace("#",""),{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16)}},DayPilot.ColorUtil.rgbToHex=function(e){return"#"+a(e.r)+a(e.g)+a(e.b)},DayPilot.ColorUtil.adjustLuminance=function(e,t){return{r:e.r+t,g:e.g+t,b:e.b+t}},DayPilot.ColorUtil.darker=function(e,t){var a="";9===e.length&&(a=e.slice(7,9),e=e.slice(0,7));var r=DayPilot.ColorUtil.hexToRgb(e);"number"!=typeof t&&(t=1);var o=17,n=Math.round(t*o),d=DayPilot.ColorUtil.adjustLuminance(r,-n);return DayPilot.ColorUtil.rgbToHex(d)+a},DayPilot.ColorUtil.lighter=function(e,t){return"number"!=typeof t&&(t=1),DayPilot.ColorUtil.darker(e,-t)},DayPilot.ColorUtil.pl=function(e){var t=DayPilot.ColorUtil.hexToRgb(e),a=t.r/255,r=t.g/255,o=t.b/255;return Math.sqrt(.299*a*a+.587*r*r+.114*o*o)},DayPilot.ColorUtil.contrasting=function(e,t,a){var r=DayPilot.ColorUtil.pl(e);return t=t||"#ffffff",a=a||"#000000",r>.5?a:t},DayPilot.Event=function(e,t,a){var r=this;this.calendar=t,this.data=e?e:{},this.part=a?a:{},"undefined"==typeof this.data.id&&(this.data.id=this.data.value);var o={},n=["id","text","start","end","resource"];this.isEvent=!0,this.temp=function(){if(o.dirty)return o;for(var e=0;e<n.length;e++)o[n[e]]=r.data[n[e]];return o.dirty=!0,o},this.copy=function(){for(var e={},t=0;t<n.length;t++)e[n[t]]=r.data[n[t]];return e},this.commit=function(){if(o.dirty){for(var e=0;e<n.length;e++)r.data[n[e]]=o[n[e]];o.dirty=!1}},this.dirty=function(){return o.dirty},this.id=function(e){return"undefined"==typeof e?r.data.id:void(this.temp().id=e)},this.value=function(e){return"undefined"==typeof e?r.id():void r.id(e)},this.text=function(e){return"undefined"==typeof e?r.data.text:(this.temp().text=e,void this.client.innerHTML(e))},this.start=function(e){return"undefined"==typeof e?new DayPilot.Date(r.data.start):void(this.temp().start=new DayPilot.Date(e))},this.end=function(e){return"undefined"==typeof e?new DayPilot.Date(r.data.end):void(this.temp().end=new DayPilot.Date(e))},this.resource=function(e){return"undefined"==typeof e?r.data.resource:void(this.temp().resource=e)},this.duration=function(){return new DayPilot.Duration(this.start(),this.end())},this.rawend=function(e){if("undefined"==typeof e)return t&&t.internal.adjustEndIn?t.internal.adjustEndIn(new DayPilot.Date(r.data.end)):new DayPilot.Date(r.data.end);throw new DayPilot.Exception("DayPilot.Event.rawend() is readonly")},this.partStart=function(){return new DayPilot.Date(this.part.start)},this.partEnd=function(){return new DayPilot.Date(this.part.end)},this.tag=function(e){var t=r.data.tag;if(!t)return null;if("undefined"==typeof e)return r.data.tag;for(var a=r.calendar.tagFields,o=-1,n=0;n<a.length;n++)e===a[n]&&(o=n);if(o===-1)throw"Field name not found.";return t[o]},this.client={},this.client.innerHTML=function(e){if("undefined"==typeof e){var t=r.cache||r.data,a=r.calendar&&r.calendar.internal&&r.calendar.internal.xssTextHtml;return a?a(t.text,t.html):DayPilot.Util.escapeTextHtml(t.text,t.html)}r.data.html=e},this.client.html=this.client.innerHTML,this.client.header=function(e){return"undefined"==typeof e?r.data.header:void(r.data.header=e)},this.client.cssClass=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.cssClass?r.cache.cssClass:r.data.cssClass:void(r.data.cssClass=e)},this.client.toolTip=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.toolTip?r.cache.toolTip:"undefined"!=typeof r.data.toolTip?r.data.toolTip:r.data.text:void(r.data.toolTip=e)},this.client.barVisible=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.barHidden?!r.cache.barHidden:r.calendar.durationBarVisible&&!r.data.barHidden:void(r.data.barHidden=!e)},this.client.backColor=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.backColor?r.cache.backColor:"undefined"!=typeof r.data.backColor?r.data.backColor:r.calendar.eventBackColor:void(r.data.backColor=e)},this.client.borderColor=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.borderColor?r.cache.borderColor:"undefined"!=typeof r.data.borderColor?r.data.borderColor:r.calendar.eventBorderColor:void(r.data.borderColor=e)},this.client.contextMenu=function(e){if("undefined"==typeof e){if(r.oContextMenu)return r.oContextMenu;r.cache?r.cache.contextMenu:r.data.contextMenu}else r.oContextMenu=e},this.client.moveEnabled=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.moveDisabled?!r.cache.moveDisabled:"Disabled"!==r.calendar.eventMoveHandling&&!r.data.moveDisabled:void(r.data.moveDisabled=!e)},this.client.resizeEnabled=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.resizeDisabled?!r.cache.resizeDisabled:"Disabled"!==r.calendar.eventResizeHandling&&!r.data.resizeDisabled:void(r.data.resizeDisabled=!e)},this.client.clickEnabled=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.clickDisabled?!r.cache.clickDisabled:"Disabled"!==r.calendar.eventClickHandling&&!r.data.clickDisabled:void(r.data.clickDisabled=!e)},this.client.rightClickEnabled=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.rightClickDisabled?!r.cache.rightClickDisabled:"Disabled"!==r.calendar.eventRightClickHandling&&!r.data.rightClickDisabled:void(r.data.rightClickDisabled=!e)},this.client.deleteEnabled=function(e){return"undefined"==typeof e?r.cache&&"undefined"!=typeof r.cache.deleteDisabled?!r.cache.deleteDisabled:"Disabled"!==r.calendar.eventDeleteHandling&&!r.data.deleteDisabled:void(r.data.deleteDisabled=!e)},this.toJSON=function(e){var t={};if(t.value=this.id(),t.id=this.id(),t.text=this.text(),t.start=this.start(),t.end=this.end(),t.tag={},r.calendar&&r.calendar.tagFields)for(var a=r.calendar.tagFields,o=0;o<a.length;o++)t.tag[a[o]]=this.tag(a[o]);return t}}}}(),DayPilot.JSON={},function(){function e(e){return e<10?"0"+e:e}function t(e){return d.lastIndex=0,d.test(e)?'"'+e.replace(d,function(e){var t=i[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function a(e,d){var i,l,s,c,u,f=r,h=d[e];switch(h&&"object"==typeof h&&"function"==typeof h.toJSON2?h=h.toJSON2(e):h&&"object"==typeof h&&"function"==typeof h.toJSON&&!h.ignoreToJSON&&(h=h.toJSON(e)),"function"==typeof n&&(h=n.call(d,e,h)),typeof h){case"string":return t(h);case"number":return isFinite(h)?String(h):"null";case"boolean":case"null":return String(h);case"object":if(!h)return"null";if(r+=o,u=[],"number"==typeof h.length&&!h.propertyIsEnumerable("length")){for(c=h.length,i=0;i<c;i+=1)u[i]=a(i,h)||"null";return s=0===u.length?"[]":r?"[\n"+r+u.join(",\n"+r)+"\n"+f+"]":"["+u.join(",")+"]",r=f,s}if(n&&"object"==typeof n)for(c=n.length,i=0;i<c;i+=1)l=n[i],"string"==typeof l&&(s=a(l,h),s&&u.push(t(l)+(r?": ":":")+s));else for(l in h)Object.hasOwnProperty.call(h,l)&&(s=a(l,h),s&&u.push(t(l)+(r?": ":":")+s));return s=0===u.length?"{}":r?"{\n"+r+u.join(",\n"+r)+"\n"+f+"}":"{"+u.join(",")+"}",r=f,s}}"function"!=typeof Date.prototype.toJSON2&&(Date.prototype.toJSON2=function(t){return this.getUTCFullYear()+"-"+e(this.getUTCMonth()+1)+"-"+e(this.getUTCDate())+"T"+e(this.getUTCHours())+":"+e(this.getUTCMinutes())+":"+e(this.getUTCSeconds())},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var r,o,n,d=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,i={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};"function"!=typeof DayPilot.JSON.stringify&&(DayPilot.JSON.stringify=function(e,t,d){var i;if(r="",o="","number"==typeof d)for(i=0;i<d;i+=1)o+=" ";else"string"==typeof d&&(o=d);if(n=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("JSON.stringify");return a("",{"":e})})}();