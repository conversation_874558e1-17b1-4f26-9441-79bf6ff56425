﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(){var e=function(){};if("undefined"==typeof DayPilot.Calendar||!DayPilot.Calendar.events){var t={};t.selectedCells=[],t.topSelectedCell=null,t.bottomSelectedCell=null,t.column=null,t.firstSelected=null,t.firstMousePos=null,t.originalMouse=null,t.originalHeight=null,t.originalTop=null,t.resizing=null,t.globalHandlers=!1,t.moving=null,t.register=function(e){t.registered||(t.registered=[]);for(var n=t.registered,i=0;i<n.length;i++)if(n[i]===e)return;n.push(e)},t.unregister=function(e){var n=t.registered;if(n){var i=DayPilot.indexOf(n,e);i!==-1&&n.splice(i,1)}},t.getCellsAbove=function(e){for(var n=[],i=t.getColumn(e),a=e.parentNode,s=null;a&&s!==t.firstSelected;)for(s=a.getElementsByTagName("td")[i],n.push(s),a=a.previousSibling;a&&"TR"!==a.tagName;)a=a.previousSibling;return n},t.getCellsBelow=function(e){for(var n=[],i=t.getColumn(e),a=e.parentNode,s=null;a&&s!==t.firstSelected;)for(s=a.getElementsByTagName("td")[i],n.push(s),a=a.nextSibling;a&&"TR"!==a.tagName;)a=a.nextSibling;return n},t.getColumn=function(e){for(var t=0;e.previousSibling;)e=e.previousSibling,"TD"===e.tagName&&t++;return t},t.gUnload=function(e){if(t.registered)for(var n=t.registered,i=0;i<n.length;i++){var a=n[i];a.dispose(),t.unregister(a)}},t.gMouseUp=function(n){if(t.resizing){if(!t.resizingShadow)return t.resizing.style.cursor="default",document.body.style.cursor="default",t.resizing=null,void(DayPilot.Global.resizing=null);var i=t.resizing.event,a=t.resizingShadow.clientHeight,s=t.resizingShadow.offsetTop,l=t.resizing.dpBorder;t.deleteShadow(t.resizingShadow),t.resizingShadow=null,t.resizing.style.cursor="default",i.calendar.nav.top.style.cursor="auto",t.resizing.onclick=null,t.resizing=null,DayPilot.Global.resizing=null,i.calendar.a(i,a,s,l)}else if(t.moving){if(!t.movingShadow)return t.moving=null,DayPilot.Global.moving=null,void(document.body.style.cursor="default");var s=t.movingShadow.offsetTop,i=t.moving.event;t.deleteShadow(t.movingShadow),DayPilot.Util.removeClass(t.moving,i.calendar.b("_event_moving_source"));var o=t.movingShadow.column;t.moving=null,DayPilot.Global.moving=null,t.movingShadow=null,i.calendar.nav.top.style.cursor="auto",i.calendar.c(i,o,s,n)}else if(DayPilot.Global.selecting){var r=DayPilot.Global.selecting.calendar;r.d=DayPilot.Global.selecting,DayPilot.Global.selecting=null;var h=r.getSelection();r.f(h.start,h.end,h.resource),"Hold"!==r.timeRangeSelectedHandling&&"HoldForever"!==r.timeRangeSelectedHandling&&e()}else t.selecting=!1},t.deleteShadow=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},t.moveShadow=function(e){var n=t.movingShadow,i=n.parentNode;i.style.display="none",n.parentNode.removeChild(n),e.firstChild.appendChild(n),n.style.left="0px",i.style.display="",n.style.width=t.movingShadow.parentNode.offsetWidth+1+"px"};var n=DayPilot.Util.isVueVNode,i=DayPilot.Util.overlaps;t.Calendar=function(e,a){var s=!1;if(this instanceof t.Calendar&&!this.g&&(s=!0,this.g=!0),!s)throw"DayPilot.Calendar() is a constructor and must be called as 'var c = new DayPilot.Calendar(id);'";var l=this;this.uniqueID=null,this.isCalendar=!0,this.v="2025.3.696-lite",this.id=e,this.clientName=e,this.cache={},this.cache.pixels={},this.elements={},this.elements.events=[],this.elements.selection=[],this.nav={},this.afterRender=function(){},this.fasterDispose=!0,this.angularAutoApply=!1,this.api=2,this.businessBeginsHour=9,this.businessEndsHour=18,this.cellDuration=30,this.cellHeight=30,this.columnMarginLeft=0,this.columnMarginRight=5,this.columnsLoadMethod="GET",this.contextMenu=null,this.days=1,this.durationBarVisible=!0,this.eventBorderRadius=null,this.eventsLoadMethod="GET",this.headerDateFormat=null,this.headerHeight=30,this.headerTextWrappingEnabled=!1,this.height=300,this.heightSpec="BusinessHours",this.hideUntilInit=!0,this.hourWidth=60,this.initScrollPos="Auto",this.loadingLabelHtml=null,this.loadingLabelText="Loading...",this.loadingLabelVisible=!0,this.locale="en-us",this.snapToGrid=!0,this.showToolTip=!0,this.startDate=(new DayPilot.Date).getDatePart(),this.cssClassPrefix="calendar_default",this.theme=null,this.timeFormat="Auto",this.useEventBoxes="Always",this.viewType="Days",this.visible=!0,this.xssProtection="Enabled",this.headerClickHandling="Enabled",this.eventClickHandling="Enabled",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.eventMoveHandling="Update",this.eventDeleteHandling="Disabled",this.timeRangeSelectedHandling="Enabled",this.onBeforeCellRender=null,this.onBeforeEventRender=null,this.onBeforeHeaderRender=null,this.onEventClick=null,this.onEventClicked=null,this.onEventDelete=null,this.onEventDeleted=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onEventRightClick=null,this.onEventRightClicked=null,this.onHeaderClick=null,this.onHeaderClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.i=!1,this.clearSelection=function(){t.topSelectedCell=null,t.bottomSelectedCell=null,this.j()},this.j=function(){DayPilot.de(l.elements.selection),l.elements.selection=[],l.nav.activeSelection=null},this.cleanSelection=this.clearSelection,this.k=function(e,t,n){var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.l();var a="JSON"+DayPilot.JSON.stringify(i);__doPostBack(l.uniqueID,a)},this.m=function(e,t,n){this.callbackTimeout&&window.clearTimeout(this.callbackTimeout),this.callbackTimeout=window.setTimeout(function(){l.loadingStart()},100);var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.l();var a="JSON"+DayPilot.JSON.stringify(i);this.backendUrl?DayPilot.request(this.backendUrl,this.n,a,this.ajaxError):"function"==typeof WebForm_DoCallback&&WebForm_DoCallback(this.uniqueID,a,this.o,this.clientName,this.onCallbackError,!0)},this.onCallbackError=function(e,t){alert("Error!\r\nResult: "+e+"\r\nContext:"+t)},this.dispose=function(){var e=l;e.i||(e.i=!0,clearInterval(e.p),e.q(),e.nav.scroll.root=null,DayPilot.pu(e.nav.loading),e.r(),e.s(),e.nav.select=null,e.nav.cornerRight=null,e.nav.scrollable=null,e.nav.zoom=null,e.nav.loading=null,e.nav.header=null,e.nav.hourTable=null,e.nav.scrolltop=null,e.nav.scroll.onscroll=null,e.nav.scroll=null,e.nav.main=null,e.nav.message=null,e.nav.messageClose=null,e.nav.top=null,t.unregister(e))},this.disposed=function(){return this.i},this.t=function(){this.nav.top.dispose=this.dispose},this.n=function(e){l.o(e.responseText)},this.l=function(){var e={};return e.control="dpc",e.id=this.id,e.v=this.v,e.days=l.days,e.startDate=l.startDate,e.heightSpec=l.heightSpec,e.businessBeginsHour=l.businessBeginsHour,e.businessEndsHour=l.businessEndsHour,e.hashes=l.hashes,e.timeFormat=l.timeFormat,e.viewType=l.viewType,e.locale=l.locale,e},this.u=function(e,t){for(var n=e.parentNode;n&&"TD"!==n.tagName;)n=n.parentNode;var i=l.eventBorderRadius;"number"==typeof i&&(i+="px");var a=document.createElement("div");a.setAttribute("unselectable","on"),a.style.position="absolute",a.style.width=e.offsetWidth+"px",a.style.height=e.offsetHeight+"px",a.style.left=e.offsetLeft+"px",a.style.top=e.offsetTop+"px",a.style.boxSizing="border-box",a.style.zIndex=101,a.className=l.b("_shadow");var s=document.createElement("div");return s.className=l.b("_shadow_inner"),i&&(s.style.borderRadius=i,a.style.borderRadius=i),a.appendChild(s),n.firstChild.appendChild(a),a},this.w={},this.w.locale=function(){var e=DayPilot.Locale.find(l.locale);return e?e:DayPilot.Locale.US},this.w.timeFormat=function(){return"Auto"!==l.timeFormat?l.timeFormat:this.locale().timeFormat},this.w.z=function(){return"Disabled"!==l.xssProtection},this.w.A=function(){if("Auto"===l.weekStarts){var e=o.locale();return e?e.weekStarts:0}return l.weekStarts||0},this.w.B=function(){var e=l.cellDuration;if(e<=1)return 1;if(e>=60)return 60;var t=[1,2,3,4,5,6,10,12,15,20,30,60],n=Math.floor(e);return Math.max.apply(null,t.filter(function(e){return e<=n}))};var o=this.w;this.o=function(e,t){if(e&&0===e.indexOf("$$$")){if(!window.console)throw"Error received from the server side: "+e;return void console.log("Error received from the server side: "+e)}var e=JSON.parse(e);if(e.CallBackRedirect)return void(document.location.href=e.CallBackRedirect);if("None"===e.UpdateType)return l.loadingStop(),void l.C();if(l.q(),"Full"===e.UpdateType&&(l.columns=e.Columns,l.days=e.Days,l.startDate=new DayPilot.Date(e.StartDate),l.heightSpec=e.HeightSpec?e.HeightSpec:l.heightSpec,l.businessBeginsHour=e.BusinessBeginsHour?e.BusinessBeginsHour:l.businessBeginsHour,l.businessEndsHour=e.BusinessEndsHour?e.BusinessEndsHour:l.businessEndsHour,l.headerDateFormat=e.HeaderDateFormat?e.HeaderDateFormat:l.headerDateFormat,l.viewType=e.ViewType,l.backColor=e.BackColor?e.BackColor:l.backColor,l.eventHeaderVisible=e.EventHeaderVisible?e.EventHeaderVisible:l.eventHeaderVisible,l.timeFormat=e.TimeFormat?e.TimeFormat:l.timeFormat,l.locale=e.Locale?e.Locale:l.locale,l.D()),e.Hashes)for(var n in e.Hashes)l.hashes[n]=e.Hashes[n];l.events.list=e.Events,l.E(),l.F(),"Full"===e.UpdateType&&(l.G(),l.H(),l.I(),l.J()),l.C(),l.K(),l.clearSelection(),l.afterRender(e.CallBackData,!0),l.loadingStop()},this.L=function(){return this.M()/36e5},this.N=function(){return this.businessBeginsHour>this.businessEndsHour?24-this.businessBeginsHour+this.businessEndsHour:this.businessEndsHour-this.businessBeginsHour},this.O=function(){return this.M()/(60*o.B()*1e3)},this.M=function(){var e=0;return e="BusinessHoursNoScroll"===this.heightSpec?this.N():24,60*e*60*1e3},this.P=function(){return"BusinessHoursNoScroll"===this.heightSpec?this.businessBeginsHour:0},this.Q=function(){return 2===l.api},this.eventClickCallBack=function(e,t){this.m("EventClick",t,e)},this.eventClickPostBack=function(e,t){this.k("EventClick",t,e)},this.R=function(e){var t=this,n=t.event;if(n.client.clickEnabled())if(l.Q()){var i={};if(i.e=n,i.originalEvent=e,i.meta=e.metaKey,i.ctrl=e.ctrlKey,i.control=l,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventClick&&(l.S.apply(function(){l.onEventClick(i)}),i.preventDefault.value))return;switch(l.eventClickHandling){case"CallBack":l.eventClickCallBack(n);break;case"PostBack":l.eventClickPostBack(n);break;case"ContextMenu":var a=n.client.contextMenu();a?a.show(n):l.contextMenu&&l.contextMenu.show(n)}"function"==typeof l.onEventClicked&&l.S.apply(function(){l.onEventClicked(i)})}else switch(l.eventClickHandling){case"PostBack":l.eventClickPostBack(n);break;case"CallBack":l.eventClickCallBack(n);break;case"JavaScript":l.onEventClick(n)}},this.T=function(e){var t=this.event;if(e.stopPropagation&&e.stopPropagation(),!t.client.rightClickEnabled())return!1;var n={};if(n.e=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventRightClick&&(l.onEventRightClick(n),n.preventDefault.value))return!1;switch(l.eventRightClickHandling){case"ContextMenu":var i=t.client.contextMenu();i?i.show(t):l.contextMenu&&l.contextMenu.show(this.event)}return"function"==typeof l.onEventRightClicked&&l.onEventRightClicked(n),e.preventDefault&&e.preventDefault(),!1},this.eventDeleteCallBack=function(e,t){this.m("EventDelete",t,e)},this.eventDeletePostBack=function(e,t){this.k("EventDelete",t,e)},this.U=function(e){if(l.Q()){var t={};if(t.e=e,t.control=l,t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventDelete&&(l.S.apply(function(){l.onEventDelete(t)}),t.preventDefault.value))return;switch(l.eventDeleteHandling){case"CallBack":l.eventDeleteCallBack(e);break;case"PostBack":l.eventDeletePostBack(e);break;case"Update":l.events.remove(e)}"function"==typeof l.onEventDeleted&&l.S.apply(function(){l.onEventDeleted(t)})}else switch(l.eventDeleteHandling){case"PostBack":l.eventDeletePostBack(e);break;case"CallBack":l.eventDeleteCallBack(e);break;case"JavaScript":l.onEventDelete(e)}},this.eventResizeCallBack=function(e,t,n,i){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=e,a.newStart=t,a.newEnd=n,this.m("EventResize",i,a)},this.eventResizePostBack=function(e,t,n,i){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=e,a.newStart=t,a.newEnd=n,this.k("EventResize",i,a)},this.a=function(e,t,n,i){var a=0,s=new Date,o=new Date;if("top"===i?(s=l.V(e,n-a),o=e.end()):"bottom"===i&&(s=e.start(),o=l.W(e,n+t-a)),l.Q()){var r={};if(r.e=e,r.control=l,r.newStart=s,r.newEnd=o,r.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventResize&&(l.S.apply(function(){l.onEventResize(r)}),r.preventDefault.value))return;switch(l.eventResizeHandling){case"PostBack":l.eventResizePostBack(e,s,o);break;case"CallBack":l.eventResizeCallBack(e,s,o);break;case"Update":e.start(s),e.end(o),l.events.update(e)}"function"==typeof l.onEventResized&&l.S.apply(function(){l.onEventResized(r)})}else switch(l.eventResizeHandling){case"PostBack":l.eventResizePostBack(e,s,o);break;case"CallBack":l.eventResizeCallBack(e,s,o);break;case"JavaScript":l.onEventResize(e,s,o)}},this.V=function(e,t){var n=l.X[e.part.dayIndex].start,i=Math.floor(t/l.cellHeight);l.snapToGrid||(i=t/l.cellHeight);var a=Math.floor(i*o.B()),s=60*a*1e3,r=60*l.P()*60*1e3;return n.addTime(s+r)},this.W=function(e,t){var n=Math.floor(t/l.cellHeight);l.snapToGrid||(n=t/l.cellHeight);var i=Math.floor(n*o.B()),a=60*i*1e3,s=60*l.P()*60*1e3;return l.X[e.part.dayIndex].start.addTime(a+s)},this.eventMovePostBack=function(e,t,n,i,a){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var s={};s.e=e,s.newStart=t,s.newEnd=n,this.k("EventMove",a,s)},this.eventMoveCallBack=function(e,t,n,i,a){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var s={};s.e=e,s.newStart=t,s.newEnd=n,this.m("EventMove",a,s)},this.c=function(e,t,n,i){var a=0,s=Math.floor((n-a)/l.cellHeight);l.snapToGrid||(s=(n-a)/l.cellHeight);var r=o.B(),h=s*r*60*1e3,c=e.start(),d=e.end(),u=new Date;c instanceof DayPilot.Date&&(c=c.toDate()),u.setTime(Date.UTC(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()));var v=c.getTime()-(u.getTime()+3600*c.getUTCHours()*1e3+Math.floor(c.getUTCMinutes()/r)*r*60*1e3);"Never"===l.useEventBoxes&&(v=0);var f=d.getTime()-c.getTime(),p=this.X[t],m=p.id;l.snapToGrid||(v=0);var g=p.start.getTime(),y=new Date;y.setTime(g+h+v);var b=new DayPilot.Date(y),D=b.addTime(f);if(l.Q()){var C={};if(C.e=e,C.newStart=b,C.newEnd=D,C.newResource=m,C.ctrl=i.ctrlKey,C.shift=i.shiftKey,C.control=l,C.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventMove&&(l.S.apply(function(){l.onEventMove(C)}),C.preventDefault.value))return;switch(l.eventMoveHandling){case"PostBack":l.eventMovePostBack(e,b,D,p.id);break;case"CallBack":l.eventMoveCallBack(e,b,D,p.id);break;case"Update":e.start(b),e.end(D),e.resource(m),l.events.update(e)}"function"==typeof l.onEventMoved&&l.S.apply(function(){l.onEventMoved(C)})}else switch(l.eventMoveHandling){case"PostBack":l.eventMovePostBack(e,b,D,p.id);break;case"CallBack":l.eventMoveCallBack(e,b,D,p.id);break;case"JavaScript":l.onEventMove(e,b,D,p.id,!1)}},this.timeRangeSelectedPostBack=function(e,t,n,i){var a={};a.start=e,a.end=t,this.k("TimeRangeSelected",i,a)},this.timeRangeSelectedCallBack=function(e,t,n,i){var a={};a.start=e,a.end=t,this.m("TimeRangeSelected",i,a)},this.f=function(e,t,n){if(e=new DayPilot.Date(e),t=new DayPilot.Date(t),this.Q()){var i={};if(i.start=e,i.end=t,i.resource=n,i.control=l,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onTimeRangeSelect&&(l.S.apply(function(){l.onTimeRangeSelect(i)}),i.preventDefault.value))return;switch(l.timeRangeSelectedHandling){case"PostBack":l.timeRangeSelectedPostBack(e,t);break;case"CallBack":l.timeRangeSelectedCallBack(e,t)}"function"==typeof l.onTimeRangeSelected&&l.S.apply(function(){l.onTimeRangeSelected(i)})}else switch(l.timeRangeSelectedHandling){case"PostBack":l.timeRangeSelectedPostBack(e,t);break;case"CallBack":l.timeRangeSelectedCallBack(e,t);break;case"JavaScript":l.onTimeRangeSelected(e,t)}},this.Y=function(e){if(!DayPilot.Global.selecting&&"Disabled"!==l.timeRangeSelectedHandling){var t=e.which;if(1===t||0===t){DayPilot.Global.selecting={calendar:l,start:l.coords};var n=DayPilot.Global.selecting;return n.start.time=l.Z(l.coords.x,l.coords.y),n.start.columnIndex=l.$(l.coords.x),n.start.column=l.X[n.start.columnIndex],l._(),l.aa(),!1}}},this.aa=function(){var e=DayPilot.Global.selecting;!function(){var t=e.start.column,n=e.start.columnIndex,i=t.start,a=l.getPixels(e.startTime,i),s=l.getPixels(e.endTime,i),o=a.boxTop,r=s.boxBottom;l.snapToGrid||(o=a.top,r=s.top);var h=r-o,c=l.eventBorderRadius;"number"==typeof c&&(c+="px");var d=function(){if(l.nav.activeSelection)return l.nav.activeSelection;var e=document.createElement("div");e.setAttribute("unselectable","on"),e.style.position="absolute",e.style.left="0px",e.style.width="100%";var t=document.createElement("div");return t.setAttribute("unselectable","on"),t.className=l.b("_shadow_inner"),c&&(t.style.borderRadius=c,e.style.borderRadius=c),e.appendChild(t),l.nav.events.rows[0].cells[n].selection.appendChild(e),l.elements.selection.push(e),l.nav.activeSelection=e,e}();d.className=l.b("_shadow"),d.firstChild.innerHTML="",d.style.top=o+"px",d.style.height=h+"px",l.nav.events.rows[0].cells[n].selection.appendChild(d)}()},this._=function(){var e=DayPilot.Global.selecting;e.end=l.coords,e.end.time=l.Z(e.start.x,e.end.y),e.end.column=l.X[l.$(e.end.x)],l.ba&&(e.end.time=l.Z(e.end.x,e.end.y)),l.ca(e)},this.ca=function(e){var t,n,i;l.snapToGrid?e.end.time<e.start.time?(t=l.da(e.end.time,e.start.column.start),n=l.ea(e.start.time,e.start.column.start),i=e.endTime):(t=l.da(e.start.time,e.start.column.start),n=l.ea(e.end.time,e.start.column.start),i=e.startTime):e.end.time<e.start.time?(t=e.end.time,n=e.start.time,i=e.endTime):(t=e.start.time,n=e.end.time,i=e.startTime),e.startTime=t,e.endTime=n,e.anchor=i},this.getSelection=function(){if(!l.d)return null;var e=l.d;return new DayPilot.Selection(e.startTime,e.endTime,e.start.column.id,l)},this.$=function(e){if(e-=l.hourWidth,e<0)return null;for(var t=0,n=l.nav.events.rows[0].cells,i=0;i<n.length;i++){if(t+=n[i].offsetWidth,e<t)return i}return null},this.Z=function(e,t){e=DayPilot.Util.atLeast(e,0);var n=this.$(e),i=t/(60/o.B())/l.cellHeight,a=60*i*60*1e3,s=6e4*Math.floor(a/6e4),r=this.X[n];return r?r.start.addTime(s):null},this.da=function(e,t){var n=e.getTime();t&&(n-=t.getTime());var i=60*o.B()*1e3,a=n%i;return e.addTime(-a)},this.ea=function(e,t){var n=this.da(e,t);return n.getTime()===e.getTime()?n:n.addTime(60*o.B()*1e3)},this.fa={},this.fa.getCellCoords=function(){var e={};if(e.x=0,e.y=0,!l.coords)return null;e.x=l.$(l.coords.x);var t=0,n=Math.floor((l.coords.y-t)/l.cellHeight);return e.y=n,e.x<0?null:e},this.columns={},this.columns.list=[],this.columns.load=function(e,t,n){if(!e)throw new DayPilot.Exception("columns.load(): 'url' parameter required");var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var s={};return s.exception=e,void i(s)}if(DayPilot.isArray(n)){var o={};if(o.preventDefault=function(){this.preventDefault.value=!0},o.data=n,"function"==typeof t&&t(o),o.preventDefault.value)return;l.columns.list=n,l.ga&&l.update()}};l.columnsLoadMethod&&"POST"===l.columnsLoadMethod.toUpperCase()?DayPilot.ajax({"method":"POST","url":e,"success":a,"error":i}):DayPilot.ajax({"method":"GET","url":e,"success":a,"error":i})},this.D=function(){var e;e="Resources"!==l.viewType?this.ha():l.columns.list,this.X=[];for(var t=0;t<e.length;t++){var n=this.ia(e[t]);this.X.push(n)}},this.ia=function(e){var t={};if(t.name=e.name,t.html=e.html,t.id=e.id,t.toolTip=e.toolTip,t.data=e,e.start?t.start=new DayPilot.Date(e.start):t.start=new DayPilot.Date(l.startDate),"BusinessHoursNoScroll"===this.heightSpec){var n=t.start.getDatePart();t.start=n.addHours(this.businessBeginsHour)}return t.putIntoBlock=function(e){for(var t=0;t<this.blocks.length;t++){var n=this.blocks[t];if(n.overlapsWith(e.part.top,e.part.height))return n.events.push(e),n.min=Math.min(n.min,e.part.top),n.max=Math.max(n.max,e.part.top+e.part.height),t}var n=[];return n.lines=[],n.events=[],n.overlapsWith=function(e,t){return!(e+t-1<this.min||e>this.max-1)},n.putIntoLine=function(e){for(var t=0;t<this.lines.length;t++){var n=this.lines[t];if(n.isFree(e.part.top,e.part.height))return n.push(e),t}var n=[];return n.isFree=function(e,t){for(var n=e+t-1,i=this.length,a=0;a<i;a++){var s=this[a];if(!(n<s.part.top||e>s.part.top+s.part.height-1))return!1}return!0},n.push(e),this.lines.push(n),this.lines.length-1},n.events.push(e),n.min=e.part.top,n.max=e.part.top+e.part.height,this.blocks.push(n),this.blocks.length-1},t.putIntoLine=function(e){for(var t=0;t<this.lines.length;t++){var n=this.lines[t];if(n.isFree(e.part.top,e.part.height))return n.push(e),t}var n=[];return n.isFree=function(e,t){for(var n=e+t-1,i=this.length,a=0;a<i;a++){var s=this[a];if(!(n<s.part.top||e>s.part.top+s.part.height-1))return!1}return!0},n.push(e),this.lines.push(n),this.lines.length-1},t},this.ha=function(){var e=[],t=this.startDate.getDatePart(),n=this.days;switch(this.viewType){case"Day":n=1;break;case"Week":n=7;var i=o.A();t=t.firstDayOfWeek(i);break;case"WorkWeek":n=5,t=t.firstDayOfWeek(1)}for(var a=0;a<n;a++){var s=l.headerDateFormat?l.headerDateFormat:o.locale().datePattern,r={};r.start=t.addDays(a),r.name=r.start.toString(s,o.locale()),e.push(r)}return e},this.visibleStart=function(){if("Resources"===l.viewType){if(0===l.X.length)return DayPilot.Date.today();var e=l.X.map(function(e){return e.start.getTime()}),t=Math.min.apply(null,e);return new DayPilot.Date(t)}return this.X[0].start},this.visibleEnd=function(){if("Resources"===l.viewType){if(0===l.X.length)return DayPilot.Date.today().addDays(1);var e=l.X.map(function(e){return e.start.getTime()}),t=Math.max.apply(null,e);return new DayPilot.Date(t).addDays(1)}var t=this.X.length-1;return this.X[t].start.addDays(1)},this.b=function(e){var t=this.theme||this.cssClassPrefix;return t?t+e:""},this.q=function(){if(this.elements.events)for(var e=0;e<this.elements.events.length;e++){var t=this.elements.events[e];l.ja(t)}this.elements.events=[]},this.ja=function(e){!function(){var t=e.domArgs;if(e.domArgs=null,t&&"function"==typeof l.onBeforeEventDomRemove&&l.onBeforeEventDomRemove(t),t&&"function"==typeof l.onBeforeEventDomAdd){var i=t&&t.ka;if(i){l.ma.la&&n(t.element)&&(l.ma.na=!0,l.ma.oa(i),l.ma.na=!1)}}}();var t=e.event;if(t&&(t.calendar=null),e.onclick=null,e.onclickSave=null,e.onmouseover=null,e.onmouseout=null,e.onmousemove=null,e.onmousedown=null,e.firstChild&&e.firstChild.firstChild&&e.firstChild.firstChild.tagName&&"IMG"===e.firstChild.firstChild.tagName.toUpperCase()){var i=e.firstChild.firstChild;i.onmousedown=null,i.onmousemove=null,i.onclick=null}e.helper=null,e.data=null,e.event=null,DayPilot.de(e)},this.pa=function(e){var i=e.cache||e.data,a=this.nav.events,s=i.borderRadius||l.eventBorderRadius;"number"==typeof s&&(s+="px");var o=document.createElement("div");o.style.position="absolute",o.style.left=e.part.left+"%",o.style.top=e.part.top+"px",o.style.width=e.part.width+"%",o.style.height=Math.max(e.part.height,2)+"px",o.style.overflow="hidden",o.data=e,o.event=e,o.unselectable="on",o.style.MozUserSelect="none",o.style.KhtmlUserSelect="none",o.className=this.b("_event"),i.cssClass&&DayPilot.Util.addClass(o,i.cssClass),l.showToolTip&&e.client.toolTip()&&(o.title=e.client.toolTip()),o.isFirst=e.part.start.getTime()===e.start().getTime(),o.isLast=e.part.end.getTime()===e.end().getTime(),o.onclick=this.R,DayPilot.re(o,"contextmenu",this.T),o.onmouseout=function(e){o.deleteIcon&&(o.deleteIcon.style.display="none")},o.onmousemove=function(n){var i=5;if("undefined"!=typeof t){var a=DayPilot.mo3(o,n);if(a&&!t.resizing&&!t.moving){o.deleteIcon&&(o.deleteIcon.style.display="");var s=this.isLast;a.y<=i&&e.client.resizeEnabled()?(this.style.cursor="n-resize",this.dpBorder="top"):this.offsetHeight-a.y<=i&&e.client.resizeEnabled()?s?(this.style.cursor="s-resize",this.dpBorder="bottom"):this.style.cursor="not-allowed":t.resizing||t.moving||("Disabled"!==l.eventClickHandling?this.style.cursor="pointer":this.style.cursor="default")}}},o.onmousedown=function(n){var i=n.which||n.button;if("n-resize"!==this.style.cursor&&"s-resize"!==this.style.cursor||1!==i){if(1===i&&e.client.moveEnabled()){t.moving=this,DayPilot.Global.moving=this,t.moving.event=this.event;var a=t.moving.helper={};a.oldColumn=l.X[this.data.part.dayIndex].id,t.originalMouse=DayPilot.mc(n),t.originalTop=this.offsetTop;var s=DayPilot.mo3(this,n);s?t.moveOffsetY=s.y:t.moveOffsetY=0,l.nav.top.style.cursor="move"}}else t.resizing=this,DayPilot.Global.resizing=this,t.originalMouse=DayPilot.mc(n),t.originalHeight=this.offsetHeight,t.originalTop=this.offsetTop,l.nav.top.style.cursor=this.style.cursor;return!1};var r=document.createElement("div");if(r.setAttribute("unselectable","on"),r.className=l.b("_event_inner"),"darker"===i.borderColor&&i.backColor?r.style.borderColor=DayPilot.ColorUtil.darker(i.backColor,2):r.style.borderColor=i.borderColor,i.backColor&&(r.style.background=i.backColor),i.fontColor&&(r.style.color=i.fontColor),s&&(o.style.borderRadius=s,r.style.borderRadius=s),o.appendChild(r),e.client.barVisible()){var h=e.part.height-2,c=100*e.part.barTop/h,d=Math.ceil(100*e.part.barHeight/h),u=document.createElement("div");u.setAttribute("unselectable","on"),u.className=this.b("_event_bar"),u.style.position="absolute",i.barBackColor&&(u.style.backgroundColor=i.barBackColor);var v=document.createElement("div");v.setAttribute("unselectable","on"),v.className=this.b("_event_bar_inner"),v.style.top=c+"%",0<d&&d<=1?v.style.height="1px":v.style.height=d+"%",i.barColor&&(v.style.backgroundColor=i.barColor),u.appendChild(v),o.appendChild(u)}if(e.client.deleteEnabled()){var f=document.createElement("div");f.style.position="absolute",f.style.right="2px",f.style.top="2px",f.style.width="17px",f.style.height="17px",f.className=l.b("_event_delete"),f.onmousedown=function(e){e.stopPropagation()},f.onclick=function(e){e.stopPropagation();var t=this.parentNode.event;t&&l.U(t)},f.style.display="none",o.deleteIcon=f,o.appendChild(f)}var p=i.areas?DayPilot.Areas.copy(i.areas):[];if(DayPilot.Areas.attach(o,e,{"areas":p}),"function"==typeof l.onAfterEventRender){var m={};m.e=o.event,m.div=o,l.onAfterEventRender(m)}if(function(){var t={};if(t.control=l,t.e=e,t.element=null,o.domArgs=t,"function"==typeof l.onBeforeEventDomAdd&&l.onBeforeEventDomAdd(t),t.element){var i=r;if(i){t.ka=i;if(n(t.element)){if(!l.ma.la)throw new DayPilot.Exception("Can't reach Vue");l.ma.na=!0,l.ma.qa(t.element,i),l.ma.na=!1}else i.appendChild(t.element)}}else r.innerHTML=e.client.innerHTML()}(),a.rows[0].cells[e.part.dayIndex]){a.rows[0].cells[e.part.dayIndex].firstChild.appendChild(o),l.ra(o)}l.elements.events.push(o)},this.ra=function(e){for(var t=e&&e.childNodes?e.childNodes.length:0,n=0;n<t;n++)try{var i=e.childNodes[n];1===i.nodeType&&(i.unselectable="on",this.ra(i))}catch(e){}},this.K=function(){for(var e=0;e<this.X.length;e++){var t=this.X[e];if(t.blocks)for(var n=0;n<t.blocks.length;n++)for(var i=t.blocks[n],a=0;a<i.lines.length;a++)for(var s=i.lines[a],l=0;l<s.length;l++){var o=s[l];o.part.width=100/i.lines.length,o.part.left=o.part.width*a;var r=a===i.lines.length-1;r||(o.part.width=1.5*o.part.width),this.pa(o)}}},this.sa=function(){this.nav.top.innerHTML="",DayPilot.Util.addClass(this.nav.top,this.b("_main")),this.nav.top.style.MozUserSelect="none",this.nav.top.style.KhtmlUserSelect="none",this.nav.top.style.position="relative",this.nav.top.style.width=this.width?this.width:"100%",this.hideUntilInit&&(this.nav.top.style.visibility="hidden"),this.visible||(this.nav.top.style.display="none"),this.nav.scroll=document.createElement("div"),this.nav.scroll.style.height=this.ta()+"px","BusinessHours"===this.heightSpec?this.nav.scroll.style.overflow="auto":this.nav.scroll.style.overflow="hidden",this.nav.scroll.style.position="relative";var e=this.ua();this.nav.top.appendChild(e),this.nav.scroll.style.zoom=1;var t=this.va();this.nav.scrollable=t.firstChild,this.nav.scroll.appendChild(t),this.nav.top.appendChild(this.nav.scroll),this.nav.scrollLayer=document.createElement("div"),this.nav.scrollLayer.style.position="absolute",this.nav.scrollLayer.style.top="0px",this.nav.scrollLayer.style.left="0px",this.nav.top.appendChild(this.nav.scrollLayer),this.nav.loading=document.createElement("div"),this.nav.loading.style.position="absolute",this.nav.loading.style.top="0px",this.nav.loading.style.left=this.hourWidth+5+"px",this.nav.loading.innerHTML=l.wa(l.loadingLabelText,l.loadingLabelHtml),this.nav.loading.style.display="none",this.nav.top.appendChild(this.nav.loading)},this.I=function(){this.fasterDispose||DayPilot.pu(this.nav.hourTable),this.nav.scrollable.rows[0].cells[0].innerHTML="",this.nav.hourTable=this.xa(),this.nav.scrollable.rows[0].cells[0].appendChild(this.nav.hourTable)},this.va=function(){var e=document.createElement("div");e.style.zoom=1,e.style.position="relative";var t=document.createElement("table");t.cellSpacing="0",t.cellPadding="0",t.border="0",t.style.border="0px none",t.style.width="100%",t.style.position="absolute";var n,i=t.insertRow(-1);n=i.insertCell(-1),n.valign="top",n.style.padding="0px",n.style.border="0px none",this.nav.hourTable=this.xa(),n.appendChild(this.nav.hourTable),n=i.insertCell(-1),n.valign="top",n.width="100%",n.style.padding="0px",n.style.border="0px none";var a=document.createElement("div");return a.style.position="relative",n.appendChild(a),a.appendChild(this.ya()),a.appendChild(this.za()),e.appendChild(t),this.nav.zoom=e,e},this.ya=function(){var e=document.createElement("table");return e.cellPadding="0",e.cellSpacing="0",e.border="0",e.style.width="100%",e.style.border="0px none",e.style.tableLayout="fixed",this.nav.main=e,this.nav.events=e,e},this.za=function(){var e=document.createElement("table");e.style.top="0px",e.cellPadding="0",e.cellSpacing="0",e.border="0",e.style.position="absolute",e.style.width="100%",e.style.border="0px none",e.style.tableLayout="fixed",this.nav.events=e;for(var t=this.X,n=t.length,i=e.insertRow(-1),a=0;a<n;a++){var s=i.insertCell(-1);s.style.padding="0px",s.style.border="0px none",s.style.height="0px",s.style.overflow="visible",l.rtl||(s.style.textAlign="left");var o=document.createElement("div");o.style.marginRight=l.columnMarginRight+"px",o.style.marginLeft=l.columnMarginLeft+"px",o.style.position="relative",o.style.height="1px",o.style.marginTop="-1px";var r=document.createElement("div");s.selection=r,s.appendChild(o),s.appendChild(r)}return e},this.xa=function(){var e=document.createElement("table");e.cellSpacing="0",e.cellPadding="0",e.border="0",e.style.border="0px none",e.style.width=this.hourWidth+"px",e.oncontextmenu=function(){return!1};for(var t=l.L(),n=0;n<t;n++)this.Aa(e,n);return e},this.Aa=function(e,t){var n=60*l.cellHeight/o.B(),i=e.insertRow(-1);i.style.height=n+"px";var a=i.insertCell(-1);a.valign="bottom",a.unselectable="on",a.style.cursor="default",a.style.padding="0px",a.style.border="0px none";var s=document.createElement("div");s.style.position="relative",s.className=this.b("_rowheader"),s.style.width=this.hourWidth+"px",s.style.height=n+"px",s.style.overflow="hidden",s.unselectable="on";var r=document.createElement("div");r.className=this.b("_rowheader_inner"),r.unselectable="on";var h=document.createElement("div");h.unselectable="on";var c=this.startDate.addHours(t).addHours(l.P()),d=c.getHours(),u=d<12,v=o.timeFormat();"Clock12Hours"===v&&(d%=12,0===d&&(d=12)),h.innerHTML=d;var f=document.createElement("span");f.unselectable="on",f.className=this.b("_rowheader_minutes");var p;p="Clock12Hours"===v?u?"AM":"PM":"00",f.innerHTML=p,h.appendChild(f),r.appendChild(h),s.appendChild(r),a.appendChild(s)},this.ta=function(){var e=o.B(),t=60/e;switch(this.heightSpec){case"Full":
return 24*t*this.cellHeight;case"BusinessHours":var n=this.N();return n*this.cellHeight*t;case"BusinessHoursNoScroll":var n=this.N();return n*this.cellHeight*t;default:throw"DayPilot.Calendar: Unexpected 'heightSpec' value."}},this.Ba=function(){var e=l.nav.corner?l.nav.corner.parentNode:null;if(e){e.innerHTML="";var t=this.Ca();e.appendChild(t),l.nav.corner=t}},this.ua=function(){var e=document.createElement("div");e.style.overflow="auto";var t=document.createElement("table");t.cellPadding="0",t.cellSpacing="0",t.border="0",t.style.width="100%",t.style.borderCollapse="separate",t.style.border="0px none";var n=t.insertRow(-1),i=n.insertCell(-1);i.style.padding="0px",i.style.border="0px none";var a=this.Ca();i.appendChild(a),this.nav.corner=a,i=n.insertCell(-1),i.style.width="100%",i.valign="top",i.style.position="relative",i.style.padding="0px",i.style.border="0px none",this.nav.header=document.createElement("table"),this.nav.header.cellPadding="0",this.nav.header.cellSpacing="0",this.nav.header.border="0",this.nav.header.width="100%",this.nav.header.style.tableLayout="fixed",this.nav.header.oncontextmenu=function(){return!1};var s="hidden"!==this.nav.scroll.style.overflow;if(i.appendChild(this.nav.header),s){i=n.insertCell(-1),i.unselectable="on";var l=document.createElement("div");l.unselectable="on",l.style.position="relative",l.style.width="16px",l.style.height=this.headerHeight+"px",l.className=this.b("_cornerright");var o=document.createElement("div");o.className=this.b("_cornerright_inner"),l.appendChild(o),i.appendChild(l),this.nav.cornerRight=l}return e.appendChild(t),e},this.Ca=function(){var e=document.createElement("div");e.style.position="relative",e.className=this.b("_corner"),e.style.width=this.hourWidth+"px",e.style.height=this.headerHeight+"px",e.oncontextmenu=function(){return!1};var t=document.createElement("div");return t.unselectable="on",t.className=this.b("_corner_inner"),e.appendChild(t),e},this.r=function(){var e=this.nav.main;e.root=null,e.onmouseup=null;for(var t=0;t<e.rows.length;t++)for(var n=e.rows[t],i=0;i<n.cells.length;i++){var a=n.cells[i];l.Da(a)}this.fasterDispose||DayPilot.pu(e)},this.Da=function(e){e&&(!function(){var t=e,i=t.domArgs;if(t.domArgs=null,i&&"function"==typeof l.onBeforeCellDomRemove&&l.onBeforeCellDomRemove(i),i&&"function"==typeof l.onBeforeCellDomAdd){var a=i&&i.ka;if(a){l.ma.la&&n(i.element)&&(l.ma.na=!0,l.ma.oa(a),l.ma.na=!1)}}}(),e.root=null,e.onmousedown=null,e.onmousemove=null,e.onmouseout=null,e.onmouseup=null)},this.H=function(){var e=o.B(),i=this.nav.main,a=60*e*1e3,s=this.O(),r=l.X;for(i&&this.r();i&&i.rows&&i.rows.length>0;)this.fasterDispose||DayPilot.pu(i.rows[0]),i.deleteRow(0);this.tableCreated=!0;for(var h=r.length,c=this.nav.events;c&&c.rows&&c.rows.length>0;)this.fasterDispose||DayPilot.pu(c.rows[0]),c.deleteRow(0);for(var h=r.length,d=c.insertRow(-1),u=0;u<h;u++){var v=d.insertCell(-1);v.style.padding="0px",v.style.border="0px none",v.style.height="0px",v.style.overflow="visible",l.rtl||(v.style.textAlign="left");var f=document.createElement("div");f.style.marginRight=l.columnMarginRight+"px",f.style.marginLeft=l.columnMarginLeft+"px",f.style.position="relative",f.style.height="1px",f.style.marginTop="-1px";var p=document.createElement("div");p.style.position="relative",v.selection=p,v.appendChild(f),v.appendChild(p)}for(var m=0;m<s;m++){var d=i.insertRow(-1);d.style.MozUserSelect="none",d.style.KhtmlUserSelect="none";for(var u=0;u<h;u++){var g=this.X[u],v=d.insertCell(-1);v.start=g.start.addTime(m*a),v.end=v.start.addTime(a),v.resource=g.id,v.onmousedown=this.Y,v.onmouseup=function(){return!1},v.onclick=function(){return!1},v.root=this,v.style.padding="0px",v.style.border="0px none",v.style.verticalAlign="top",v.style.height=l.cellHeight+"px",v.style.overflow="hidden",v.unselectable="on";var f=document.createElement("div");f.unselectable="on",f.style.height=l.cellHeight+"px",f.style.position="relative",f.className=this.b("_cell");var y=this.Ea(v.start,v.end),b={"business":y,"text":null,"html":null,"cssClass":null,"backColor":null,"backImage":null,"backRepeat":null,"fontColor":null},D={"start":v.start,"end":v.end,"resource":v.resource,"properties":b,"x":u,"y":m};!function(){if("function"==typeof l.onBeforeCellRender){var e={};e.cell=D,l.onBeforeCellRender(e)}}(),b.business&&DayPilot.Util.addClass(f,l.b("_cell_business")),b.cssClass&&DayPilot.Util.addClass(f,b.cssClass);var C=document.createElement("div");C.setAttribute("unselectable","on"),C.className=this.b("_cell_inner");var w=DayPilot.Util.escapeTextHtml(b.text,b.html);w&&(C.innerHTML=w),b.backColor&&(C.style.backgroundColor=b.backColor),b.backImage&&(C.style.backgroundImage="url("+b.backImage+")"),b.backRepeat&&(C.style.backgroundRepeat=b.backRepeat),b.fontColor&&(C.style.color=b.fontColor),f.appendChild(C),function(){if("function"==typeof l.onBeforeCellDomAdd||"function"==typeof l.onBeforeCellDomRemove){var e={};if(e.control=l,e.cell=D,e.element=null,v.domArgs=e,"function"==typeof l.onBeforeCellDomAdd&&l.onBeforeCellDomAdd(e),e.element){var t=C;if(t){e.ka=t;if(n(e.element)){if(!l.ma.la)throw new DayPilot.Exception("Can't reach Vue");l.ma.na=!0,l.ma.qa(e.element,t),l.ma.na=!1}else t.appendChild(e.element)}}}}(),v.appendChild(f)}}i.root=this,l.nav.scrollable.onmousemove=function(e){var n=l.nav.scrollable;l.coords=DayPilot.mo3(n,e);var i=DayPilot.mc(e);if(t.resizing){t.resizingShadow||(t.resizingShadow=l.u(t.resizing,!1,l.shadow));var a=l.cellHeight,s=0,o=i.y-t.originalMouse.y;if("bottom"===t.resizing.dpBorder){var r=t.originalHeight+o;l.snapToGrid&&(r=Math.floor((t.originalHeight+t.originalTop+o+a/2)/a)*a-t.originalTop+s),r<a&&(r=a);var h=l.nav.main.clientHeight;t.originalTop+r>h&&(r=h-t.originalTop),t.resizingShadow.style.height=r+"px"}else if("top"===t.resizing.dpBorder){var c=t.originalTop+o;l.snapToGrid&&(c=Math.floor((t.originalTop+o-s+a/2)/a)*a+s),c<s&&(c=s),c>t.originalTop+t.originalHeight-a&&(c=t.originalTop+t.originalHeight-a);var r=t.originalHeight-(c-t.originalTop);r<a?r=a:t.resizingShadow.style.top=c+"px",t.resizingShadow.style.height=r+"px"}}else if(t.moving){if(!l.coords)return;if(!t.movingShadow){var d=3,i=DayPilot.mc(e),u=Math.abs(i.x-t.originalMouse.x)+Math.abs(i.y-t.originalMouse.y);if(u<=d)return;t.movingShadow=l.u(t.moving,!0,l.shadow),t.movingShadow.style.width=t.movingShadow.parentNode.offsetWidth+1+"px"}var a=l.cellHeight,s=0,v=t.moveOffsetY;v||(v=a/2);var c=l.coords.y-v;l.snapToGrid&&(c=Math.floor((l.coords.y-v-s+a/2)/a)*a+s),c<s&&(c=s);var f=l.nav.events,h=l.nav.main.clientHeight+s,p=parseInt(t.movingShadow.style.height);c+p>h&&(c=h-p),DayPilot.Util.addClass(t.moving,l.b("_event_moving_source")),t.movingShadow.parentNode.style.display="none",t.movingShadow.style.top=c+"px",t.movingShadow.parentNode.style.display="";var m=f.clientWidth/f.rows[0].cells.length,g=Math.floor((l.coords.x-45)/m);g<0&&(g=0),g<f.rows[0].cells.length&&g>=0&&t.movingShadow.column!==g&&(t.movingShadow.column=g,t.moveShadow(f.rows[0].cells[g]))}else DayPilot.Global.selecting&&(l._(),l.aa())},l.nav.scrollable.style.display=""},this.Ea=function(e,t){return this.businessBeginsHour<this.businessEndsHour?!(e.getHours()<this.businessBeginsHour||e.getHours()>=this.businessEndsHour||6===e.getDayOfWeek()||0===e.getDayOfWeek()):e.getHours()>=this.businessBeginsHour||e.getHours()<this.businessEndsHour},this.s=function(){var e=this.nav.header;if(e&&e.rows)for(var t=0;t<e.rows.length;t++)for(var n=e.rows[t],i=0;i<n.cells.length;i++){var a=n.cells[i];a.onclick=null,a.onmousemove=null,a.onmouseout=null}this.fasterDispose||DayPilot.pu(e)},this.Fa=function(e){function t(t){var a=i[t],s=e?n.insertCell(-1):n.cells[t];s.data=a,s.style.overflow="hidden",s.style.padding="0px",s.style.border="0px none",s.style.height=l.headerHeight+"px",s.onclick=l.Ga;var o,r=e?document.createElement("div"):s.firstChild;e?(r.unselectable="on",r.style.MozUserSelect="none",r.style.cursor="default",r.style.position="relative",r.className=l.b("_colheader"),r.style.height=l.headerHeight+"px",l.headerTextWrappingEnabled||(r.style.whiteSpace="nowrap"),o=document.createElement("div"),o.className=l.b("_colheader_inner"),o.unselectable="on",r.appendChild(o),s.appendChild(r)):o=r.firstChild;var h={};h.header={},h.header.cssClass=null,h.header.verticalAlignment="center",h.header.horizontalAlignment="center",h.column=l.Ha(a,l),"function"==typeof l.onBeforeHeaderRender&&(DayPilot.Util.copyProps(a,h.header,["id","start","name","html","backColor","toolTip","areas"]),l.onBeforeHeaderRender(h),DayPilot.Util.copyProps(h.header,a,["html","backColor","toolTip","areas","cssClass","verticalAlignment","horizontalAlignment"])),a.toolTip&&(o.title=a.toolTip),a.cssClass&&DayPilot.Util.addClass(r,a.cssClass),a.backColor&&(o.style.background=a.backColor),a.areas&&DayPilot.Areas.attach(r,a);var c=a.verticalAlignment;if(c)switch(o.style.display="flex",c){case"center":o.style.alignItems="center";break;case"top":o.style.alignItems="flex-start";break;case"bottom":o.style.alignItems="flex-end"}var d=a.horizontalAlignment;if(d)switch(d){case"center":o.style.justifyContent="center";break;case"left":o.style.justifyContent="flex-start";break;case"right":o.style.justifyContent="flex-end"}r.firstChild.innerHTML=l.wa(a.name,a.html)}for(var n=e?this.nav.header.insertRow(-1):this.nav.header.rows[0],i=this.X,a=i.length,s=0;s<a;s++)t(s)},this.Ga=function(e){if("Disabled"!==l.headerClickHandling){var t=this.data,n=l.Ha(t),i={};i.header={},i.header.id=t.id,i.header.name=t.name,i.header.start=t.start,i.column=n,i.originalEvent=e,i.shift=e.shiftKey,i.ctrl=e.ctrlKey,i.meta=e.metaKey,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onHeaderClick&&(l.onHeaderClick(i),i.preventDefault.value)||"function"==typeof l.onHeaderClicked&&l.onHeaderClicked(i)}},this.Ha=function(e){return new DayPilot.CalendarColumn(e,l)},this.Ia=function(){return this.width&&this.width.indexOf("px")!==-1?"Pixel":"Percentage"},this.G=function(){var e=this.nav.header,t=!0,n=this.X;for(n.length;this.headerCreated&&e&&e.rows&&e.rows.length>0;)this.fasterDispose||DayPilot.pu(e.rows[0]),e.deleteRow(0);this.headerCreated=!0;this.Fa(t)},this.loadingStart=function(){this.loadingLabelVisible&&(this.nav.loading.innerHTML=this.loadingLabelText,this.nav.loading.style.top=this.headerHeight+5+"px",this.nav.loading.style.display="")},this.commandCallBack=function(e,t){var n={};n.command=e,this.m("Command",t,n)},this.loadingStop=function(e){this.callbackTimeout&&window.clearTimeout(this.callbackTimeout),this.nav.loading.style.display="none"},this.Ja=function(){var e=this.nav.scroll;e.root=this,l.Ka(),e.onscroll||(e.onscroll=function(){l.La()})},this.callbackError=function(e,t){alert("Error!\r\nResult: "+e+"\r\nContext:"+t)},this.Ma=function(){var e=DayPilot.sw(this.nav.scroll),t=this.nav.cornerRight;t&&(t.style.width=e+"px")},this.Na=function(){t.globalHandlers||(t.globalHandlers=!0,DayPilot.re(document,"mouseup",t.gMouseUp))},this.events={},this.events.add=function(e){var t=null;if(e instanceof DayPilot.Event)t=e.data;else{if("object"!=typeof e)throw"DayPilot.Calendar.events.add() expects an object or DayPilot.Event instance.";t=e}l.events.list||(l.events.list=[]),l.events.list.push(t),l.Oa({"eventsOnly":!0}),l.S.notify()},this.events.find=function(e){if(!l.events.list)return null;if("function"==typeof e){for(var t=e,n=0;n<l.events.list.length;n++){var i=new DayPilot.Event(l.events.list[n],l);if(t(i))return i}return null}for(var n=0;n<l.events.list.length;n++){var a=l.events.list[n];if(a.id===e)return new DayPilot.Event(a,l)}return null},this.events.forRange=function(e,t){return e=new DayPilot.Date(e),t=new DayPilot.Date(t),(l.events.list||[]).filter(function(n){return i(e,t,new DayPilot.Date(n.start),new DayPilot.Date(n.end))}).map(function(e){return new DayPilot.Event(e,l)})},this.events.update=function(e){if(e instanceof DayPilot.Event)e.commit();else if("object"==typeof e){var t=l.events.find(e.id);if(t){var n=DayPilot.indexOf(l.events.list,t.data);l.events.list.splice(n,1,e)}}l.Oa({"eventsOnly":!0}),l.S.notify()},this.events.remove=function(e){var t;if(e instanceof DayPilot.Event)t=e.data;else if("object"==typeof e){var n=l.events.find(e.id);n&&(t=n.data)}else if("string"==typeof e||"number"==typeof e){var n=l.events.find(e);n&&(t=n.data)}var i=DayPilot.indexOf(l.events.list,t);l.events.list.splice(i,1),l.Oa({"eventsOnly":!0}),l.S.notify()},this.events.load=function(e,t,n){var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var s={};return s.exception=e,void i(s)}if(DayPilot.isArray(n)){var o={};if(o.preventDefault=function(){this.preventDefault.value=!0},o.data=n,"function"==typeof t&&t(o),o.preventDefault.value)return;l.events.list=n,l.ga&&l.Oa({"eventsOnly":!0})}};if(l.eventsLoadMethod&&"POST"===l.eventsLoadMethod.toUpperCase())DayPilot.Http.ajax({"method":"POST","data":{"start":l.visibleStart().toString(),"end":l.visibleEnd().toString()},"url":e,"success":a,"error":i});else{var s=e,o="start="+l.visibleStart().toString()+"&end="+l.visibleEnd().toString();s+=s.indexOf("?")>-1?"&"+o:"?"+o,DayPilot.Http.ajax({"method":"GET","url":s,"success":a,"error":i})}},this.Pa=function(){if(l.nav.top.className!==l.b("_main")){l.nav.top.className=l.b("_main");var e=l.nav.corner;e.className=l.b("_corner"),e.firstChild.className=l.b("_corner_inner");var t=l.nav.cornerRight;t&&(t.className=l.b("_cornerright"),t.firstChild.className=l.b("_cornerright_inner"))}},this.update=function(e){if(l.i)throw new DayPilot.Exception("You are trying to update a DayPilot.Calendar instance that has been disposed.");l.Qa(e),l.Oa()},this.Oa=function(e){if(this.ga){var e=e||{},t=!e.eventsOnly;l.Ra(),l.q(),l.nav.top.style.cursor="auto",t&&(l.D(),l.G(),l.H(),l.I(),l.J(),l.Ba(),l.Ma(),l.Pa(),l.Ka()),l.E(),l.F(),l.K(),l.clearSelection(),this.visible?this.show():this.hide()}},this.Sa=null,this.Qa=function(e){if(e){var t={"events":{"preInit":function(){var e=this.data||[];DayPilot.isArray(e.list)?l.events.list=e.list:l.events.list=e}},"columns":{"preInit":function(){l.columns.list=this.data}}};this.Sa=t;for(var n in e)if(t[n]){var i=t[n];i.data=e[n],i.preInit&&i.preInit()}else l[n]=e[n]}},this.Ta=function(){var e=this.Sa;for(var t in e){var n=e[t];n.postInit&&n.postInit()}},this.Ua=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Calendar() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Calendar: The placeholder element not found: '"+e+"'."}},this.Va={},this.Va.events=[],this.Wa=function(e){var t=this.Va.events,n=this.events.list[e],i={};for(var a in n)i[a]=n[a];if(i.start=new DayPilot.Date(n.start),i.end=new DayPilot.Date(n.end),"function"==typeof this.onBeforeEventRender){var s={};s.control=l,s.data=i,this.onBeforeEventRender(s)}t[e]=i},this.E=function(){var e=this.events.list;if(l.Va.events=[],e){if(!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.Calendar.events.list expects an array object. You supplied: "+typeof e);var t=e.length,n=864e5;this.cache.pixels={};var i=[];this.scrollLabels=[],this.minStart=1e4,this.maxEnd=0;for(var a=0;a<t;a++){var s=e[a],o=s;if("object"!=typeof o)throw new DayPilot.Exception("Event data item must be an object");if(!o.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(!o.end)throw new DayPilot.Exception("Event data item must specify 'end' property");if(o instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Calendar: DayPilot.Event object detected in events.list array. Use raw event data instead.")}if("function"==typeof this.onBeforeEventRender)for(var a=0;a<t;a++)this.Wa(a);for(var a=0;a<this.X.length;a++){var r={};r.minEnd=1e6,r.maxStart=-1,this.scrollLabels.push(r);var h=this.X[a];h.events=[],h.lines=[],h.blocks=[];for(var c=new DayPilot.Date(h.start),d=c.getTime(),u=c.addTime(n),v=u.getTime(),f=0;f<t;f++)if(!i[f]){var s=e[f],p=new DayPilot.Date(s.start),m=new DayPilot.Date(s.end),g=p.getTime(),y=m.getTime();if(!(y<g)){var b=!(y<=d||g>=v);if("Resources"===l.viewType&&(b=b&&h.id===s.resource),b){var D=new DayPilot.Event(s,l);D.part.dayIndex=a,D.part.start=d<g?p:c,D.part.end=v>y?m:u;var C=this.getPixels(D.part.start,h.start),w=this.getPixels(D.part.end,h.start),x=C.top,k=w.top;if(x===k&&(C.cut||w.cut))continue;var P=w.boxBottom,S="Always"===l.useEventBoxes;S?(D.part.top=Math.floor(x/this.cellHeight)*this.cellHeight+1,D.part.height=Math.max(Math.ceil(P/this.cellHeight)*this.cellHeight-D.part.top,this.cellHeight-1)+1):(D.part.top=x+1,D.part.height=k-x+1),D.part.barTop=Math.max(x-D.part.top-1,0),D.part.barHeight=Math.max(k-x-2,1);var p=D.part.top,m=D.part.top+D.part.height;p>r.maxStart&&(r.maxStart=p),m<r.minEnd&&(r.minEnd=m),p<this.minStart&&(this.minStart=p),m>this.maxEnd&&(this.maxEnd=m),h.events.push(D),"function"==typeof this.onBeforeEventRender&&(D.cache=this.Va.events[f]),D.part.start.getTime()===g&&D.part.end.getTime()===y&&(i[f]=!0)}}}}for(var a=0;a<this.X.length;a++){var h=this.X[a];h.events.sort(this.Xa);for(var f=0;f<h.events.length;f++){var s=h.events[f];h.putIntoBlock(s)}for(var f=0;f<h.blocks.length;f++){var E=h.blocks[f];E.events.sort(this.Xa);for(var T=0;T<E.events.length;T++){var s=E.events[T];E.putIntoLine(s)}}}}},this.Xa=function(e,t){if(!(e&&t&&e.start&&t.start))return 0;var n=e.start().getTime()-t.start().getTime();return 0!==n?n:t.end().getTime()-e.end().getTime()},this.debug=function(e,t){this.debuggingEnabled&&(l.debugMessages||(l.debugMessages=[]),l.debugMessages.push(e),"undefined"!=typeof console&&console.log(e))},this.getPixels=function(e,t){t||(t=this.startDate);var n=t.getTime(),i=e.getTime(),a=o.B(),s=this.cache.pixels[i+"_"+n];if(s)return s;n=t.getTime();var l=60*a*1e3,r=i-n,h=r%l,c=r-h,d=c+l;0===h&&(d=c);var u={};return u.cut=!1,u.top=this.Ya(r),u.boxTop=this.Ya(c),u.boxBottom=this.Ya(d),this.cache.pixels[i+"_"+n]=u,u},this.Ya=function(e){return Math.floor(this.cellHeight*e/(6e4*o.B()))},this.Za=function(e){var t=Math.floor((shadowTop-_startOffset)/l.cellHeight);l.snapToGrid||(t=(shadowTop-_startOffset)/l.cellHeight);o.B()},this.Ra=function(){this.startDate=new DayPilot.Date(this.startDate).getDatePart()},this.F=function(){this.nav.corner&&(this.nav.corner.style.height=this.headerHeight+"px")},this.J=function(){var e=this.ta();this.nav.scroll&&e>0&&(this.nav.scroll.style.height=e+"px")},this.S={},this.S.scope=null,this.S.notify=function(){l.S.scope&&l.S.scope["$apply"]()},this.S.apply=function(e){e()},this.La=function(){if(l.nav.scroll&&l.$a()){var e=l.nav.scroll.scrollTop,t=o.B(),n=60/t,i=e/(n*l.cellHeight);l._a.scrollHour=i}},this.Ka=function(){var e=0,t=60/o.B();e="number"==typeof l._a.scrollHour?t*l.cellHeight*l._a.scrollHour:"Auto"===l.initScrollPos?"BusinessHours"===this.heightSpec?t*this.cellHeight*this.businessBeginsHour:0:this.initScrollPos;var n=l.nav.top;"none"===n.style.display?(n.style.display="",l.nav.scroll.scrollTop=e,n.style.display="none"):l.nav.scroll.scrollTop=e},this.getScrollY=function(){return l.nav.scroll.scrollTop},this.setScrollY=function(e){l.nav.scroll.scrollTop=e,l.La()},this.ab=function(){return!(!this.backendUrl&&"function"!=typeof WebForm_DoCallback)&&("undefined"==typeof l.events.list||!l.events.list)},this.C=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.show=function(){l.visible=!0,l.nav.top.style.display="",this.Ma()},this.hide=function(){l.visible=!1,l.nav.top.style.display="none"},this.bb=function(){this.Ra(),this.D(),this.sa(),this.G(),this.H(),this.Ma(),this.Ja(),this.Na(),t.register(this),this.cb(),this.m("Init")},this._a={},this.db=function(){this._a.themes=[],this._a.themes.push(this.theme||this.cssClassPrefix)},this.eb=function(){for(var e=this._a.themes,t=0;t<e.length;t++){var n=e[t];DayPilot.Util.removeClass(this.nav.top,n+"_main")}this._a.themes=[]},this.fb=function(){if(this.afterRender(null,!1),"function"==typeof this.onAfterRender){var e={};e.isCallBack=!1,this.onAfterRender(e)}},this.gb=function(){if("function"==typeof this.onInit&&!this.hb){this.hb=!0;var e={};this.onInit(e)}},this.$a=function(){var e=l.nav.top;return!!e&&(e.offsetWidth>0&&e.offsetHeight>0)},this.cb=function(){var e=l.$a;e()||(l.p=setInterval(function(){e()&&(l.Ja(),l.Ma(),clearInterval(l.p))},100))},this.wa=function(e,t){return l.w.z()?DayPilot.Util.escapeTextHtml(e,t):DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":e:t},this.ma={},this.ma.la=null,this.ma.ib=function(e,t,n){var i=l.ma.la;if("function"==typeof i.createVNode&&"function"==typeof i.render){var a=i.createVNode(e,n);i.render(a,t)}},this.ma.qa=function(e,t){var n=l.ma.la;if("function"==typeof n.render){var i=e;DayPilot.isArray(e)&&(i=n.h("div",null,e)),n.render(i,t)}},this.ma.oa=function(e){var t=l.ma.la;"function"==typeof t.render&&t.render(null,e)},this.internal={},this.internal.loadOptions=l.Qa,this.internal.xssTextHtml=l.wa,this.internal.enableVue=function(e){l.ma.la=e},this.internal.vueRef=function(){return l.ma.la},this.internal.vueRendering=function(){return l.ma.na},this.init=function(){this.Ua();var e=this.ab();return this.db(),e?void this.bb():(this.Ra(),this.D(),this.E(),this.sa(),this.G(),this.H(),this.C(),this.Ma(),this.Ja(),this.Na(),t.register(this),this.events&&(this.F(),this.K()),this.fb(),this.gb(),this.cb(),this.ga=!0,this)},this.Init=this.init,this.Qa(a)},DayPilot.CalendarColumn=function(e,t){var n=this;n.id=e.id,n.name=e.name,n.data=e.data,n.start=new DayPilot.Date(e.start),n.calendar=t,n.toJSON=function(){var e={};return e.id=this.id,this.start&&(e.start=this.start.toString()),e.name=this.name,e}},DayPilot.Calendar=t.Calendar,"undefined"!=typeof jQuery&&!function(e){e.fn.daypilotCalendar=function(e){var t=null,n=this.each(function(){if(!this.daypilot){var n=new DayPilot.Calendar(this.id);this.daypilot=n;for(name in e)n[name]=e[name];n.init(),t||(t=n)}});return 1===this.length?t:n}}(jQuery),function(){var e=DayPilot.am();e&&e.directive("daypilotCalendar",["$parse",function(e){return{"restrict":"E","template":"<div></div>","replace":!0,"link":function(t,n,i){var a=new DayPilot.Calendar(n[0]);a.S.scope=t,a.init();var s=i["id"];s&&(t[s]=a);var l=i["publishAs"];if(l){(0,e(l).assign)(t,a)}for(var o in i)0===o.indexOf("on")&&!function(n){a[n]=function(a){var s=e(i[n]);t["$apply"](function(){s(t,{"args":a})})}}(o);var r=t["$watch"],h=i["config"]||i["daypilotConfig"],c=i["events"]||i["daypilotEvents"];r.call(t,h,function(e){for(var t in e)a[t]=e[t];a.update(),a.gb()},!0),r.call(t,c,function(e){a.events.list=e,a.update()},!0)}}}])}()}}();