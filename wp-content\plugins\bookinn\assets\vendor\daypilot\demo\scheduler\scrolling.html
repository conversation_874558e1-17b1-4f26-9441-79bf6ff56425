﻿<!DOCTYPE html>
<html>
<head>
    <title>Scrolling (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">You can use the Scheduler API to scroll programmatically. Read more about the <a href="https://javascript.daypilot.org/scheduler/">JavaScript
        scheduler</a>.
    </div>

    <div class="tools">
        Scroll to: <a href="#" id="start">start</a>
        |
        <a href="#" id="now">now</a>
        |
        <a href="#" id="end">end</a>
    </div>

    <div id="dp"></div>

    <script type="text/javascript">


        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-01-01",
            days: 366,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMMM yyyy"},
                {groupBy: "Cell", format: "d"}
            ],
            heightSpec: "Max",
            height: 300,
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
                {name: "Room G", id: "G"},
                {name: "Room H", id: "H"},
                {name: "Room I", id: "I"},
                {name: "Room J", id: "J"},
                {name: "Room K", id: "K"}
            ],
            onTimeRangeSelected: args => {
                const name = window.prompt("New event name:", "Event");
                dp.clearSelection();
                if (!name) return;
                const e = new DayPilot.Event({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: name
                });
                dp.events.add(e);
            }
        });
        dp.init();
        dp.scrollTo("2025-03-25");

        const app = {
            elements: {
                start: document.querySelector("#start"),
                now: document.querySelector("#now"),
                end: document.querySelector("#end")
            },
            init() {
                this.loadEventData();
                this.addEventHandlers();
            },
            loadEventData() {
                const events = [];

                for (let i = 0; i < 15; i++) {
                    const duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const durationDays = Math.floor(Math.random() * 6); // 0 to 5
                    const start = Math.floor(Math.random() * 6) + 2; // 2 to 7
                    const res = Math.floor(Math.random() * 6); // 0 to 5

                    const event = {
                        start: new DayPilot.Date("2025-03-25T00:00:00").addDays(start),
                        end: new DayPilot.Date("2025-03-25T12:00:00").addDays(start).addDays(durationDays).addHours(duration),
                        id: DayPilot.guid(),
                        resource: String.fromCharCode(65 + i),
                        text: "Event"
                    };

                    events.push(event);
                }
                dp.update({events});
            },
            addEventHandlers() {
                this.elements.start.addEventListener("click", (ev) => {
                    ev.preventDefault();
                    dp.scrollTo("2025-01-01");
                });
                this.elements.now.addEventListener("click", (ev) => {
                    ev.preventDefault();
                    dp.scrollTo(DayPilot.Date.now());
                });
                this.elements.end.addEventListener("click", (ev) => {
                    ev.preventDefault();
                    dp.scrollTo("2025-12-31");
                });
            }
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

