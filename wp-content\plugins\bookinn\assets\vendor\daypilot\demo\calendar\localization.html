﻿<!DOCTYPE html>
<html>
<head>
    <title>Localization (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note"><b>Note:</b> Read more about the <a
        href="https://doc.daypilot.org/calendar/localization/">event calendar localization</a> [doc.daypilot.org].
    </div>

    <div class="tools">Select locale:
        <select id="locale">
            <option>ca-es</option>
            <option>cs-cz</option>
            <option>da-dk</option>
            <option>de-at</option>
            <option>de-ch</option>
            <option selected="selected">de-de</option>
            <option>de-lu</option>
            <option>en-au</option>
            <option>en-ca</option>
            <option>en-gb</option>
            <option>en-us</option>
            <option>es-es</option>
            <option>es-mx</option>
            <option>eu-es</option>
            <option>fi-fi</option>
            <option>fr-be</option>
            <option>fr-ch</option>
            <option>fr-fr</option>
            <option>fr-lu</option>
            <option>gl-es</option>
            <option>it-it</option>
            <option>it-ch</option>
            <option>ja-jp</option>
            <option>nb-no</option>
            <option>nl-nl</option>
            <option>nl-be</option>
            <option>nn-no</option>
            <option>pt-br</option>
            <option>pl-pl</option>
            <option>pt-pt</option>
            <option>ru-ru</option>
            <option>sk-sk</option>
            <option>sv-se</option>
            <option>uk-ua</option>
            <option>zh-cn</option>
        </select>
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Calendar("dp");

        // view
        dp.startDate = "2021-03-25";
        dp.viewType = "Week";
        dp.locale = "de-de";

        dp.headerDateFormat = "dddd";

        // event creating
        dp.onTimeRangeSelected = function (args) {
            var name = prompt("New event name:", "Event");
            if (!name) return;
            var e = new DayPilot.Event({
                start: args.start,
                end: args.end,
                id: DayPilot.guid(),
                text: name
            });
            dp.events.add(e);
            dp.clearSelection();
        };

        dp.onEventClick = function (args) {
            alert("clicked: " + args.e.id());
        };

        dp.init();


        const app = {
            elements: {
                locale: document.querySelector("#locale")
            },
            init() {
                this.addEventHandlers();
                this.loadEventData();
            },
            addEventHandlers() {
                app.elements.locale.addEventListener("change", function() {
                    const locale = app.elements.locale.value;
                    dp.update({locale});
                });
            },
            loadEventData() {
                const events = [
                    {
                        start: new DayPilot.Date("2021-03-25T12:00:00"),
                        end: new DayPilot.Date("2021-03-25T12:00:00").addHours(3),
                        id: DayPilot.guid(),
                        text: "Special event"
                    }
                ];
                dp.events.list = events;
                dp.update();
            }
        };
        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

