﻿<!DOCTYPE html>
<html>
<head>
    <title>Localization (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">Read more about the <a href="https://doc.daypilot.org/scheduler/localization/">scheduler
        localization</a> [doc.daypilot.org].
    </div>

    <div class="tools">Select locale:
        <select id="locale">
            <option>ca-es</option>
            <option>cs-cz</option>
            <option>da-dk</option>
            <option>de-at</option>
            <option>de-ch</option>
            <option selected="selected">de-de</option>
            <option>de-lu</option>
            <option>en-au</option>
            <option>en-ca</option>
            <option>en-gb</option>
            <option>en-us</option>
            <option>es-es</option>
            <option>es-mx</option>
            <option>eu-es</option>
            <option>fi-fi</option>
            <option>fr-be</option>
            <option>fr-ca</option>
            <option>fr-ch</option>
            <option>fr-fr</option>
            <option>fr-lu</option>
            <option>gl-es</option>
            <option>it-it</option>
            <option>it-ch</option>
            <option>ja-jp</option>
            <option>ko-kr</option>
            <option>nb-no</option>
            <option>nl-nl</option>
            <option>nl-be</option>
            <option>nn-no</option>
            <option>pt-br</option>
            <option>pl-pl</option>
            <option>pt-pt</option>
            <option>ro-ro</option>
            <option>ru-ru</option>
            <option>sk-sk</option>
            <option>sv-se</option>
            <option>tr-tr</option>
            <option>uk-ua</option>
            <option>zh-cn</option>
            <option>zh-tw</option>
        </select>
    </div>


    <div style="display: flex;">
        <div style="margin-right: 10px;">
            <div id="nav"></div>
        </div>
        <div style="flex-grow: 1;">
            <div id="dp"></div>
        </div>
    </div>


    <script type="text/javascript">
        const nav = new DayPilot.Navigator("nav", {
            showMonths: 1,
            selectMode: "month",
            locale: "de-de",
            timeRangeSelectedHandling: "JavaScript",
            onTimeRangeSelected: args => {
                console.log("timeRangeSelected");
                dp.startDate = args.start;
                dp.days = DayPilot.DateUtil.daysDiff(args.start, args.end);
                dp.update();
            }
        });
        nav.init();

        const dp = new DayPilot.Scheduler("dp", {
            locale: "de-de",
            timeHeaders: [{groupBy: 'Month'}, {groupBy: 'Week'}, {groupBy: 'Cell'}],
            startDate: DayPilot.Date.today().firstDayOfMonth(),
            days: DayPilot.Date.today().daysInMonth(),
            scale: "Day",
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"}
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                const name = modal.result;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: name
                });
            }
        });
        dp.init();

        const app = {
            init() {
                this.addEventHandlers();
                this.loadEventData();
            },
            addEventHandlers() {
                document.querySelector("#locale").addEventListener("change", function(ev) {
                    const locale = this.value;

                    dp.update({locale});
                    nav.update({locale});
                });
            },
            loadEventData() {
                const events = [];

                for (var i = 0; i < 10; i++) {
                    var duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    var start = Math.floor(Math.random() * 6) - 3; // -3 to 3

                    var e = {
                        start: new DayPilot.Date("2025-03-25T00:00:00").addHours(start),
                        end: new DayPilot.Date("2025-03-25T12:00:00").addHours(start).addHours(duration),
                        id: DayPilot.guid(),
                        resource: "A",
                        text: "Event"
                    };
                    events.push(e);
                }

                dp.update({events});
            }
        };
        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

