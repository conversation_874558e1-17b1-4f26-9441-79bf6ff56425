﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";if("undefined"==typeof DayPilot.Navigator||!DayPilot.Navigator.def){var t=DayPilot.Util.isVueVNode;DayPilot.Navigator=function(e,i){this.v="2025.3.696-lite";var s=this;this.id=e,this.api=2,this.isNavigator=!0,this.autoFocusOnClick=!0,this.weekStarts="Auto",this.selectMode="Day",this.titleHeight=30,this.dayHeaderHeight=30,this.bound=null,this.cellWidth=30,this.cellHeight=30,this.cssClassPrefix="navigator_default",this.freeHandSelectionEnabled=!1,this.selectionStart=(new DayPilot.Date).getDatePart(),this.selectionEnd=null,this.selectionDay=null,this.showMonths=1,this.skipMonths=1,this.command="navigate",this.year=(new DayPilot.Date).getYear(),this.month=(new DayPilot.Date).getMonth()+1,this.showToday=!1,this.showWeekNumbers=!1,this.todayHtml=null,this.todayHeight=40,this.todayPosition="Bottom",this.todayText="Today",this.weekNumberAlgorithm="Auto",this.rowsPerMonth="Six",this.orientation="Vertical",this.locale="en-us",this.rtl=!1,this.visible=!0,this.timeRangeSelectedHandling="Bind",this.visibleRangeChangedHandling="Enabled",this.onVisibleRangeChange=null,this.onVisibleRangeChanged=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onTodayClick=null,this.nav={},this.a={},this.b=function(){this.root.dp=this,this.root.className=this.c("_main"),"Horizontal"===this.orientation?(o.d()||(this.root.style.width=this.showMonths*(7*o.cellWidth()+this.e())+"px"),this.root.style.height=6*this.cellHeight+this.titleHeight+this.dayHeaderHeight+"px"):o.d()||(this.root.style.width=7*o.cellWidth()+this.e()+"px"),this.rtl&&(this.root.style.direction="rtl"),this.root.style.position="relative",this.visible||(this.root.style.display="none");var t=document.createElement("input");t.type="hidden",t.name=s.id+"_state",t.id=t.name,this.root.appendChild(t),this.state=t,this.startDate?this.startDate=new DayPilot.Date(this.startDate).firstDayOfMonth():this.selectionDay?this.startDate=new DayPilot.Date(this.selectionDay).firstDayOfMonth():this.startDate=DayPilot.Date.fromYearMonthDay(this.year,this.month),this.calendars=[],this.selected=[],this.months=[]},this.f=function(){return 2===s.api},this.g=function(){this.root.innerHTML=""},this.c=function(t){var e=this.theme||this.cssClassPrefix;return e?e+t:""},this.i=function(t,e){var i=this.c("_"+e);DayPilot.Util.addClass(t,i)},this.j=function(t,e){var i=this.c("_"+e);DayPilot.Util.removeClass(t,i)},this.k=function(){if(!o.d())return null;var t=7;return this.showWeekNumbers&&t++,100/t},this.l=function(){return o.d()?null:o.cellWidth()},this.m=function(t){return"number"!=typeof t&&(t=1),o.d()?this.k()*t:this.l()*t},this.n=function(t){var e=o.d()?"%":"px";return this.m(t)+e},this.o=function(e,i){var n={};n.cells=[],n.days=[],n.weeks=[];var a=this.startDate.addMonths(e),l=i.before,h=i.after,r=a.firstDayOfMonth(),c=r.firstDayOfWeek(o.weekStarts()),d=r.addMonths(1),u=DayPilot.DateUtil.daysDiff(c,d),f="Auto"===this.rowsPerMonth?Math.ceil(u/7):6;n.rowCount=f;var y=(new DayPilot.Date).getDatePart(),p=7*o.cellWidth()+this.e();n.width=p;var v=this.cellHeight*f+this.titleHeight+this.dayHeaderHeight;n.height=v;var m=document.createElement("div");if(o.d()?"Horizontal"===this.orientation&&(m.style.width=100/s.showMonths+"%"):m.style.width=p+"px",m.style.height=v+"px","Horizontal"===this.orientation)m.style.position="absolute",o.d()?(m.style.left=100/s.showMonths*e+"%",n.leftPct=100/s.showMonths*e):m.style.left=p*e+"px",m.style.top="0px",n.top=0,n.left=p*e;else{m.style.position="relative";var g=e>0?s.months[e-1].top+s.months[e-1].height:0;n.top=g,n.left=0,n.leftPct=0}m.className=this.c("_month"),m.style.cursor="default",m.style.userSelect="none",m.style.webkitUserSelect="none",m.month=n,n.div=m,this.root.appendChild(m);var b=this.titleHeight+this.dayHeaderHeight,w=document.createElement("div");w.style.position="absolute",w.style.left="0px",w.style.right="0px",w.style.top="0px",w.style.width=s.n(),w.style.height=this.titleHeight+"px",w.style.lineHeight=this.titleHeight+"px",w.className=this.c("_titleleft"),i.left&&(w.style.cursor="pointer",w.innerHTML="<span>&lt;</span>",w.onclick=this.p),m.appendChild(w),this.tl=w;var x=document.createElement("div");x.style.position="absolute",x.style.left=s.n(),x.style.top="0px",x.style.width=s.n(s.showWeekNumbers?6:5),x.style.height=this.titleHeight+"px",x.style.lineHeight=this.titleHeight+"px",x.className=this.c("_title"),x.innerHTML=o.locale().monthNames[a.getMonth()]+" "+a.getYear(),m.appendChild(x),this.ti=x;var D=document.createElement("div");D.style.position="absolute",D.style.left=s.n(s.showWeekNumbers?7:6),D.style.right=s.n(s.showWeekNumbers?7:6),D.style.top="0px",D.style.width=s.n(),D.style.height=this.titleHeight+"px",D.style.lineHeight=this.titleHeight+"px",D.className=this.c("_titleright"),i.right&&(D.style.cursor="pointer",D.innerHTML="<span>&gt;</span>",D.onclick=this.q),m.appendChild(D),this.tr=D;var k=(this.e(),s.showWeekNumbers?1:0);if(this.showWeekNumbers)for(var S=0;S<f;S++){var H=c.addDays(7*S),C=null;switch(this.weekNumberAlgorithm){case"Auto":C=1===o.weekStarts()?H.weekNumberISO():H.weekNumber();break;case"US":C=H.weekNumber();break;case"ISO8601":C=H.weekNumberISO();break;default:throw"Unknown weekNumberAlgorithm value."}var M=document.createElement("div");M.style.position="absolute",M.style.left="0px",M.style.right="0px",M.style.top=S*this.cellHeight+b+"px",M.style.width=s.n(),M.style.height=this.cellHeight+"px",M.style.lineHeight=this.cellHeight+"px",M.className=this.c("_weeknumber"),M.innerHTML="<span>"+C+"</span>",m.appendChild(M),n.weeks.push(M)}if(s.showWeekNumbers){var M=document.createElement("div");M.style.position="absolute",M.style.left="0px",M.style.right="0px",M.style.top=this.titleHeight+"px",M.style.width=s.n(),M.style.height=this.dayHeaderHeight+"px",M.className=this.c("_dayheader"),m.appendChild(M)}for(var N=0;N<7;N++){n.cells[N]=[];var M=document.createElement("div");M.style.position="absolute",M.style.left=s.n(N+k),M.style.right=s.n(N+k),M.style.top=this.titleHeight+"px",M.style.width=s.n(),M.style.height=this.dayHeaderHeight+"px",M.style.lineHeight=this.dayHeaderHeight+"px",M.className=this.c("_dayheader"),M.innerHTML="<span>"+this.r(N)+"</span>",m.appendChild(M),n.days.push(M);for(var S=0;S<f;S++){var H=c.addDays(7*S+N),E=this.s(H)&&"none"!==this.t(),T=H.firstDayOfMonth()===a,B=H<a,O=H>=a.addMonths(1);if("month"===this.t())E=E&&T;else if("day"===this.t())E=E&&(T||l&&B||h&&O);else if("week"===this.t()){var P=H.firstDayOfMonth()===a;E=E&&(P||l&&B||h&&O)}var W=document.createElement("div");n.cells[N][S]=W;var R=s.u(N,S),A=R.x,_=R.y,V=o.d()?"%":"px";W.day=H,W.x=N,W.y=S,W.left=A,W.top=_,W.isCurrentMonth=T,W.isNextMonth=O,W.isPrevMonth=B,W.showBefore=l,W.showAfter=h,W.className=this.c(T?"_day":"_dayother"),s.i(W,"cell"),H.getTime()===y.getTime()&&T&&this.i(W,"today"),0!==H.dayOfWeek()&&6!==H.dayOfWeek()||this.i(W,"weekend"),W.style.position="absolute",W.style.left=A+V,W.style.right=A+V,W.style.top=_+"px",W.style.width=s.n(),W.style.height=this.cellHeight+"px",W.style.lineHeight=this.cellHeight+"px";var L=document.createElement("div");L.style.position="absolute",L.className=H.getTime()===y.getTime()&&T?this.c("_todaybox"):this.c("_daybox"),s.i(L,"cell_box"),L.style.left="0px",L.style.top="0px",L.style.right="0px",L.style.bottom="0px",W.appendChild(L);var I=null;this.cells&&this.cells[H.toStringSortable()]&&(I=this.cells[H.toStringSortable()]);var j=I||{};if(j.day=H,j.isCurrentMonth=T,j.isToday=H.getTime()===y.getTime()&&T,j.isWeekend=0===H.dayOfWeek()||6===H.dayOfWeek(),I?(j.html=I.html||H.getDay(),j.cssClass=I.css):(j.html=H.getDay(),j.cssClass=null),"function"==typeof s.onBeforeCellRender){var U={};U.cell=j,s.onBeforeCellRender(U),I=U.cell}if(I&&DayPilot.Util.addClass(W,I.cssClass||I.css),T||l&&B||h&&O){var J=document.createElement("div");J.innerHTML=H.getDay(),J.style.position="absolute",J.style.left="0px",J.style.top="0px",J.style.right="0px",J.style.bottom="0px",s.i(J,"cell_text"),W.isClickable=!0,I&&I.html&&(J.innerHTML=I.html),W.appendChild(J)}W.onclick=this.w,function(){if("function"==typeof s.onBeforeCellDomAdd||"function"==typeof s.onBeforeCellDomRemove){var e={};if(e.control=s,e.cell=j,e.element=null,W.domArgs=e,"function"==typeof s.onBeforeCellDomAdd&&s.onBeforeCellDomAdd(e),e.element){var i=L;if(i){e.z=i;if(t(e.element)){if(!s.B.A)throw new DayPilot.Exception("Can't reach Vue");s.B.C=!0,s.B.D(e.element,i),s.B.C=!1}else i.appendChild(e.element)}}}}(),m.appendChild(W),E&&(s.E(m,N,S),this.selected.push(W))}}var z=document.createElement("div");z.style.position="absolute",z.style.left="0px",z.style.top=b-2+"px",z.style.width=s.n(7+k),z.style.height="1px",z.style.fontSize="1px",z.style.lineHeight="1px",z.className=this.c("_line"),m.appendChild(z),this.months.push(n)},this.u=function(t,e){var i=this.titleHeight+this.dayHeaderHeight,n=(this.e(),this.showWeekNumbers?1:0);return{"x":s.m(t+n),"y":e*this.cellHeight+i}},this.E=function(t,e,i){var n=t.month.cells[e][i];s.i(n,"select")},this.F=function(t,e,i){var n=t.month.cells[e][i];s.j(n,"select")},this.e=function(){return this.showWeekNumbers?o.cellWidth():0},this.G=function(){if(this.items)for(var t=0;t<this.showMonths;t++)for(var e=0;e<7;e++)for(var i=0;i<6;i++){var s=this.months[t].cells[e][i];s&&(1===this.items[s.day.toStringSortable()]?(this.i(s,"busy"),this.j(s,"free")):(this.j(s,"busy"),this.i(s,"free")))}},this.H=function(){var t={};t.startDate=s.startDate,t.selectionStart=s.selectionStart,t.selectionEnd=s.selectionEnd.addDays(1),s.state.value=JSON.stringify(t)},this.t=function(){return(this.selectMode||"").toLowerCase()},this.I=function(){var t=this.selectionDay||this.selectionStart;switch(t||(t=DayPilot.Date.today()),t=new DayPilot.Date(t),this.t()){case"day":this.selectionStart=t,this.selectionDay=t,this.selectionEnd=t;break;case"week":this.selectionDay=t,this.selectionStart=t.firstDayOfWeek(o.weekStarts()),this.selectionEnd=this.selectionStart.addDays(6);break;case"month":this.selectionDay=t,this.selectionStart=t.firstDayOfMonth(),this.selectionEnd=this.selectionStart.lastDayOfMonth();break;case"none":this.selectionEnd=t;break;default:throw"Unknown selectMode value."}},this.J=null,this.select=function(t,e,i){var n=e&&(e instanceof DayPilot.Date||"string"==typeof e),a=e&&"object"==typeof e||"boolean"==typeof e,o=t,l=n?e:null,h=a?e:i;if(!this.K)return void(this.J={"date1":o,"date2":l,"options":h});var r=!0,c=!0;h&&"object"==typeof h?(h.dontFocus&&(r=!1),h.dontNotify&&(c=!1)):"boolean"==typeof h&&(r=!h);var d=this.selectionStart,u=this.selectionEnd;this.selectionStart=new DayPilot.Date(o).getDatePart(),this.selectionDay=this.selectionStart;var f=!1;if(r){var y=this.startDate;(this.selectionStart<this.L()||this.selectionStart>=this.M())&&(y=this.selectionStart.firstDayOfMonth()),y.toStringSortable()!==this.startDate.toStringSortable()&&(f=!0),this.startDate=y}l&&s.freeHandSelectionEnabled?s.selectionEnd=new DayPilot.Date(l):this.I(),this.g(),this.b(),this.N(),this.G(),this.H(),!c||d.equals(this.selectionStart)&&u.equals(this.selectionEnd)||this.O(),f&&this.P()},this.update=function(t){s.Q(t)},this.Q=function(t){if(s.R(t),this.K){if(s.S)throw new DayPilot.Exception("You are trying to update a DayPilot.Navigator instance that has been disposed.");s.T();var e={"day":s.selectionDay,"start":s.selectionStart,"end":s.selectionEnd};s.U(),e.start===s.selectionStart&&e.end===s.selectionEnd&&e.day===s.selectionDay||s.O()}},this.U=function(){this.g(),this.b(),this.I(),this.N(),this.V(),this.G(),this.H(),this.visible?this.show():this.hide()},this.T=function(){s.a={}},this.W=null,this.R=function(t){if(t){var e={"events":{"preInit":function(){var t=this.data;t&&(DayPilot.isArray(t.list)?s.events.list=t.list:s.events.list=t)}}};this.W=e;for(var i in t)if(e[i]){var n=e[i];n.data=t[i],n.preInit&&n.preInit()}else s[i]=t[i]}},this.X=function(){var t=this.W;for(var e in t){var i=t[e];i.postInit&&i.postInit()}},this.Y=function(t,e,i){var s={};s.action=t,s.parameters=i,s.data=e,s.header=this.Z();var n="JSON"+JSON.stringify(s);this.backendUrl?DayPilot.request(this.backendUrl,this.$,n,this._):WebForm_DoCallback(this.uniqueID,n,this.aa,null,this.callbackError,!0)},this._=function(t){if("function"==typeof s.onAjaxError){var e={};e.request=t,s.onAjaxError(e)}else"function"==typeof s.ajaxError&&s.ajaxError(t)},this.$=function(t){s.aa(t.responseText)},this.ba=function(t,e,i){var n={};n.action=t,n.parameters=i,n.data=e,n.header=this.Z();var a="JSON"+JSON.stringify(n);__doPostBack(s.uniqueID,a)},this.Z=function(){var t={};return t.v=this.v,t.startDate=this.startDate,t.selectionStart=this.selectionStart,t.showMonths=this.showMonths,t},this.ca=function(t,e){"refresh"===t&&this.P()},this.r=function(t){var e=t+o.weekStarts();return e>6&&(e-=7),o.locale().dayNamesShort[e]},this.s=function(t){return null!==this.selectionStart&&null!==this.selectionEnd&&(this.selectionStart.getTime()<=t.getTime()&&t.getTime()<=this.selectionEnd.getTime())},this.da=function(t){for(var e=0;e<s.months.length;e++){var i=s.months[e];if(!i)return null;if(t.x<i.left)return null;if(!(i.left+i.width<t.x)){s.months[e].height;if(i.top<=t.y&&t.y<i.top+i.height)return e}}return null},this.ea=function(t){},this.fa=function(){if(!o.d())return void(s.ga=s.cellWidth);var t=s.months[0].cells[0][0],e=t.clientWidth;s.ga=e,s.months.forEach(function(t){t.width=t.div.clientWidth,"Horizontal"===s.orientation&&(t.left=t.div.offsetLeft),t.cells.forEach(function(t,i){t.forEach(function(t,s){t.width=e,t.left=i*e})})})},this.ha=function(t){s.fa();var e=DayPilot.mo3(s.nav.top,t),i=s.da(e);if(null===i)return null;var n=s.months[i],a=this.titleHeight+this.dayHeaderHeight;if(n.top<=e.y&&e.y<n.top+a)return{"month":i,"x":0,"y":0,"coords":e,"header":!0};for(var o=0;o<n.cells.length;o++)for(var l=0;l<n.cells[o].length;l++){var h=n.cells[o][l],r=h.top+n.top,c=h.left+n.left;if(c<=e.x&&e.x<c+s.ga&&r<=e.y&&e.y<r+s.cellHeight)return{"month":i,"x":o,"y":l,"coords":e}}return null},this.ia=function(t){if(s.freeHandSelectionEnabled){var e=s.ha(t);e&&!e.header&&(n.start=e),s.months[e.month].cells[e.x][e.y],t.preventDefault()}},this.ja=function(t){if(n.start){var e=s.ha(t);if(n.end)n.end=e;else if(e){var i=3,a=DayPilot.distance(n.start.coords,e.coords);a>i&&(n.end=e)}n.end&&(n.clear(),n.draw())}},this.ka={};var n=this.ka;n.start=null,n.drawCell=function(t){var e=s.months[t.month],i=s.u(t.x,t.y),a=e.top+i.y,l=e.left+i.x,h="px",r=s.n();if(o.d()){var c="Horizontal"===s.orientation?s.showMonths:1;l=e.leftPct+i.x/c,h="%",r=s.n(1/c)}var d=document.createElement("div");d.style.position="absolute",d.style.left=l+h,d.style.top=a+"px",d.style.height=s.cellHeight+"px",d.style.width=r,d.style.backgroundColor="#ccc",d.style.opacity=.5,d.style.cursor="default",s.nav.preselection.appendChild(d),n.cells.push(d)},n.clear=function(){if(n.cells){for(var t=0;t<n.cells.length;t++)s.nav.preselection.removeChild(n.cells[t]);n.cells=[]}},n.draw=function(){var t=n.ordered(),e=new a(t.start),i=t.end;if(i){if(i===n.end&&i.header&&i.month>0){i.month-=1;var o=s.months[i.month];i.x=6,i.y=o.rowCount-1}for(n.cells=[];!e.is(i);){e.visible()&&n.drawCell(e);var l=new a(e).next();if(!l)return;e.month=l.month,e.x=l.x,e.y=l.y}e.visible()&&n.drawCell(e)}},n.ordered=function(){var t=n.start,e=n.end,i={};return!e||new a(t).before(e)?(i.start=t,i.end=e):(i.start=e,i.end=t),i};var a=function(t,e,i){if(t instanceof a)return t;if("object"==typeof t){var n=t;this.month=n.month,this.x=n.x,this.y=n.y}else this.month=t,this.x=e,this.y=i;this.is=function(t){return this.month===t.month&&this.x===t.x&&this.y===t.y},this.next=function(){var t=this;if(t.x<6)return{"month":t.month,"x":t.x+1,"y":t.y};var e=s.months[t.month];return t.y<e.rowCount-1?{"month":t.month,"x":0,"y":t.y+1}:t.month<s.months.length-1?{"month":t.month+1,"x":0,"y":0}:null},this.visible=function(){var t=this.cell();return!!t.isCurrentMonth||(!(!t.isPrevMonth||!t.showBefore)||!(!t.isNextMonth||!t.showAfter))},this.nextVisible=function(){for(var t=this;!t.visible();){var e=t.next();if(!e)return null;t=new a(e)}return t},this.previous=function(){var t=this;if(t.x>0)return{"month":t.month,"x":t.x-1,"y":t.y};s.months[t.month];if(t.y>0)return{"month":t.month,"x":6,"y":t.y-1};if(t.month>0){var e=s.months[t.month-1];return{"month":t.month-1,"x":6,"y":e.rowCount-1}}return null},this.previousVisible=function(){for(var t=this;!t.visible();){var e=t.previous();if(!e)return null;t=new a(e)}return t},this.cell=function(){return s.months[this.month].cells[this.x][this.y]},this.date=function(){return this.cell().day},this.before=function(t){return this.date()<new a(t).date()}};this.w=function(t){var e=this.parentNode,i=this.parentNode.month,n=this.x,a=this.y,o=i.cells[n][a].day;if(i.cells[n][a].isClickable){s.clearSelection(),s.selectionDay=o;var o=s.selectionDay;switch(s.t()){case"none":s.selectionStart=o,s.selectionEnd=o;break;case"day":if(s.autoFocusOnClick){var l=o;if(o<s.L()||o>=s.M())return void s.select(o)}var h=i.cells[n][a];s.E(e,n,a),s.selected.push(h),s.selectionStart=h.day,s.selectionEnd=h.day;break;case"week":if(s.autoFocusOnClick){var l=i.cells[0][a].day,r=i.cells[6][a].day;if(l.firstDayOfMonth()===r.firstDayOfMonth()&&(l<s.L()||r>=s.M()))return void s.select(o)}for(var c=0;c<7;c++)s.E(e,c,a),s.selected.push(i.cells[c][a]);s.selectionStart=i.cells[0][a].day,s.selectionEnd=i.cells[6][a].day;break;case"month":if(s.autoFocusOnClick){var l=o;if(o<s.L()||o>=s.M())return void s.select(o)}for(var l=null,r=null,a=0;a<6;a++)for(var n=0;n<7;n++){var h=i.cells[n][a];h&&h.day.getYear()===o.getYear()&&h.day.getMonth()===o.getMonth()&&(s.E(e,n,a),s.selected.push(h),null===l&&(l=h.day),r=h.day)}s.selectionStart=l,s.selectionEnd=r;break;default:throw"unknown selectMode"}s.H(),s.O()}},this.O=function(t){var e=s.selectionStart,i=s.selectionEnd.addDays(1),n=DayPilot.DateUtil.daysDiff(e,i),a=s.selectionDay;if(t=t||{},s.f()){var o={};if(o.start=e,o.end=i,o.day=a,o.days=n,o.mode=t.mode||s.selectMode,o.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof s.onTimeRangeSelect&&(s.onTimeRangeSelect(o),o.preventDefault.value))return;switch(s.timeRangeSelectedHandling){case"Bind":if("object"==typeof bound){var l={};l.start=e,l.end=i,l.days=n,l.day=a,bound.commandCallBack(s.command,l)}break;case"None":break;case"PostBack":s.timeRangeSelectedPostBack(e,i,a)}"function"==typeof s.onTimeRangeSelected&&s.onTimeRangeSelected(o)}else switch(s.timeRangeSelectedHandling){case"Bind":if("object"==typeof bound){var l={};l.start=e,l.end=i,l.days=n,l.day=a,bound.commandCallBack(s.command,l)}break;case"JavaScript":s.onTimeRangeSelected(e,i,a);break;case"None":break;case"PostBack":s.timeRangeSelectedPostBack(e,i,a)}},this.timeRangeSelectedPostBack=function(t,e,i,s){var n={};n.start=t,n.end=e,n.day=s,this.ba("TimeRangeSelected",i,n)},this.q=function(t){s.la(s.skipMonths)},this.p=function(t){s.la(-s.skipMonths)},this.la=function(t){this.startDate=this.startDate.addMonths(t),this.g(),this.b(),this.N(),this.H(),this.P(),this.G()},this.L=function(){return s.startDate.firstDayOfMonth()},this.M=function(){return s.startDate.firstDayOfMonth().addMonths(this.showMonths)},this.visibleStart=function(){return s.startDate.firstDayOfMonth().firstDayOfWeek(o.weekStarts())},this.visibleEnd=function(){return s.startDate.firstDayOfMonth().addMonths(this.showMonths-1).firstDayOfWeek(o.weekStarts()).addDays(42)},this.P=function(){var t=this.visibleStart(),e=this.visibleEnd();if(s.f()){var i={};if(i.start=t,i.end=e,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof s.onVisibleRangeChange&&(s.onVisibleRangeChange(i),i.preventDefault.value))return;switch(this.visibleRangeChangedHandling){case"CallBack":this.visibleRangeChangedCallBack(null);break;case"PostBack":this.visibleRangeChangedPostBack(null);break;case"Disabled":}"function"==typeof s.onVisibleRangeChanged&&s.onVisibleRangeChanged(i)}else switch(this.visibleRangeChangedHandling){case"CallBack":this.visibleRangeChangedCallBack(null);break;case"PostBack":this.visibleRangeChangedPostBack(null);break;case"JavaScript":this.onVisibleRangeChanged(t,e);break;case"Disabled":}},this.visibleRangeChangedCallBack=function(t){var e={};this.Y("Visible",t,e)},this.visibleRangeChangedPostBack=function(t){var e={};this.ba("Visible",t,e)},this.aa=function(t,e){var t=JSON.parse(t);s.items=t.Items,s.cells=t.Cells,s.cells?s.update():s.G()},this.N=function(){this.showToday&&"Top"===this.todayPosition&&this.ma();for(var t=0;t<this.showMonths;t++){var e=this.na(t);this.o(t,e)}this.showToday&&"Bottom"===this.todayPosition&&this.ma(),this.root.style.height=this.oa()+"px",this.nav.preselection=document.createElement("div"),this.nav.preselection.style.position="absolute",this.nav.preselection.style.left="0px",this.nav.preselection.style.right="0px",this.nav.preselection.style.top="0px",this.root.appendChild(this.nav.preselection)},this.ma=function(){if(this.showToday){var t=document.createElement("span");t.className=this.c("_todaysection_button"),this.todayHtml?t.innerHTML=this.todayHtml:t.innerText=this.todayText,t.onclick=function(){if("function"==typeof s.onTodayClick){var t={};if(t.preventDefault=function(){this.preventDefault.value=!0},s.onTodayClick(t),t.preventDefault.value)return}s.select(DayPilot.Date.today())};var e=document.createElement("div");e.style.height=this.todayHeight+"px",e.className=this.c("_todaysection"),e.appendChild(t),this.root.appendChild(e)}},this.oa=function(){var t=0;if(this.showToday&&(t+=this.todayHeight),"Horizontal"===this.orientation){for(var e=0;e<this.months.length;e++){var i=this.months[e];i.height>t&&(t=i.height)}return t}for(var e=0;e<this.months.length;e++){var i=this.months[e];t+=i.height}return t},this.na=function(t){if(this.internal.showLinks)return this.internal.showLinks;var e={};return e.left=0===t,e.right=0===t,e.before=0===t,e.after=t===this.showMonths-1,"Horizontal"===this.orientation&&(e.right=t===this.showMonths-1),e},this.pa={},this.pa.scope=null,this.pa.notify=function(){s.pa.scope&&s.pa.scope["$apply"]()},this.B={},this.B.A=null,this.B.qa=function(t,e,i){var n=s.B.A;if("function"==typeof n.createVNode&&"function"==typeof n.render){var a=n.createVNode(t,i);n.render(a,e)}},this.B.D=function(t,e){var i=s.B.A;if("function"==typeof i.render){var n=t;DayPilot.isArray(t)&&(n=i.h("div",null,t)),i.render(n,e)}},this.B.ra=function(t){var e=s.B.A;"function"==typeof e.render&&e.render(null,t)},this.internal={},this.internal.loadOptions=s.R,this.internal.initialized=function(){return s.K},this.internal.enableVue=function(t){s.B.A=t},this.internal.vueRef=function(){return s.B.A},this.internal.vueRendering=function(){return s.B.C},this.internal.upd=function(t){s.Q(t)},this.sa={};var o=this.sa;o.locale=function(){return DayPilot.Locale.find(s.locale)},o.weekStarts=function(){if("Auto"===s.weekStarts){var t=o.locale();return t?t.weekStarts:0}return s.weekStarts},o.cellWidth=function(){if(s.a.cellWidth)return s.a.cellWidth;var t=s.ta("_cell_dimensions").width;return t||(t=s.cellWidth),s.a.cellWidth=t,t},o.d=function(){return"Auto"===s.sa.cellWidth()},this.clearSelection=function(){for(var t=0;t<this.selected.length;t++){var e=this.selected[t];s.F(e.parentNode,e.x,e.y)}this.selected=[]},this.ua=function(){return!!this.backendUrl&&("undefined"==typeof s.items||!s.items)},this.events={},this.V=function(){if(DayPilot.isArray(this.events.list)){this.items={};for(var t=0;t<this.events.list.length;t++){var e=this.events.list[t];if(!e.hidden){var i=this.va(e);for(var s in i)this.items[s]=1}}}},this.ta=function(t){var e=document.createElement("div");e.style.position="absolute",e.style.top="-2000px",e.style.left="-2000px",e.className=this.c(t);var i=s.root||document.body;i.appendChild(e);var n=e.offsetHeight,a=e.offsetWidth;i.removeChild(e);var o={};return o.height=n,o.width=a,o},this.va=function(t){for(var e=new DayPilot.Date(t.start),i=new DayPilot.Date(t.end),s={},n=e.getDatePart();n.getTime()<=i.getTime();)s[n.toStringSortable()]=1,n=n.addDays(1);return s},this.show=function(){s.visible=!0,s.root.style.display=""},this.hide=function(){s.visible=!1,s.root.style.display="none"},this.wa=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Navigator() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Navigator: The placeholder element not found: '"+e+"'."}this.root=this.nav.top},this.init=function(){if(this.wa(),!this.root.dp){this.I(),this.b(),this.N(),this.V(),this.G(),this.xa(),this.ya(),this.za();if(this.ua()&&this.P(),this.K=!0,this.X(),this.J){var t=this.J;this.select(t.date1,t.date2,t.options),this.J=null}return this}},this.ya=function(){s.nav.top.onmousedown=this.ia,s.nav.top.onmousemove=this.ja},this.za=function(){DayPilot.re(document,"mouseup",s.Aa)},this.Aa=function(t){if(n.start&&n.end){var e=DayPilot.mo3(s.nav.top,t);if(e.x===n.start.coords.x&&e.y===n.start.coords.y)return n.start=null,void n.clear();n.clear();var i=n.ordered();i.start=new a(i.start).nextVisible(),i.end=new a(i.end).previousVisible(),s.selectionDay=new a(i.start).date(),s.selectionStart=s.selectionDay,s.selectionEnd=new a(i.end).date(),n.start=null,n.end=null,s.g(),s.b(),s.N(),s.G(),s.H();s.O({"mode":"FreeHand"})}n.start=null,n.end=null},this.dispose=function(){var t=s;t.root&&(t.root.removeAttribute("style"),t.root.removeAttribute("class"),t.root.dp=null,t.root.innerHTML=null,t.root=null,t.S=!0)},this.xa=function(){this.root.dispose=this.dispose},this.Init=this.init,this.R(i)},"undefined"!=typeof jQuery&&!function(t){t.fn.daypilotNavigator=function(t){var e=null,i=this.each(function(){if(!this.daypilot){var i=new DayPilot.Navigator(this.id);this.daypilot=i;for(var s in t)i[s]=t[s];i.Init(),e||(e=i)}});return 1===this.length?e:i}}(jQuery),function(){var t=DayPilot.am();t&&t.directive("daypilotNavigator",["$parse",function(t){return{"restrict":"E","template":"<div id='{{id}}'></div>","compile":function(e,i){return e.replaceWith(this["template"].replace("{{id}}",i["id"])),function(e,i,s){var n=new DayPilot.Navigator(i[0]);n.pa.scope=e,n.init();var a=s["id"];a&&(e[a]=n);var o=s["publishAs"];if(o){(0,t(o).assign)(e,n)}for(var l in s)if(0===l.indexOf("on")){var h=DayPilot.Util.shouldApply(l);h?!function(i){n[i]=function(n){var a=t(s[i]);e["$apply"](function(){a(e,{"args":n})})}}(l):!function(i){n[i]=function(n){t(s[i])(e,{"args":n})}}(l)}var r=e["$watch"],c=s["config"]||s["daypilotConfig"],d=s["events"]||s["daypilotEvents"];r.call(e,c,function(t,e){for(var i in t)n[i]=t[i];n.update()},!0),r.call(e,d,function(t){n.events.list=t,n.V(),n.G()},!0)}}}}])}(),DayPilot.Navigator.def={},"undefined"!=typeof Sys&&Sys.Application&&Sys.Application.notifyScriptLoaded&&Sys.Application.notifyScriptLoaded()}}(DayPilot);