﻿<!DOCTYPE html>
<html>
<head>
    <title>Highlighting Today (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note"><b>Note:</b>
        This demo uses the <a href="https://api.daypilot.org/daypilot-calendar-onbeforecellrender/">onBeforeCellRender</a> event to highlight today's cells with a light yellow color.

        Read more about Calendar <a href="https://doc.daypilot.org/calendar/cell-customization/">cell customization</a>.
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Calendar("dp", {
            viewType: "Week",
            startDate: DayPilot.Date.today(),
            // headerDateFormat: "dddd",
            onTimeRangeSelected: async args => {

                const form = [
                    {name: "Name", id: "text"}
                ];

                const data = {
                    text: "Event"
                };

                const modal = await DayPilot.Modal.form(form, data);

                dp.clearSelection();

                if (modal.canceled) {
                    return;
                }

                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    text: modal.result.text,
                    barColor: "#3c78d8"
                });
            },
            onBeforeCellRender: args => {
                if (args.cell.start.getDatePart() === DayPilot.Date.today()) {
                    args.cell.properties.backColor = "#fff8e1";
                }
            }
        });
        dp.init();

        const app = {
            init() {
                this.loadEvents();
            },
            loadEvents() {
                const events = [
                ];
                dp.update({events});
            }
        };
        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

