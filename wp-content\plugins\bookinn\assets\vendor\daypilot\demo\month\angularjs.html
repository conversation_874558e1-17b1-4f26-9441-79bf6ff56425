﻿<!DOCTYPE html>
<html>
<head>
    <title>Open-Source AngularJS Monthly Event Calendar</title>

    <script src="//ajax.googleapis.com/ajax/libs/angularjs/1.2.15/angular.min.js"></script>


    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

	<!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

<!-- /top -->

                    <div class="note"><b>Note:</b> Read more about the <a href="https://doc.daypilot.org/month/angularjs/">AngularJS monthly event calendar</a>.</div>


                    <div ng-app="main" ng-controller="DemoCtrl" >

                        <daypilot-month id="dp" config="config" events="events" ></daypilot-month>
                        <div class="space">
                            <div>
                                New event:
                                <button ng-click="add()">Add</button>
                            </div>
                            <div>
                                First event:
                                <button ng-click="move()">Move</button>
                                <button ng-click="rename()">Rename</button>
                            </div>
                            <div>
                                Events array (debug):
                                <div ng-repeat="item in events">
                                    {{item}}
                                </div>

                            </div>
                        </div>
                    </div>

                    <script type="text/javascript">

                        var app = angular.module('main', ['daypilot']).controller('DemoCtrl', function($scope) {

                            $scope.config = {
                                startDate: "2021-09-01"
                            };

                            $scope.events = [
                                {
                                    start: new DayPilot.Date("2021-09-01T10:00:00"),
                                    end: new DayPilot.Date("2021-09-01T14:00:00"),
                                    id: DayPilot.guid(),
                                    text: "First Event"
                                }
                            ];

                            $scope.add = function() {
                                $scope.events.push(
                                        {
                                            start: new DayPilot.Date("2021-09-01T10:00:00"),
                                            end: new DayPilot.Date("2021-09-01T12:00:00"),
                                            id: DayPilot.guid(),
                                            text: "Simple Event"
                                        }
                                );
                            };

                            $scope.move = function() {
                                var event = $scope.events[0];
                                event.start = event.start.addDays(1);
                                event.end = event.end.addDays(1);
                            };

                            $scope.rename = function() {
                                $scope.events[0].text = "New name";
                            };

                        });

                    </script>

<!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

