﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Loading (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">This demo loads the events directly from the specified URL using the <code>events.load()</code>
        method. Read more about <a href="https://doc.daypilot.org/scheduler/event-loading/">loading events</a>
        [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMMM yyyy"},
                {groupBy: "Cell", format: "d"}
            ]
        });
        dp.init();


        dp.rows.load("eventloadingresources.txt");
        dp.events.load("eventloadingevents.txt");


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

