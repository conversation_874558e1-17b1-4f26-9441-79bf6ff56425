﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Copying (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <style>
        #dp .scheduler_default_cellparent, .scheduler_default_cell.scheduler_default_cell_business.scheduler_default_cellparent {
            background: #f3f3f3;
        }

    </style>

    <div class="note">
        You can copy events by holding Ctrl while dragging and dropping, or by using the icon on the right side of the event. Read more about <a
        href="https://doc.daypilot.org/scheduler/event-copying/">copying events</a>.
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            eventHeight: 40,
            startDate: "2024-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMM yyyy"},
                {groupBy: "Cell", format: "d"}
            ],
            onTimeRangeSelected: async (args) => {

                const form = [
                    {name: "Name", id: "name"}
                ];

                const modal = await DayPilot.Modal.form(form);
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result.name
                });
            },
            contextMenu: new DayPilot.Menu({
                items: [
                    {
                        text: "Copy", onClick: (args) => {
                            app.ref.copied = args.source;
                        }
                    },
                    {
                        text: "Delete", onClick: (args) => {
                            dp.events.remove(args.source);
                        }
                    },
                ]
            }),
            onEventMove: (args) => {
                if (args.ctrl) {
                    const newEvent = {
                        start: args.newStart,
                        end: args.newEnd,
                        text: "Copy of " + args.e.text(),
                        resource: args.newResource,
                        id: DayPilot.guid()  // generate random id
                    };
                    dp.events.add(newEvent);

                    // notify the server about the action here

                    args.preventDefault(); // prevent the default action - moving event to the new location
                }
                if (args.areaData === "event-copy") {
                    dp.events.add({
                        start: args.newStart,
                        end: args.newEnd,
                        resource: args.newResource,
                        id: DayPilot.guid(),
                        text: "Copy of " + args.e.text()
                    });
                    args.preventDefault();
                }
            },
            onBeforeEventRender: (args) => {
                args.data.areas = [
                    {
                        right: 2,
                        top: "calc(50% - 9px)",
                        width: 18,
                        height: 18,
                        backColor: "#fff",
                        style: "box-sizing: border-box; border-radius: 18px; padding-left: 4px; border: 1px solid #ccc;font-size: 14px;line-height: 14px;color: #999;",
                        html: "&raquo;",
                        toolTip: "Copy",
                        action: "Move",
                        data: "event-copy"

                    }
                ];
            },
            height: 350
        });
        dp.init();
        dp.scrollTo("2024-03-25");


        const app = {
            ref: {
                copied: null
            },
            loadData() {
                const resources = [
                  {name: "Room 1", id: "A"},
                  {name: "Room 2", id: "B"},
                  {name: "Room 3", id: "C"},
                  {name: "Room 4", id: "D"},
                  {name: "Person 1", id: "E"},
                  {name: "Person 2", id: "F"},
                  {name: "Person 3", id: "G"},
                  {name: "Person 4", id: "H"},
                  {name: "Tool 1", id: "I"},
                  {name: "Tool 2", id: "J"},
                  {name: "Tool 3", id: "K"},
                  {name: "Tool 4", id: "L"}
                ];

                const events = [];
                for (let i = 0; i < 12; i++) {
                    const duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const durationDays = Math.floor(Math.random() * 4) + 2; // 2 to 5
                    const start = Math.floor(Math.random() * 6) + 2; // 2 to 7

                    const e = {
                        start: new DayPilot.Date("2024-03-25T00:00:00").addDays(start),
                        end: new DayPilot.Date("2024-03-25T12:00:00").addDays(start).addDays(durationDays).addHours(duration),
                        id: DayPilot.guid(),
                        resource: String.fromCharCode(65 + i),
                        text: "Event " + (i + 1)
                    };

                    events.push(e);
                }

                dp.update({
                    resources: resources,
                    events: events
                });
            },
            init() {
                app.loadData();
            }
        };

        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

