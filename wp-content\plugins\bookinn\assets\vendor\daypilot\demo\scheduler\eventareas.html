﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Active Areas (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">Move the cursor over an event to see the active areas. Read more about <a
        href="https://doc.daypilot.org/scheduler/event-active-areas/">event active areas</a> [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            eventHeight: 40,
            startDate: "2025-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Day", format: "d"},
            ],
            contextMenu: new DayPilot.Menu({
                items: [
                    { text: "Open", onClick: args => DayPilot.Modal.alert("Not implemented") }
                ]
            }),
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
                {name: "Room G", id: "G"},
                {name: "Room H", id: "H"},
                {name: "Room I", id: "I"},
            ],
            onTimeRangeSelected: args => {
                const name = prompt("New event name:", "Event");
                dp.clearSelection();
                if (!name) return;
                dp.events.add(new DayPilot.Event({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: name
                }));
            },
            height: 350
        });

        dp.init();


        const app = {
            init() {
                this.loadEventData();
            },
            loadEventData() {
                // generate and load events
                const events = [
                    {
                        id: 1,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-10T00:00:00",
                        text: "Context menu",
                        resource: "A",
                        areas: [
                            {
                                right: 7,
                                top: 12,
                                height: 16,
                                width: 16,
                                symbol: "../icons/daypilot.svg#minichevron-down-4",
                                cssClass: "icon",
                                action: "ContextMenu"
                            }
                        ]
                    },
                    {
                        id: 2,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-10T00:00:00",
                        text: "Status",
                        resource: "B",
                        areas: [
                            {
                                right: 7,
                                top: 12,
                                height: 16,
                                width: 16,
                                symbol: "../icons/daypilot.svg#padlock",
                                cssClass: "status",
                            }
                        ]
                    },
                    {
                        id: 3,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-08T00:00:00",
                        text: "Resize",
                        resource: "C",
                        resizeDisabled: true,
                        areas: [
                            {
                                right: 4,
                                top: 8,
                                height: 24,
                                width: 12,
                                symbol: "../icons/daypilot.svg#resize-right",
                                cssClass: "icon",
                                action: "ResizeEnd"
                            }
                        ]
                    },
                    {
                        id: 4,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-08T00:00:00",
                        text: "",
                        resource: "D",
                        moveDisabled: true,
                        areas: [
                            {
                                left: 4,
                                top: 8,
                                height: 24,
                                width: 12,
                                symbol: "../icons/daypilot.svg#resize-left",
                                cssClass: "icon",
                                action: "ResizeStart"
                            },
                            {
                                left: 20,
                                top: 12,
                                height: 24,
                                right: 0,
                                text: "Resize start",
                            },

                        ]
                    },
                    {
                        id: 5,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-08T00:00:00",
                        text: "",
                        resource: "E",
                        moveDisabled: true,
                        areas: [
                            {
                                left: 4,
                                top: 8,
                                height: 24,
                                width: 12,
                                symbol: "../icons/daypilot.svg#move-vertical",
                                cssClass: "icon",
                                action: "Move"
                            },
                            {
                                left: 20,
                                top: 12,
                                height: 24,
                                right: 0,
                                text: "Move",
                            },

                        ]
                    },
                    {
                        id: 6,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-10T00:00:00",
                        text: "Delete",
                        resource: "F",
                        areas: [
                            {
                                right: 5,
                                top: 12,
                                height: 16,
                                width: 16,
                                symbol: "../icons/daypilot.svg#x-circle",
                                cssClass: "icon",
                                onClick: function(args) {
                                    dp.events.remove(args.source);
                                }
                            }
                        ]
                    },
                    {
                        id: 7,
                        start: "2025-01-02T00:00:00",
                        end: "2025-01-10T00:00:00",
                        text: "Details",
                        resource: "G",
                        areas: [
                            {
                                right: 5,
                                top: 12,
                                height: 16,
                                width: 16,
                                symbol: "../icons/daypilot.svg#bubble",
                                cssClass: "icon",
                                onClick: function(args) {
                                    DayPilot.Modal.alert("Event details");
                                }
                            }
                        ]
                    },
                ];

                dp.update({events});
            }
        };
        app.init();

    </script>

    <style>
        .icon {
            color: #e69138;
        }
        .icon:hover {
            color: #333333;
        }
        .status {
            color: #999999;
        }
    </style>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

