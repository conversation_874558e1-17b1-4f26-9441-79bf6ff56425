[18-Aug-2025 10:01:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:01:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:01:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:01:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:01:55 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:01:55 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:01:55 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:01:55 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:01:55 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:01:55 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:01:55 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:01:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:01:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:01:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:02:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:02:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:02:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:02:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:02:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:02:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:01 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:03:01 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:03:01 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:03:01 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:03:01 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:03:01 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:03:01 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:03:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:03:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:03:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:03:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:03:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:07:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:07:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:07:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:07:21 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:07:21 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:07:21 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:07:21 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:07:21 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:07:21 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:07:21 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:30:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:30:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:30:40 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:30:40 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:30:41 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:30:41 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:30:41 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:30:41 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:30:41 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:30:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:30:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:30:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:52 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:31:52 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:31:52 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:31:52 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:31:52 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:31:52 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:31:52 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:31:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:31:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:31:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:31:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:33:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:33:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:33:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:33:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:33:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:33:16 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:33:16 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:33:16 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:33:16 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:33:16 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:33:16 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:33:16 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:33:18 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:18 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:20 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:20 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:33:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:33:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:34:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:34:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:34:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:38 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:34:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:34:38 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:34:38 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:34:38 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:34:38 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:34:38 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:34:38 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:34:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:34:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:34:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:34:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:34:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:34:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:34:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:36:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:36:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:36:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:36:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:36:54 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:36:54 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:36:54 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:36:54 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:36:54 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:36:54 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:36:54 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:36:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:36:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:37:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:37:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:37:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 0e7efcdedb...
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:48 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:48 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => f71efcd31a
)

[18-Aug-2025 10:37:48 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 10:37:48 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 10:37:48 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:48 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 10:37:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:48 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 10:37:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:37:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:37:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:37:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:37:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:42:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:42:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:42:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:42:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:42:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:42:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:42:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:42:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:42:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:42:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:47:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:52:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:52:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:52:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:52:44 UTC] Automatic updates starting...
[18-Aug-2025 10:52:45 UTC]   Automatic theme updates starting...
[18-Aug-2025 10:52:45 UTC]   Automatic theme updates complete.
[18-Aug-2025 10:52:45 UTC] Automatic updates complete.
[18-Aug-2025 10:57:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:57:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:57:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:57:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:57:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:57:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:57:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 10:57:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 10:57:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 10:57:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:02:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:02:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:02:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:02:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:02:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:02:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:03:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:03:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:03:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:03:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:07:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:08:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:08:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:08:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:08:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:12:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:13:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:13:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:13:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:13:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:17:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:17:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:17:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:17:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:17:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:17:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:18:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:18:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:18:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:18:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:22:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:22:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:22:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:22:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:22:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:22:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:23:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:23:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:23:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:23:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:27:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:27:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:27:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:27:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:27:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:27:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:28:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:28:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:28:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:28:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:32:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:32:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:32:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:32:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:32:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:32:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:33:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:33:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:33:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:33:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 11:37:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:48:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:48:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 01a5f10d14...
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:48:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:48:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:48:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:48:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:48:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:49:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: f71efcd31a...
[18-Aug-2025 13:49:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:50:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:50:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:11 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:50:11 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:50:11 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:50:11 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:50:11 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:50:11 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:50:11 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:50:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:50:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:50:35 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:50:35 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:50:35 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:50:35 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:50:35 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:50:35 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:50:35 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:50:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:50:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:50:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:51:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:51:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:51:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:51:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:51:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:51:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:05 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:52:05 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:52:05 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:52:05 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:52:05 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:52:05 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:52:05 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:52:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:52:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:52:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:52:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:52:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:52:14 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:52:14 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:53:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:53:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:53:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:53:34 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:53:34 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:53:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:53:36 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:53:36 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:53:36 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:53:36 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:53:36 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:53:36 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:53:36 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:53:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:53:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:54:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:54:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:54:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:54:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:33 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:54:33 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:54:33 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:54:33 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:54:33 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:54:33 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:54:33 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:54:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:54:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:58:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:58:31 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:58:31 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:58:31 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:58:31 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:58:31 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:58:31 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:58:31 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:58:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:34 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:58:34 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:58:56 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 13:58:56 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 13:58:56 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 13:58:56 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 13:58:56 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:58:56 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 13:58:56 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 13:58:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 13:59:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:00:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:00:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:00:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:00:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:11 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:00:11 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:00:11 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:00:11 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:00:11 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:00:11 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:00:11 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:00:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:00:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:00:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:00:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:51 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:01:51 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:01:51 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:01:52 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:01:52 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:01:52 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:01:52 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:01:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:01:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:01:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:06:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:06:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:06:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:06:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:06:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:11:45 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:46 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:46 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:46 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:47 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:47 UTC] [BookInn Autoloader] Classe non trovata: BookInn_Timeline_Calendar (file atteso: class-bookinn-timeline-calendar.php)
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:11:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:16:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:16:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:16:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:16:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:16:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:21:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:21:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:51 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:21:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:51 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:21:51 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:21:51 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:21:51 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:21:51 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:21:51 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:21:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:21:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:21:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:22:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:22:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:22:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:22:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:22:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:26:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:26:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:26:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:26:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:26:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:26:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:26:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:26:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:26:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:26:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:28:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:28:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:28:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:28:58 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:28:58 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:28:58 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:28:58 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:28:58 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:28:58 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:28:58 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:29:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:29:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:29:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:29:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:29:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:33:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:35:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:35:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:35:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:35:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:35:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:01 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:36:01 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:36:01 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:36:01 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:36:01 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:36:01 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:36:01 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:36:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:36:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:36:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:21 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:36:21 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:36:21 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:36:21 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:36:21 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:36:21 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:36:21 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:36:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:36:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:36:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:41:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:41:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:41:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:41:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:41:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:41:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:48 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:41:48 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:41:48 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:41:48 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:41:48 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:41:48 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:41:48 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:41:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:41:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:41:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:41:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:46:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:46:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:35 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 14:46:35 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 14:46:35 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 14:46:35 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 14:46:35 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 14:46:35 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 14:46:35 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 14:46:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 14:46:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:46:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:46:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:51:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:51:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:51:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:51:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:51:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:56:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:56:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 14:56:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:56:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 14:56:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:56:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:56:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:56:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 14:56:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 14:56:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:01:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:01:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:01:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:01:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:01:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:01:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:01:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:01:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:01:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:01:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:06:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:06:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:06:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:06:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:06:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:06:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:07:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:07:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:07:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:07:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:11:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:11:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:11:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:11:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:11:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:12 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:14:12 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:14:12 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:14:12 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:14:12 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:14:12 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:14:12 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:16 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:16 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:14:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:14:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:14:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:14:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:15:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:15:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:15:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:15:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:15:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:15:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:41 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:15:41 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:15:41 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:15:41 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:15:41 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:15:41 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:15:41 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:15:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:15:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:15:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:18:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:18:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:18:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:18:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:18:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:18:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:50 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:18:50 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:18:50 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:18:50 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:18:50 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:18:50 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:18:50 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:18:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:18:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:22:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:22:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:22:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:22:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:57 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:22:57 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:22:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:22:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:22:58 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:22:58 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:22:58 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:22:58 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:22:58 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:22:58 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:22:58 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:23:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:23:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:27:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:27:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:34 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:34 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:27:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:27:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:36 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:27:36 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:27:36 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:27:36 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:27:36 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:27:36 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:27:36 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:27:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:27:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:27:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:27:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:27:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:29:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:29:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:14 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:14 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:19 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:29:19 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:29:19 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:29:19 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:29:19 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:29:19 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:29:19 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:29:20 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:29:20 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:29:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:29:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:29:23 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:29:23 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:29:23 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:29:23 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:29:23 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:29:23 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:29:23 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:29:25 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:29:25 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:34:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:34:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:34:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:34:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:34:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:36:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:36:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:36:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:36:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:32 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:36:32 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:36:32 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:36:32 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:36:32 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:36:32 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:36:32 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:36:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:36:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:36:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:36:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:36:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:36:37 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:36:37 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:36:37 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:36:37 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:36:37 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:36:37 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:36:37 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:39:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:39:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:45 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:39:45 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:39:45 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:39:45 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:39:45 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:39:45 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:39:45 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:39:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:39:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:39:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:39:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:39:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:39:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:39:53 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:39:53 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:39:53 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:39:53 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:39:53 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:39:53 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:39:53 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:41:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:37 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:37 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:41:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:41:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:41:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:41:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:41:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:41:48 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:41:48 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:41:48 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:41:48 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:41:48 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:41:48 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:41:48 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:42:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:42:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:42:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:42:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:42:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:42:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:42:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:42:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:42:18 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:42:18 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:42:18 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:42:18 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:42:18 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:42:18 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:42:18 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:42:18 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:42:18 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:42:18 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:42:18 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:42:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:42:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:45:36 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:45:36 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:45:36 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:45:36 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:45:36 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:45:36 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:45:36 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:45:39 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:45:39 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:45:39 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:45:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:40 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:45:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:40 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:45:40 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:45:40 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:45:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:50:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:50:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:50:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:46 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:54:46 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:54:46 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:54:46 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:54:46 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:54:46 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:54:46 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:54:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:54:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:54:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:54:56 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:54:56 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:54:56 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:54:56 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:54:56 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:54:56 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:54:56 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:54:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:55:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:55:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:55:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:55:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:55:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:55:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:57:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:57:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:57:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:45 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:57:45 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 15:57:45 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:57:45 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:57:45 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 15:57:45 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:57:45 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:57:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 15:57:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 15:57:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:49 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:57:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:49 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:57:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:57:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:57:51 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 15:57:51 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 15:57:51 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 15:57:51 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 15:57:51 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 15:57:51 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 15:57:51 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 15:57:51 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 15:57:51 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 15:58:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:58:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:58:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:58:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:58:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:58:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:59:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 15:59:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 15:59:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:59:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 15:59:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 15:59:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:01:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:01:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:50 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:01:50 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:01:50 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:01:50 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:01:50 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:01:50 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:01:50 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:01:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:01:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:01:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:01:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:01:54 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:01:54 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:01:54 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:01:54 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:01:54 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:01:54 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:01:54 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:03:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:04:03 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:04:03 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:04:03 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:04:03 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:04:03 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:04:03 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:04:03 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:04:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:04:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:04:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:04:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:04:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:04:12 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:04:12 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:04:12 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:04:12 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:04:12 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:04:12 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:04:12 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:08:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:08:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:08:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:08:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:08:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:09:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:09:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:18 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:18 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:21 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:09:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:21 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:09:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:25 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:25 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:25 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:09:25 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:09:25 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:09:25 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:09:25 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:09:25 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:09:25 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:43 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:09:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:43 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:09:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:45 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:09:45 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:09:45 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:09:45 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:09:45 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:09:45 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:09:45 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:09:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:09:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:09:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:09:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:09:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:09:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:09:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:09:53 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:09:53 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:09:53 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:09:53 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:09:53 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:09:53 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:09:53 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:11:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:11:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:11:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:11:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:11:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:11:39 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:11:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:42 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:11:42 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:11:42 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:11:42 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:11:42 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:11:42 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:11:42 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:11:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:11:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:11:45 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:11:45 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:11:45 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:11:45 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:11:45 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:11:45 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:11:45 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:15:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:15:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:15:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:14 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:14 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:15:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:15:25 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:25 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:25 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:15:25 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:15:25 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:15:25 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:15:25 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:15:25 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:15:25 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:15:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:15:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:15:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:33 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:33 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:15:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:15:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:15:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:15:38 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:15:38 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:15:38 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:15:38 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:15:38 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:15:38 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:15:38 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:15:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:15:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:15:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:58 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:58 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:15:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:15:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:16:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:16:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:16:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:16:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:16:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:16:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:16:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:16:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:16:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:16:06 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:16:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:16:06 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:16:06 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:16:06 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:16:06 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:16:06 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:16:06 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:16:06 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:16:06 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:16:06 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:16:06 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:16:06 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:16:06 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:16:06 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:17:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:17:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:17:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:17:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:17:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:17:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:17:56 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:17:56 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:17:56 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:17:56 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:17:56 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:17:56 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:17:56 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:17:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:18:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:18:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:18:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:18:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:18:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:18:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:18:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:18:06 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:18:06 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:18:06 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:18:06 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:18:06 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:18:06 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:18:06 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:18:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:18:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:24 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:24 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:24 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:22:24 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:22:24 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:22:24 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:22:24 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:22:24 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:22:24 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:22:25 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:22:25 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:22:25 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:25 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:26 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:26 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:22:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:22:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:22:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:22:31 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:22:31 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:22:31 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:22:31 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:22:31 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:22:31 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:22:31 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:22:31 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:22:31 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:24:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:24:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:24:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:08 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:08 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:11 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:24:11 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:24:11 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:24:11 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:24:11 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:24:11 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:24:11 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:11 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:24:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:13 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:13 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:15 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:24:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:24:15 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:24:17 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:24:17 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:24:17 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:24:17 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:24:17 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:24:17 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:24:17 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:24:17 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:24:17 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:24:19 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:24:19 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:27:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:27:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: bbad5f5331...
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[18-Aug-2025 16:27:40 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:27:40 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 8bc1509552
)

[18-Aug-2025 16:27:40 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:27:40 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:27:40 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[18-Aug-2025 16:27:40 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:27:40 UTC] BookInn Filter: Found 8 bookings, total: 8
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8fa9db903c...
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 8bc1509552...
[18-Aug-2025 16:27:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[18-Aug-2025 16:27:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:27:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:27:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: a72ecc4061...
[18-Aug-2025 16:27:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_nonce
[18-Aug-2025 16:27:48 UTC] BookInn Filters Debug: Received filters: Array
(
)

[18-Aug-2025 16:27:48 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => a72ecc4061
    [per_page] => 200
)

[18-Aug-2025 16:27:48 UTC] BookInn Filter: Final WHERE clause: 1=1
[18-Aug-2025 16:27:48 UTC] BookInn Filter: WHERE values: Array
(
)

[18-Aug-2025 16:27:48 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 200 OFFSET 0
            
[18-Aug-2025 16:27:48 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[18-Aug-2025 16:27:48 UTC] BookInn Filter: Found 8 bookings, total: 8
