﻿<!DOCTYPE html>
<html>
<head>
    <title>Cell Customization (Open-Source JavaScript Monthly Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

	<!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

<!-- /top -->

    <div class="note">
        <b>Note:</b>
        You can <a href="https://doc.daypilot.org/month/cell-customization/">customize the day cells</a> (background color, header text, CSS, HTML, business status) in the monthly calendar.
    </div>

<div id="dp"></div>

<script type="text/javascript">
    const dp = new DayPilot.Month("dp", {
        startDate: DayPilot.Date.today(),
        onTimeRangeSelected: async function (args) {

            const colors = [
                {name: "Blue", id: "#3c78d8"},
                {name: "Green", id: "#6aa84f"},
                {name: "Yellow", id: "#f1c232"},
                {name: "Red", id: "#cc0000"},
            ];

            const form = [
                {name: "Text", id: "text"},
                {name: "Color", id: "barColor", options: colors}
            ];

            const data = {
                text: "Event",
                barColor: "#6aa84f"
            };

            const modal = await DayPilot.Modal.form(form, data);

            dp.clearSelection();

            if (modal.canceled) {
                return;
            }

            dp.events.add({
                start: args.start,
                end: args.end,
                id: DayPilot.guid(),
                text: modal.result.text,
                barColor: modal.result.barColor
            });
        },
        onBeforeCellRender: args => {
            if (args.cell.start === DayPilot.Date.today()) {
                args.cell.properties.backColor = "#fff8e1";
                args.cell.properties.html = "Today";
            }
        }
    });

    // generate and load events
    for (let i = 0; i < 10; i++) {
        const duration = Math.floor(Math.random() * 1.2);
        const start = Math.floor(Math.random() * 6) - 3; // -3 to 3

        dp.events.add({
            start: new DayPilot.Date("2022-03-04T00:00:00").addDays(start),
            end: new DayPilot.Date("2022-03-04T12:00:00").addDays(start).addDays(duration),
            id: DayPilot.guid(),
            text: "Event " + i
        });
    }

    dp.init();


</script>

<!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

