﻿<!DOCTYPE html>
<html>
<head>
    <title>Snap to Grid (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">This demo uses <a href="https://doc.daypilot.org/scheduler/snap-to-grid/">snapToGrid</a> = false
        and <a href="https://doc.daypilot.org/scheduler/exact-event-duration/">useEventBoxes</a> = "Never".
    </div>

    <div id="dp"></div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            startDate: new DayPilot.Date("2025-03-01"),
            days: new DayPilot.Date("2025-03-01").daysInMonth() + new DayPilot.Date("2025-03-01").addMonths(1).daysInMonth(),
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Cell"}
            ],
            snapToGrid: false,
            useEventBoxes: "Never",
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
                {name: "Room G", id: "G"},
                {name: "Room H", id: "H"},
                {name: "Room I", id: "I"},
                {name: "Room J", id: "J"},
                {name: "Room K", id: "K"},
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
        });
        dp.init();

        const app = {
            init() {
                this.loadEventData();
            },
            loadEventData() {
                // generate and load events
                const events = [];

                for (let i = 0; i < 1; i++) {
                    const duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const durationDays = Math.floor(Math.random() * 6 + 2); // 2 to 7
                    const start = Math.floor(Math.random() * 6) + 2; // 2 to 7

                    const resId = String.fromCharCode(65 + i);

                    const event = {
                        start: new DayPilot.Date("2025-03-02T00:00:00").addDays(start),
                        end: new DayPilot.Date("2025-03-02T12:00:00").addDays(start).addDays(durationDays).addHours(duration),
                        id: DayPilot.guid(),
                        resource: "B",
                        text: `Event ${i + 1}`
                    };

                    events.push(event);
                }
                dp.update({events});
            }
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

