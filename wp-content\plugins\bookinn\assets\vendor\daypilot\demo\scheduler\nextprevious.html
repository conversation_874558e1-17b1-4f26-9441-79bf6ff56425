﻿<!DOCTYPE html>
<html>
<head>
    <title>Next/Previous Navigation (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->


    <div class="note">Read more about <a
        href="https://doc.daypilot.org/scheduler/next-and-previous-buttons/">next/previous buttons</a>
        [doc.daypilot.org].
    </div>


    <div class="tools">
        <a href="#" id="previous">Previous</a>
        |
        <a href="#" id="today">Today</a>
        |
        <a href="#" id="next">Next</a>
    </div>
    <div>
        <div id="dp"></div>
    </div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            startDate: DayPilot.Date.today().firstDayOfMonth(),
            cellGroupBy: "Month",
            days: DayPilot.Date.today().daysInMonth(),
            scale: "Day",
            cellWidthSpec: "Auto",
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Day", format: "d"}
            ],
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                const name = modal.result;
                dp.clearSelection();
                if (!name) return;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: "Event"
                });
            }
        });
        dp.init();


        const app = {
            elements: {
                previous: document.getElementById("previous"),
                today: document.getElementById("today"),
                next: document.getElementById("next")
            },
            addEventHandlers() {
                app.elements.previous.addEventListener("click", (e) => {
                    e.preventDefault();
                    app.changeDate(dp.startDate.addMonths(-1));
                });
                app.elements.today.addEventListener("click", (e) => {
                    e.preventDefault();
                    app.changeDate(DayPilot.Date.today());
                });
                app.elements.next.addEventListener("click", (e) => {
                    e.preventDefault();
                    app.changeDate(dp.startDate.addMonths(1));
                });

            },
            changeDate(date) {
                const startDate = date.firstDayOfMonth();
                const days = startDate.daysInMonth();
                const events = [/* ... */]; // provide event data for the new date range
                dp.update({
                    startDate,
                    days,
                    events
                });
            },
            init() {
                app.addEventHandlers();
            }
        };
        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

