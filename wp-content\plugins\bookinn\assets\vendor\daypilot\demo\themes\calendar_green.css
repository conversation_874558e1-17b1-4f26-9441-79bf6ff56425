﻿/* calendar green */

.calendar_green_main {
    --color-header-background: #636363;

    --color-event-background: #47A477;
    --color-event-bar-background: transparent;
    --color-event-bar: #367c5a;
    --color-event-border: #41946d;

    --color-cell-background-business: #ffffff;
    --color-cell-background-nonbusiness: #f9f9f9;
}

.calendar_green_main {
    border: 1px solid #999;
    font-size: 13px;
}

.calendar_green_colheader {
}

.calendar_green_cornerright_inner {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-bottom: 1px solid #999;
    color: #ffffff;
    background: var(--color-header-background);
}

.calendar_green_rowheader_inner {
    font-size: 16pt;

    text-align: right;
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    color: #ffffff;
    background: var(--color-header-background);
    padding-right: 4px;
}

.calendar_green_corner_inner {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    color: #ffffff;
    background: var(--color-header-background);
}

.calendar_green_rowheader_minutes {
    font-size: 10px;
    vertical-align: super;
    padding-left: 2px;
    padding-right: 2px;
}

.calendar_green_colheader_inner {
    text-align: center;
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    color: #ffffff;
    background: var(--color-header-background);

    display: flex; align-items: center; justify-content: center;
}

.calendar_green_cell_inner {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    background-color: var(--color-cell-background-nonbusiness);
}

.calendar_green_cell_business .calendar_green_cell_inner {
    background-color: var(--color-cell-background-business);
}

.calendar_green_alldayheader_inner {
    text-align: center;
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    color: #ffffff;
    background-color: var(--color-header-background);
}

.calendar_green_message {
    padding: 10px;
    opacity: 0.9;
    color: #ffffff;
    background: #000;
    background: linear-gradient(to bottom, #999 0%, #666);
    text-shadow: 0px -1px 1px rgba(000, 000, 000, 0.2), 0px 1px 0px rgba(255, 255, 255, 0.3);
}

.calendar_green_alldayevent {
    border-radius: 15px; /* ie gradient + rounded corners problem */
    overflow: hidden;
}

.calendar_green_alldayevent_inner {
    position: absolute;
    overflow: hidden;
    top: 2px;
    bottom: 2px;
    left: 2px;
    right: 2px;
    padding: 4px;
    margin-right: 1px;
    font-size: 13px;
    color: #ffffff;
    background-color: var(--color-event-background);
    border-radius: 5px;
    border: 1px solid var(--color-event-border);
    display: flex;
    align-items: center;
}

.calendar_green_event {
    border-radius: 5px; /* ie gradient + rounded corners problem */
}

.calendar_green_event_inner {
    overflow: hidden;
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin-right: 1px;
    font-size: 13px;
    padding: 2px 2px 2px 16px; /* make space for duration bar */
    color: #ffffff;
    background-color: var(--color-event-background);
    border-radius: 5px;
    border: 1px solid var(--color-event-border);
}

.calendar_green_event_bar {
    top: 0px;
    bottom: 0px;
    left: 0px;
    width: 8px;
    background-color: var(--color-event-bar-background);
}

.calendar_green_event_bar_inner {
    position: absolute;
    width: 8px;
    border-radius: 8px;
    background-color: var(--color-event-bar);
}

.calendar_green_shadow_inner {
    background-color: #666666;
    opacity: 0.5;
    height: 100%;
    border-radius: 5px;
}

.calendar_green_event_delete {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAI5JREFUKFNtkLERgCAMRbmzdK8s4gAUlhYOYEHJEJYOYOEwDmGBPxC4kOPfvePy84MGR0RJ2N1A8H3N6DATwSQ57m2ql8NBG+AEM7D+UW+wjdfUPgerYNgB5gOLRHqhcasg84C2QxPMtrUhSqQIhg7ypy9VM2EUZPI/4rQ7rGxqo9sadTegw+UdjeDLAKUfhbaQUVPIfJYAAAAASUVORK5CYII=) center center no-repeat;
    opacity: 0.6;
    cursor: pointer;
}

.calendar_green_event_delete:hover {
    opacity: 1;
}

.calendar_green_scroll_up {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAB3RJTUUH2wESDiYcrhwCiQAAAAlwSFlzAAAuIwAALiMBeKU/dgAAAARnQU1BAACxjwv8YQUAAACcSURBVHjaY2AgF9wWsTW6yGMlhi7OhC7AyMDQzMnBXIpFHAFuCtuaMTP+P8nA8P/b1x//FfW/HHuF1UQmxv+NUP1c3OxMVVhNvCVi683E8H8LXOY/w9+fTH81tF8fv4NiIpBRj+YoZtZ/LDUoJmKYhsVUpv0MDiyMDP96sIYV0FS2/8z9ICaLlOhvS4b/jC//MzC8xBG0vJeF7GQBlK0xdiUzCtsAAAAASUVORK5CYII=);
}

.calendar_green_scroll_down {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAACqSURBVChTY7wpam3L9J+xmQEP+PGPKZZxP4MDi4zI78uMDIwa2NT+Z2DYovrmiC+TI8OBP/8ZmEqwGvif4e8vxr+FIDkmEKH25vBWBgbG0+iK/zEwLtF+ffwOXCGI8Y+BoRFFIdC030x/WmBiYBNhpgLdswNJ8RSYaSgmgk39z1gPUfj/29ef/9rwhQTDHRHbrbdEbLvRFcGthkkAra/9/uMvhkK8piNLAgCRpTnNn4AEmAAAAABJRU5ErkJggg==);
}

.calendar_green_now {
    background-color: red;
}

.calendar_green_now:before {
    content: '';
    top: -5px;
    border-width: 5px;
    border-color: transparent transparent transparent red;
    border-style: solid;
    width: 0px;
    height: 0px;
    position: absolute;
    -moz-transform: scale(.9999);
}

.calendar_green_shadow_forbidden .calendar_green_shadow_inner {
    background-color: red;
}

.calendar_green_shadow_top {
    box-sizing: border-box;
    padding: 2px;
    border: 1px solid #ccc;
    background: #fff;
    background: linear-gradient(to bottom, #ffffff 0%, #eeeeee);
}

.calendar_green_shadow_bottom {
    box-sizing: border-box;
    padding: 2px;
    border: 1px solid #ccc;
    background: #fff;
    background: linear-gradient(to bottom, #ffffff 0%, #eeeeee);
}

.calendar_green_crosshair_vertical, .calendar_green_crosshair_horizontal, .calendar_green_crosshair_left, .calendar_green_crosshair_top {
    background-color: gray;
    opacity: 0.2;
}

.calendar_green_loading {
    background-color: orange;
    color: white;
    padding: 2px;
}
