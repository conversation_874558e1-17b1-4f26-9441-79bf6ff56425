/*
 * BookInn Master Calendar Booking Chart (DayPilot Integration)
 * Handles room booking visualization using DayPilot Scheduler
 * Enhanced with professional features: date filters, status legend, improved styling
 */
(function(window, $) {
	if (!window.BookInn) window.BookInn = {};
	if (!window.BookInn.Widgets) window.BookInn.Widgets = {};

	BookInn.Widgets.MasterCalendar = {
		scheduler: null,
		initialized: false,
		currentData: { rooms: [], bookings: [] },
		dateFilters: {
			startDate: null,
			endDate: null
		},

		// Booking status configuration with colors matching BookInn theme
		statusConfig: {
			'pending': { color: '#ffc107', bgColor: '#fff3cd', label: 'Pending' },
			'confirmed': { color: '#28a745', bgColor: '#d4edda', label: 'Confirmed' },
			'checked_in': { color: '#007bff', bgColor: '#d1ecf1', label: 'Checked In' },
			'checked_out': { color: '#6c757d', bgColor: '#e2e3e5', label: 'Checked Out' },
			'cancelled': { color: '#dc3545', bgColor: '#f8d7da', label: 'Cancelled' },
			'no_show': { color: '#dc3545', bgColor: '#f8d7da', label: 'No Show' }
		},

		init: function() {
			if (this.initialized) return;
			this.initialized = true;

			// Create enhanced calendar structure
			this.createCalendarStructure();

			var container = document.getElementById('bookinn-gstc-container');
			if (!container) return;

			// Load DayPilot if not present
			if (typeof DayPilot === 'undefined') {
				console.error('DayPilot library not loaded.');
				container.innerHTML = '<div class="bookinn-error">DayPilot library missing.</div>';
				return;
			}

			// Initialize date filters with current month
			this.initializeDateFilters();

			// Bind events
			this.bindEvents();

			// Fetch bookings and rooms
			this.fetchData().then(data => {
				this.currentData = data;
				this.renderScheduler(container, data.rooms, data.bookings);
			});
		},

		/**
		 * Create enhanced calendar structure with header, filters, and legend
		 */
		createCalendarStructure: function() {
			var $calendarTab = $('#calendar-master');
			if (!$calendarTab.length) return;

			// Check if structure already exists
			if ($calendarTab.find('.bookinn-calendar-master-enhanced').length) return;

			var calendarHTML = `
				<div class="bookinn-calendar-master-enhanced">
					<!-- Calendar Header -->
					<div class="bookinn-calendar-header">
						<div class="bookinn-section-title">
							<h3><i class="dashicons dashicons-calendar-alt"></i> Master Calendar</h3>
							<p>Comprehensive booking overview with room timeline</p>
						</div>
						<div class="bookinn-calendar-controls">
							<div class="bookinn-calendar-filters">
								<div class="bookinn-filter-group">
									<label for="bookinn-calendar-start-date">From Date:</label>
									<input type="date" id="bookinn-calendar-start-date" class="bookinn-date-input">
								</div>
								<div class="bookinn-filter-group">
									<label for="bookinn-calendar-end-date">To Date:</label>
									<input type="date" id="bookinn-calendar-end-date" class="bookinn-date-input">
								</div>
								<button type="button" id="bookinn-calendar-apply-filter" class="bookinn-btn bookinn-btn-primary">
									<i class="dashicons dashicons-filter"></i> Apply Filter
								</button>
								<button type="button" id="bookinn-calendar-reset-filter" class="bookinn-btn bookinn-btn-secondary">
									<i class="dashicons dashicons-update"></i> Reset
								</button>
							</div>
						</div>
					</div>

					<!-- Status Legend -->
					<div class="bookinn-calendar-legend">
						<div class="bookinn-legend-title">
							<strong>Booking Status Legend:</strong>
						</div>
						<div class="bookinn-legend-items" id="bookinn-status-legend">
							<!-- Legend items will be populated dynamically -->
						</div>
					</div>

					<!-- Calendar Container -->
					<div class="bookinn-calendar-master-container">
						<div class="bookinn-calendar-master-content">
							<div id="bookinn-gstc-container" style="width:100%;height:600px;"></div>
						</div>
					</div>
				</div>
			`;

			$calendarTab.html(calendarHTML);
			this.createStatusLegend();
		},

		/**
		 * Create status legend with BookInn color scheme
		 */
		createStatusLegend: function() {
			var $legendContainer = $('#bookinn-status-legend');
			if (!$legendContainer.length) return;

			var legendHTML = '';
			for (var status in this.statusConfig) {
				var config = this.statusConfig[status];
				legendHTML += `
					<div class="bookinn-legend-item" data-status="${status}">
						<span class="bookinn-legend-color" style="background-color: ${config.color};"></span>
						<span class="bookinn-legend-label">${config.label}</span>
					</div>
				`;
			}
			$legendContainer.html(legendHTML);
		},

		/**
		 * Initialize date filters with current month range
		 */
		initializeDateFilters: function() {
			var today = new Date();
			var firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
			var lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

			this.dateFilters.startDate = firstDay;
			this.dateFilters.endDate = lastDay;

			// Set input values
			$('#bookinn-calendar-start-date').val(this.formatDateForInput(firstDay));
			$('#bookinn-calendar-end-date').val(this.formatDateForInput(lastDay));
		},

		/**
		 * Bind calendar events
		 */
		bindEvents: function() {
			var self = this;

			// Date filter events
			$(document).on('click', '#bookinn-calendar-apply-filter', function() {
				self.applyDateFilter();
			});

			$(document).on('click', '#bookinn-calendar-reset-filter', function() {
				self.resetDateFilter();
			});

			// Legend item toggle (for future filtering by status)
			$(document).on('click', '.bookinn-legend-item', function() {
				$(this).toggleClass('inactive');
				// Future: implement status filtering
			});
		},

		/**
		 * Apply date range filter
		 */
		applyDateFilter: function() {
			var startDateStr = $('#bookinn-calendar-start-date').val();
			var endDateStr = $('#bookinn-calendar-end-date').val();

			if (!startDateStr || !endDateStr) {
				this.showNotification('Please select both start and end dates.', 'warning');
				return;
			}

			var startDate = new Date(startDateStr);
			var endDate = new Date(endDateStr);

			if (startDate > endDate) {
				this.showNotification('Start date must be before end date.', 'error');
				return;
			}

			this.dateFilters.startDate = startDate;
			this.dateFilters.endDate = endDate;

			// Update scheduler date range
			if (this.scheduler) {
				var daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
				   this.scheduler.startDate = new DayPilot.Date(startDate);
				this.scheduler.days = daysDiff;
				this.scheduler.update();
			}

			this.showNotification('Date filter applied successfully.', 'success');
		},

		/**
		 * Reset date filter to current month
		 */
		resetDateFilter: function() {
			this.initializeDateFilters();
			this.applyDateFilter();
		},

		/**
		 * Format date for HTML input
		 */
		formatDateForInput: function(date) {
			return date.toISOString().split('T')[0];
		},

		/**
		 * Show notification message
		 */
		showNotification: function(message, type) {
			// Use BookInn notification system if available
			if (window.BookInn && window.BookInn.Utils && window.BookInn.Utils.showNotification) {
				window.BookInn.Utils.showNotification(message, type);
			} else {
				// Fallback notification
				console.log(`${type.toUpperCase()}: ${message}`);
			}
		},

		fetchData: function() {
			   // Carica dati reali da backend via AJAX
			   var ajaxUrl = (window.BookInn && BookInn.Core && BookInn.Core.config && BookInn.Core.config.ajaxUrl) ? BookInn.Core.config.ajaxUrl : '/wp-admin/admin-ajax.php';
			   var nonce = (window.BookInn && BookInn.Core && BookInn.Core.config && BookInn.Core.config.nonce) ? BookInn.Core.config.nonce : '';
			   // Carica camere e prenotazioni in parallelo
			   return Promise.all([
				   $.ajax({
					   url: ajaxUrl,
					   type: 'POST',
					   dataType: 'json',
					   data: { action: 'bookinn_get_rooms', nonce: nonce }
				   }),
				   $.ajax({
					   url: ajaxUrl,
					   type: 'POST',
					   dataType: 'json',
					   data: { action: 'bookinn_get_bookings', nonce: nonce, per_page: 200 }
				   })
			   ]).then(function(results) {
				   var rooms = [];
				   var bookings = [];
				   // Rooms
				   if (results[0].success && Array.isArray(results[0].data)) {
					   rooms = results[0].data.map(function(room) {
						   return { id: String(room.id), name: room.name || room.room_number || ('Room ' + room.id) };
					   });
				   }
				   // Bookings
				   if (results[1].success && Array.isArray(results[1].data.bookings)) {
					   bookings = results[1].data.bookings.map(function(b) {
						   return {
							   id: b.id,
							   resource: String(b.room_id),
							   text: b.guest_name || b.guest_first_name + ' ' + b.guest_last_name || 'Booking',
							   start: b.check_in_date + 'T12:00:00',
							   end: b.check_out_date + 'T10:00:00',
							   status: b.status
						   };
					   });
				   }
				   return { rooms: rooms, bookings: bookings };
			   }).catch(function(err) {
				   console.error('BookInn MasterCalendar: Errore caricamento dati', err);
				   return { rooms: [], bookings: [] };
			   });
		   },

		renderScheduler: function(container, rooms, bookings) {
			var self = this;
			var dp = new DayPilot.Scheduler(container.id);

			// Enhanced room resources with better display
			dp.resources = rooms.map(r => ({
				id: r.id,
				name: r.name,
				// Add room details for better display
				html: `<div class="bookinn-room-header">
					<div class="bookinn-room-name">${r.name}</div>
					<div class="bookinn-room-details">${r.description || ''}</div>
				</div>`
			}));

			// Enhanced events with status-based styling
			dp.events.list = bookings.map(b => {
				var statusConfig = self.statusConfig[b.status] || self.statusConfig['pending'];
				return {
					id: b.id,
					resource: b.resource,
					text: b.text,
					start: b.start,
					end: b.end,
					barColor: statusConfig.color,
					backColor: statusConfig.bgColor,
					borderColor: statusConfig.color,
					status: b.status,
					// Add custom data for modal display
					data: {
						guest_name: b.text,
						status: b.status,
						check_in: b.start,
						check_out: b.end,
						room_id: b.resource
					}
				};
			});

			// Use date filters if set, otherwise use current month
			var startDate, days;
			if (this.dateFilters.startDate && this.dateFilters.endDate) {
				   startDate = new DayPilot.Date(this.dateFilters.startDate);
				days = Math.ceil((this.dateFilters.endDate - this.dateFilters.startDate) / (1000 * 60 * 60 * 24)) + 1;
			} else {
				var today = DayPilot.Date.today();
				startDate = today.firstDayOfMonth();
				days = today.daysInMonth();
			}

			dp.startDate = startDate;
			dp.days = days;
			dp.cellWidth = 50; // Optimized cell width
			dp.scale = "Day";

			// Enhanced time headers
			dp.timeHeaders = [
				{ groupBy: "Month", format: "MMMM yyyy" },
				{ groupBy: "Day", format: "d ddd" }
			];

			// Significantly expanded room column for full descriptions
			dp.rowHeaderColumnDefaultWidth = 280;
			dp.rowHeaderColumns = [
				{
					title: "Room Details",
					display: "html",
					width: 280
				}
			];

			// Enhanced event click with modal integration
			dp.onEventClick = function(args) {
				self.showBookingModal(args.e.data);
			};

			// Event hover for quick preview
			dp.onEventMouseEnter = function(args) {
				var booking = args.e.data;
				var tooltip = `
					<div class="bookinn-booking-tooltip">
						<strong>${booking.guest_name}</strong><br>
						Status: ${self.statusConfig[booking.status]?.label || booking.status}<br>
						${booking.check_in} - ${booking.check_out}
					</div>
				`;
				args.e.tooltip = tooltip;
			};

			// Custom cell rendering for better appearance
			dp.onBeforeCellRender = function(args) {
				// Add weekend styling
				if (args.cell.start.getDayOfWeek() === 0 || args.cell.start.getDayOfWeek() === 6) {
					args.cell.backColor = "#f8f9fa";
				}
			};

			// Initialize scheduler
			dp.init();
			this.scheduler = dp;

			console.log('BookInn Master Calendar: Scheduler initialized with', rooms.length, 'rooms and', bookings.length, 'bookings');
		},

		/**
		 * Show booking details modal
		 */
		showBookingModal: function(bookingData) {
			// Try to use BookInn modal system if available
			if (window.BookInn && window.BookInn.Modal && window.BookInn.Modal.show) {
				var modalContent = this.generateBookingModalContent(bookingData);
				window.BookInn.Modal.show('Booking Details', modalContent);
			} else {
				// Fallback to custom modal
				this.showCustomBookingModal(bookingData);
			}
		},

		/**
		 * Generate booking modal content
		 */
		generateBookingModalContent: function(booking) {
			var statusConfig = this.statusConfig[booking.status] || this.statusConfig['pending'];
			return `
				<div class="bookinn-booking-modal-content">
					<div class="bookinn-booking-header">
						<h4>${booking.guest_name}</h4>
						<span class="bookinn-status-badge bookinn-status-${booking.status}"
							  style="background-color: ${statusConfig.bgColor}; color: ${statusConfig.color};">
							${statusConfig.label}
						</span>
					</div>
					<div class="bookinn-booking-details">
						<div class="bookinn-detail-row">
							<strong>Check-in:</strong> ${booking.check_in}
						</div>
						<div class="bookinn-detail-row">
							<strong>Check-out:</strong> ${booking.check_out}
						</div>
						<div class="bookinn-detail-row">
							<strong>Room:</strong> Room ${booking.room_id}
						</div>
						<div class="bookinn-detail-row">
							<strong>Status:</strong> ${statusConfig.label}
						</div>
					</div>
					<div class="bookinn-booking-actions">
						<button type="button" class="bookinn-btn bookinn-btn-primary" onclick="BookInn.Widgets.MasterCalendar.editBooking('${booking.id}')">
							<i class="dashicons dashicons-edit"></i> Edit Booking
						</button>
						<button type="button" class="bookinn-btn bookinn-btn-secondary" onclick="BookInn.Widgets.MasterCalendar.viewBookingDetails('${booking.id}')">
							<i class="dashicons dashicons-visibility"></i> View Details
						</button>
					</div>
				</div>
			`;
		},

		/**
		 * Show custom booking modal (fallback)
		 */
		showCustomBookingModal: function(booking) {
			var statusConfig = this.statusConfig[booking.status] || this.statusConfig['pending'];
			var message = `
				Booking Details:
				Guest: ${booking.guest_name}
				Status: ${statusConfig.label}
				Check-in: ${booking.check_in}
				Check-out: ${booking.check_out}
				Room: ${booking.room_id}
			`;
			alert(message);
		},

		/**
		 * Edit booking (placeholder for future implementation)
		 */
		editBooking: function(bookingId) {
			console.log('Edit booking:', bookingId);
			// Future: integrate with BookInn booking edit functionality
		},

		/**
		 * View booking details (placeholder for future implementation)
		 */
		viewBookingDetails: function(bookingId) {
			console.log('View booking details:', bookingId);
			// Future: integrate with BookInn booking details view
		}
	};

	// Auto-init when subtab is activated
	$(document).on('click', '.bookinn-sub-tab-link[data-tab="calendar-master"]', function() {
		setTimeout(function() {
			BookInn.Widgets.MasterCalendar.init();
		}, 100);
	});

	// Optionally, auto-init if already visible on page load
	$(function() {
		if ($('#calendar-master').is(':visible')) {
			BookInn.Widgets.MasterCalendar.init();
		}
	});

})(window, jQuery);
