/*
 * BookInn Master Calendar Booking Chart (DayPilot Integration)
 * Handles room booking visualization using DayPilot Scheduler
 */
(function(window, $) {
	if (!window.BookInn) window.BookInn = {};
	if (!window.BookInn.Widgets) window.BookInn.Widgets = {};

	BookInn.Widgets.MasterCalendar = {
		scheduler: null,
		initialized: false,

		init: function() {
			if (this.initialized) return;
			this.initialized = true;
			var container = document.getElementById('bookinn-gstc-container');
			if (!container) return;

			// Load DayPilot if not present
			if (typeof DayPilot === 'undefined') {
				console.error('DayPilot library not loaded.');
				container.innerHTML = '<div class="bookinn-error">DayPilot library missing.</div>';
				return;
			}

			// Fetch bookings and rooms (replace with real AJAX/data source)
			this.fetchData().then(data => {
				this.renderScheduler(container, data.rooms, data.bookings);
			});
		},

		   fetchData: function() {
			   // Carica dati reali da backend via AJAX
			   var ajaxUrl = (window.BookInn && BookInn.Core && BookInn.Core.config && BookInn.Core.config.ajaxUrl) ? BookInn.Core.config.ajaxUrl : '/wp-admin/admin-ajax.php';
			   var nonce = (window.BookInn && BookInn.Core && BookInn.Core.config && BookInn.Core.config.nonce) ? BookInn.Core.config.nonce : '';
			   // Carica camere e prenotazioni in parallelo
			   return Promise.all([
				   $.ajax({
					   url: ajaxUrl,
					   type: 'POST',
					   dataType: 'json',
					   data: { action: 'bookinn_get_rooms', nonce: nonce }
				   }),
				   $.ajax({
					   url: ajaxUrl,
					   type: 'POST',
					   dataType: 'json',
					   data: { action: 'bookinn_get_bookings', nonce: nonce, per_page: 200 }
				   })
			   ]).then(function(results) {
				   var rooms = [];
				   var bookings = [];
				   // Rooms
				   if (results[0].success && Array.isArray(results[0].data)) {
					   rooms = results[0].data.map(function(room) {
						   return { id: String(room.id), name: room.name || room.room_number || ('Room ' + room.id) };
					   });
				   }
				   // Bookings
				   if (results[1].success && Array.isArray(results[1].data.bookings)) {
					   bookings = results[1].data.bookings.map(function(b) {
						   return {
							   id: b.id,
							   resource: String(b.room_id),
							   text: b.guest_name || b.guest_first_name + ' ' + b.guest_last_name || 'Booking',
							   start: b.check_in_date + 'T12:00:00',
							   end: b.check_out_date + 'T10:00:00',
							   status: b.status
						   };
					   });
				   }
				   return { rooms: rooms, bookings: bookings };
			   }).catch(function(err) {
				   console.error('BookInn MasterCalendar: Errore caricamento dati', err);
				   return { rooms: [], bookings: [] };
			   });
		   },

		renderScheduler: function(container, rooms, bookings) {
			var dp = new DayPilot.Scheduler(container.id);
			dp.resources = rooms.map(r => ({ id: r.id, name: r.name }));
			dp.events.list = bookings.map(b => ({
				id: b.id,
				resource: b.resource,
				text: b.text,
				start: b.start,
				end: b.end,
				barColor: b.status === 'confirmed' ? '#4caf50' : '#ffc107',
				status: b.status
			}));
			// Calculate the first day of the current month
			var today = DayPilot.Date.today();
			var firstOfMonth = today.firstDayOfMonth();
			var daysInMonth = today.daysInMonth();

			dp.startDate = firstOfMonth;
			dp.days = daysInMonth;
			dp.cellWidth = 80; // più largo per un solo giorno
			dp.scale = "Day";
			dp.timeHeaders = [
				{ groupBy: "Month", format: "MMMM yyyy" },
				{ groupBy: "Day", format: "d ddd" }
			];
			// Allarga la colonna camere
			dp.rowHeaderColumnDefaultWidth = 500;
			dp.rowHeaderColumns = [
				{ title: "Room", display: "name" }
			];
			dp.onEventClick = function(args) {
				// Show modal or details (integrate with BookInn modal system)
				alert('Booking: ' + args.e.data.text);
			};
			dp.init();
			this.scheduler = dp;
		}
	};

	// Auto-init when subtab is activated
	$(document).on('click', '.bookinn-sub-tab-link[data-tab="calendar-master"]', function() {
		setTimeout(function() {
			BookInn.Widgets.MasterCalendar.init();
		}, 100);
	});

	// Optionally, auto-init if already visible on page load
	$(function() {
		if ($('#calendar-master').is(':visible')) {
			BookInn.Widgets.MasterCalendar.init();
		}
	});

})(window, jQuery);
