﻿<!DOCTYPE html>
<html>
<head>
    <title>Green CSS Theme (Open-Source JavaScript Monthly Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

	<!-- /head -->

    <link href="../themes/month_green.css?v=2025.3.696" type="text/css" rel="stylesheet"/>

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

<!-- /top -->

<div class="note"><b>Note:</b> You can create a theme using the online <strong>DayPilot Theme Designer</strong>: <a href="https://themes.daypilot.org/">https://themes.daypilot.org/</a></div>


<div id="dp"></div>

<script type="text/javascript">
    var dp = new DayPilot.Month("dp");

    // behavior and appearance
    dp.theme = "month_green";

    // view
    dp.startDate = "2021-03-01";

    // generate and load events
    for (var i = 0; i < 10; i++) {
        var duration = Math.floor(Math.random() * 1.2);
        var start = Math.floor(Math.random() * 6) - 3; // -3 to 3

        var e = new DayPilot.Event({
            start: new DayPilot.Date("2021-03-04T00:00:00").addDays(start),
            end: new DayPilot.Date("2021-03-04T12:00:00").addDays(start).addDays(duration),
            id: DayPilot.guid(),
            text: "Event " + i
        });
        dp.events.add(e);
    }

    // event creating
    dp.onTimeRangeSelected = function (args) {
        var name = prompt("New event name:", "Event");
        dp.clearSelection();
        if (!name) return;
        var e = new DayPilot.Event({
            start: args.start,
            end: args.end,
            id: DayPilot.guid(),
            text: name
        });
        dp.events.add(e);
    };

    dp.onEventClicked = function(args) {
        alert("clicked: " + args.e.id());
    };

    dp.init();


</script>

<!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

