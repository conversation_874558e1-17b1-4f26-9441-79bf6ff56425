﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Icons (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <style>
        .calendar_default_event_inner {
            color: #333;
            border-radius: 10px;
        }

    </style>

    <div class="note">
        You can add images/icons at the specified position inside an event using the <code>onBeforeEventRender</code> event handler.
        <br/>
        Read more about <a href="https://doc.daypilot.org/calendar/event-customization/">event customization</a> [doc.daypilot.org].
    </div>


    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Calendar("dp", {
            startDate: "2022-06-06",
            viewType: "Week",
            durationBarVisible: false,
            onBeforeEventRender: args => {
                args.data.areas = [
                    {
                        left: 5,
                        bottom: 5,
                        width: 40,
                        height: 40,
                        symbol: "../icons/daypilot.svg#checkmark-4",
                        style: "border-radius: 50%",
                        backColor: "#0000007f",
                        fontColor: "#ffffff",
                        padding: 4
                    }
                ]
            }
        });
        dp.init();

        const app = {
            init() {
                this.loadEvents();
            },
            loadEvents() {
                const events = [
                    {
                        "start": "2022-06-06T10:00:00",
                        "end": "2022-06-06T13:00:00",
                        "id": "23ef6fcd-e12d-b085-e38a-a4e23d0bb61d",
                        "text": "Event 1",
                        "backColor": "#FFE599",
                        "borderColor": "#F1C232"
                    },
                    {
                        "start": "2022-06-07T11:00:00",
                        "end": "2022-06-07T14:00:00",
                        "id": "fb62e2dd-267e-ec91-886b-73574d24e25a",
                        "text": "Event 2",
                        "backColor": "#9FC5E8",
                        "borderColor": "#3D85C6"
                    },
                    {
                        "start": "2022-06-08T10:00:00",
                        "end": "2022-06-08T13:00:00",
                        "id": "29b7a553-d44f-8f2c-11e1-a7d5f62eb123",
                        "text": "Event 3",
                        "backColor": "#B6D7A8",
                        "borderColor": "#6AA84F"
                    },
                    {
                        "start": "2022-06-08T14:00:00",
                        "end": "2022-06-08T17:00:00",
                        "id": "ff968cfb-eba1-8dc1-7396-7f0d4f465c8a",
                        "text": "Event 4",
                        "backColor": "#EA9999",
                        "borderColor": "#CC0000",
                        "tags": {
                            "type": "important"
                        }
                    }
                ];
                dp.update({events});
            }
        };
        app.init();


    </script>
    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

