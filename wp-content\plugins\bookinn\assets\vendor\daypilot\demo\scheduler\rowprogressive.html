﻿<!DOCTYPE html>
<html>
<head>
    <title>Progressive Row Rendering (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">This scheduler displays 1000 resources. The row headers are
        rendered progressively during scrolling (<code>progressiveRowRendering: true</code>).
        Read more about <a href="https://doc.daypilot.org/scheduler/progressive-row-rendering/">progressive row
            rendering</a>.
    </div>

    <div id="dp"></div>


    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMMM yyyy"},
                {groupBy: "Cell", format: "d"}
            ],
            rowHeaderWidth: 120,
            scrollDelayEvents: 0,
            progressiveRowRendering: true,
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
            height: 350
        });
        dp.init();
        dp.scrollTo("2025-03-25");


        const app = {
            init() {
                this.loadData();
            },
            loadData() {
                // generate and load events
                const resources = [];
                for (let i = 0; i < 1000; i++) {
                    const r = {
                        name: "Resource " + i,
                        id: "R" + i,
                        children: []
                    };

                    for (let x = 0; x < 3; x++) {
                        const c = {
                            name: "Resource " + i + "." + x,
                            id: "R" + i + "." + x
                        };
                        r.children.push(c);
                    }
                    resources.push(r);
                }

                const events = []
                for (let i = 0; i < 1000; i++) {
                    events.push({
                        start: "2025-03-25",
                        end: "2025-03-25T12:00:00",
                        id: DayPilot.guid(),
                        resource: "R" + i,
                        text: "Event"
                    });
                }

                dp.update({resources, events});
            }
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

