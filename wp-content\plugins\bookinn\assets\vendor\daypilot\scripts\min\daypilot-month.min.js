﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(){if("undefined"==typeof DayPilot.Month||!DayPilot.Month.events){var t={},e=DayPilot.Util.isVueVNode;t.Month=function(n,i){this.v="2025.3.696-lite",this.nav={};var a=this;this.id=n,this.isMonth=!0,this.api=2,this.backendUrl=null,this.cellHeaderHeight=24,this.cellHeight=100,this.cellMarginBottom=0,this.contextMenu=null,this.cssClassPrefix="month_default",this.eventBarVisible=!0,this.eventBorderRadius=null,this.eventHeight=25,this.eventsLoadMethod="GET",this.headerHeight=30,this.hideUntilInit=!0,this.lineSpace=1,this.locale="en-us",this.showToolTip=!0,this.startDate=new DayPilot.Date,this.theme=null,this.visible=!0,this.weekStarts="Auto",this.width="100%",this.xssProtection="Enabled",this.afterRender=function(){},this.cellHeaderClickHandling="Enabled",this.eventClickHandling="Enabled",this.eventDeleteHandling="Disabled",this.eventMoveHandling="Update",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.headerClickHandling="Enabled",this.timeRangeSelectedHandling="Enabled",this.onCellHeaderClick=null,this.onCellHeaderClicked=null,this.onEventClick=null,this.onEventClicked=null,this.onEventDelete=null,this.onEventDeleted=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onEventRightClick=null,this.onEventRightClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onBeforeEventRender=null,this.onBeforeCellRender=null,this.cellEvents=[],this.elements={},this.elements.events=[],this.cache={},this.a=!1,this.b=function(t,e){var t=JSON.parse(t);return t.CallBackRedirect?void(document.location.href=t.CallBackRedirect):"None"===t.UpdateType?void a.fireAfterRenderDetached(t.CallBackData,!0):(a.events.list=t.Events,"Full"===t.UpdateType&&(a.startDate=t.StartDate,a.timeFormat=t.TimeFormat?t.TimeFormat:a.timeFormat,"undefined"!=typeof t.WeekStarts&&(a.weekStarts=t.WeekStarts),a.hashes=t.Hashes),a.c(),a.d(),a.f(),"Full"===t.UpdateType&&(a.g(),a.i()),a.j(),a.show(),a.k(),void a.fireAfterRenderDetached(t.CallBackData,!0))},this.fireAfterRenderDetached=function(t,e){var n=function(t,e){return function(){a.afterRender&&a.afterRender(t,e)}};window.setTimeout(n(t,e),0)},this.lineHeight=function(){return this.eventHeight+this.lineSpace},this.events={},this.events.add=function(t){var e=null;if(t instanceof DayPilot.Event)e=t.data;else{if("object"!=typeof t)throw"DayPilot.Month.events.add() expects an object or DayPilot.Event instance.";e=t}a.events.list||(a.events.list=[]),a.events.list.push(e),a.update(),a.l.notify()},this.events.find=function(t){if(!a.events.list)return null;if("function"==typeof t){for(var e=t,n=0;n<a.events.list.length;n++){var i=new DayPilot.Event(a.events.list[n],a);if(e(i))return i}return null}for(var n=0;n<a.events.list.length;n++){var s=a.events.list[n];if(s.id===t)return new DayPilot.Event(s,a)}return null},this.events.update=function(t){if(t instanceof DayPilot.Event)t.commit();else if("object"==typeof t){var e=a.events.find(t.id);if(e){var n=DayPilot.indexOf(a.events.list,e.data);a.events.list.splice(n,1,t)}}a.update(),a.l.notify()},this.events.remove=function(t){var e;if(t instanceof DayPilot.Event)e=t.data;else if("object"==typeof t){var n=a.events.find(t.id);n&&(e=n.data)}else if("string"==typeof t||"number"==typeof t){var n=a.events.find(t);n&&(e=n.data)}var i=DayPilot.indexOf(a.events.list,e);a.events.list.splice(i,1),a.update(),a.l.notify()},this.events.load=function(t,e,n){var i=function(t){var e={};e.exception=t.exception,e.request=t.request,"function"==typeof n&&n(e)},s=function(t){var n,s=t.request;try{n=JSON.parse(s.responseText)}catch(t){var l={};return l.exception=t,void i(l)}if(DayPilot.isArray(n)){var o={};if(o.preventDefault=function(){this.preventDefault.value=!0},o.data=n,"function"==typeof e&&e(o),o.preventDefault.value)return;a.events.list=n,a.m&&a.update()}};if(a.eventsLoadMethod&&"POST"===a.eventsLoadMethod.toUpperCase())DayPilot.Http.ajax({"method":"POST","data":{"start":a.visibleStart().toString(),"end":a.visibleEnd().toString()},"url":t,"success":s,"error":i});else{var l=t,o="start="+a.visibleStart().toString()+"&end="+a.visibleEnd().toString();l+=l.indexOf("?")>-1?"&"+o:"?"+o,DayPilot.Http.ajax({"method":"GET","url":l,"success":s,"error":i})}},this.events.forRange=function(t,e){t=new DayPilot.Date(t),e=new DayPilot.Date(e);var n=(a.events.list||[]).map(function(t){return new DayPilot.Event(t)});return n.sort(a.n),n.filter(function(n){var i=n.start(),a=n.end();return i===a&&i===t||DayPilot.Util.overlaps(t,e,i,a)})},this.update=function(t){if(a.o(t),this.m){if(a.a)throw new DayPilot.Exception("You are trying to update a DayPilot.Month instance that has been disposed.");if(this.cells){a.c(),a.d(),a.f(),a.g(),a.i(),a.j(),a.p(),a.k(),this.visible?this.show():this.hide()}}},this.q=null,this.o=function(t){if(t){var e={"events":{"preInit":function(){var t=this.data||[];DayPilot.isArray(t.list)?a.events.list=t.list:a.events.list=t}}};this.q=e;for(var n in t)if(e[n]){var i=e[n];i.data=t[n],i.preInit&&i.preInit()}else a[n]=t[n]}},this.r=function(){var t=this.q;for(var e in t){var n=t[e];n.postInit&&n.postInit()}},this.s={},this.s.events=[],this.t=function(t){var e=this.s.events,n=this.events.list[t],i={};for(var s in n)i[s]=n[s];if("function"==typeof this.onBeforeEventRender){var l={};l.control=a,l.data=i,this.onBeforeEventRender(l)}e[t]=i},this.f=function(){var t=this.events.list;if(t){if(!DayPilot.isArray(t))throw new DayPilot.Exception("DayPilot.Month.events.list expects an array object. You supplied: "+typeof t);if("function"==typeof this.onBeforeEventRender)for(var e=0;e<t.length;e++)this.t(e);for(var n=0;n<t.length;n++){var i=t[n];if("object"!=typeof i)throw new DayPilot.Exception("Event data item must be an object");if(!i.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(!i.end)throw new DayPilot.Exception("Event data item must specify 'end' property");var a=new DayPilot.Date(i.start),s=new DayPilot.Date(i.end);if(!(a.getTime()>s.getTime()))for(var e=0;e<this.rows.length;e++){var l=this.rows[e],o=new DayPilot.Event(i,this);l.belongsHere(o)&&(l.events.push(o),"function"==typeof this.onBeforeEventRender&&(o.cache=this.s.events[n]))}}for(var r=0;r<this.rows.length;r++){var l=this.rows[r];l.events.sort(this.n);for(var h=0;h<this.rows[r].events.length;h++){var d=l.events[h],c=l.getStartColumn(d),v=l.getWidth(d);l.putIntoLine(d,c,v,r)}}}},this.c=function(){for(var t=0;t<this.elements.events.length;t++){var e=this.elements.events[t];a.u(e)}this.elements.events=[]},this.u=function(t){!function(){var n=t.domArgs;if(t.domArgs=null,n&&"function"==typeof a.onBeforeEventDomRemove&&a.onBeforeEventDomRemove(n),n&&"function"==typeof a.onBeforeEventDomAdd){var i=n&&n.w;if(i){a.A.z&&e(n.element)&&(a.A.B=!0,a.A.C(i),a.A.B=!1)}}}(),t.event=null,t.click=null,t.parentNode.removeChild(t)},this.k=function(){this.D()},this.D=function(){this.elements.events=[];for(var t=0;t<this.rows.length;t++)for(var e=this.rows[t],n=0;n<e.lines.length;n++)for(var i=e.lines[n],a=0;a<i.length;a++)this.E(i[a])},this.n=function(t,e){if(!(t&&e&&t.start&&e.start))return 0;var n=t.start().getTime()-e.start().getTime();return 0!==n?n:e.end().getTime()-t.end().getTime()},this.drawShadow=function(e,n,i,s,l,o){l||(l=0);var r=s;this.shadow={},this.shadow.list=[],this.shadow.start={x:e,y:n},this.shadow.width=s;var h=7*n+e-l;h<0&&(r+=h,e=0,n=0);for(var d=l;d>=7;)n--,d-=7;if(d>e){d>e+(7-this.getColCount())?(n--,e=e+7-d):(r=r-d+e,e=0)}else e-=d;n<0&&(n=0,e=0);var c=null;t.resizingEvent?c="w-resize":t.movingEvent&&(c="move"),this.nav.top.style.cursor=c;var v=a.eventBorderRadius;for("number"==typeof v&&(v+="px");r>0&&n<this.rows.length;){var u=Math.min(this.getColCount()-e,r),f=this.rows[n],p=this.getRowTop(n),y=f.getHeight(),g=document.createElement("div");g.setAttribute("unselectable","on"),g.style.position="absolute",g.style.left=this.getCellWidth()*e+"%",g.style.width=this.getCellWidth()*u+"%",g.style.top=p+"px",g.style.height=y+"px",g.style.cursor=c,g.classList.add(a.F("_shadow"));var m=document.createElement("div");m.setAttribute("unselectable","on"),g.appendChild(m),m.style.position="absolute",m.style.top="0px",m.style.right="0px",m.style.left="0px",m.style.bottom="0px",m.classList.add(a.F("_shadow_inner")),v&&(r===s&&(g.style.borderTopLeftRadius=v,g.style.borderBottomLeftRadius=v,m.style.borderTopLeftRadius=v,m.style.borderBottomLeftRadius=v),r<=u&&(g.style.borderTopRightRadius=v,g.style.borderBottomRightRadius=v,m.style.borderTopRightRadius=v,m.style.borderBottomRightRadius=v)),this.nav.top.appendChild(g),this.shadow.list.push(g),r-=u+7-this.getColCount(),e=0,n++}},this.clearShadow=function(){if(this.shadow){for(var t=0;t<this.shadow.list.length;t++)this.nav.top.removeChild(this.shadow.list[t]);this.shadow=null,this.nav.top.style.cursor=""}},this.getEventTop=function(t,e){for(var n=this.headerHeight,i=0;i<t;i++)n+=this.rows[i].getHeight();return n+=this.cellHeaderHeight,n+=e*this.lineHeight()},this.getDateFromCell=function(t,e){return this.firstDate.addDays(7*e+t)},this.E=function(n){var i=n.cache||n.data,s=i.borderRadius||a.eventBorderRadius;"number"==typeof s&&(s+="px");var l=n.part.row,o=n.part.line,r=n.part.colStart,h=n.part.colWidth,d=this.getCellWidth()*r,c=this.getCellWidth()*h,v=this.getEventTop(l,o),u=document.createElement("div");u.setAttribute("unselectable","on"),u.style.height=this.eventHeight+"px",u.style.overflow="hidden",u.className=this.F("_event"),i.cssClass&&DayPilot.Util.addClass(u,i.cssClass),n.part.startsHere||DayPilot.Util.addClass(u,this.F("_event_continueleft")),n.part.endsHere||DayPilot.Util.addClass(u,this.F("_event_continueright")),u.event=n,u.style.width=c+"%",u.style.position="absolute",u.style.left=d+"%",u.style.top=v+"px",this.showToolTip&&n.client.toolTip()&&(u.title=n.client.toolTip()),u.onclick=a.G,u.oncontextmenu=a.H,u.onmousedown=function(e){e=e||window.event;var i=e.which||e.button;if(e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),1===i)if(t.movingEvent=null,"w-resize"===this.style.cursor||"e-resize"===this.style.cursor){var s={};s.start={},s.start.x=r,s.start.y=l,s.event=u.event,s.width=DayPilot.DateUtil.daysSpan(s.event.start(),s.event.end())+1,s.direction=this.style.cursor,t.resizingEvent=s}else if("move"===this.style.cursor||n.client.moveEnabled()){a.clearShadow();var d=DayPilot.mo3(a.nav.top,e);if(!d)return;var c=a.getCellBelowPoint(d.x,d.y),v=DayPilot.DateUtil.daysDiff(n.start(),a.rows[l].start),f=7*c.y+c.x-(7*l+r);v&&(f+=v);var p={};p.start={},p.start.x=r,p.start.y=l,p.start.line=o,p.offset=a.eventMoveToPosition?0:f,p.colWidth=h,p.event=u.event,p.coords=d,t.movingEvent=p}},u.onmousemove=function(e){if("undefined"!=typeof t&&!t.movingEvent&&!t.resizingEvent){var i=DayPilot.mo3(u,e);if(i){u.deleteIcon&&(u.deleteIcon.style.display="");var a=6;i.x<=a&&n.client.resizeEnabled()?n.part.startsHere?(u.style.cursor="w-resize",u.dpBorder="left"):u.style.cursor="not-allowed":u.clientWidth-i.x<=a&&n.client.resizeEnabled()?n.part.endsHere?(u.style.cursor="e-resize",u.dpBorder="right"):u.style.cursor="not-allowed":n.client.clickEnabled()?u.style.cursor="pointer":u.style.cursor="default"}}},u.onmouseleave=function(t){u.deleteIcon&&(u.deleteIcon.style.display="none"),u.style.cursor=""},u.onmouseenter=function(t){u.deleteIcon&&(u.deleteIcon.style.display="")};var f=document.createElement("div");if(f.setAttribute("unselectable","on"),f.className=this.F("_event_inner"),"darker"===i.borderColor&&i.backColor?f.style.borderColor=DayPilot.ColorUtil.darker(i.backColor,2):f.style.borderColor=i.borderColor,i.backColor&&(f.style.background=i.backColor),i.fontColor&&(f.style.color=i.fontColor),s&&(u.style.borderRadius=s,f.style.borderRadius=s),u.appendChild(f),n.client.barVisible()){var p=document.createElement("div");p.setAttribute("unselectable","on"),p.className=this.F("_event_bar"),p.style.position="absolute";var y=document.createElement("div");y.setAttribute("unselectable","on"),y.className=this.F("_event_bar_inner"),y.style.top="0%",y.style.height="100%",i.barColor&&(y.style.backgroundColor=i.barColor),p.appendChild(y),u.appendChild(p)}if(n.client.deleteEnabled()){var g=Math.floor(a.eventHeight/2-9),m=document.createElement("div");m.style.position="absolute",m.style.right="2px",m.style.top=g+"px",m.style.width="18px",m.style.height="18px",m.className=a.F("_event_delete"),m.onmousedown=function(t){t.stopPropagation()},m.onclick=function(t){t.stopPropagation();var e=this.parentNode.event;e&&a.I(e)},m.style.display="none",u.deleteIcon=m,u.appendChild(m)}var D=i.areas?DayPilot.Areas.copy(i.areas):[];if(DayPilot.Areas.attach(u,n,{"areas":D}),"function"==typeof a.onAfterEventRender){var w={};w.e=u.event,w.div=u,a.onAfterEventRender(w)}!function(){var t={};if(t.control=a,t.e=n,t.element=null,u.domArgs=t,"function"==typeof a.onBeforeEventDomAdd&&a.onBeforeEventDomAdd(t),t.element){var i=f;if(i){t.w=i;if(e(t.element)){if(!a.A.z)throw new DayPilot.Exception("Can't reach Vue");a.A.B=!0,a.A.J(t.element,i,{"style":{"flexGrow":1}}),a.A.B=!1}else i.appendChild(t.element)}}else f.innerHTML=n.client.innerHTML()}(),this.elements.events.push(u),this.nav.events.appendChild(u)},this.lastVisibleDayOfMonth=function(){return this.startDate.lastDayOfMonth()},this.d=function(){"string"==typeof this.startDate&&(this.startDate=new DayPilot.Date(this.startDate)),this.startDate=this.startDate.firstDayOfMonth(),this.firstDate=this.startDate.firstDayOfWeek(this.getWeekStart());var t,e=(this.startDate,this.lastVisibleDayOfMonth()),n=DayPilot.DateUtil.daysDiff(this.firstDate,e)+1;t=Math.ceil(n/7),this.days=7*t,this.rows=[];for(var i=0;i<t;i++){var s={};s.start=this.firstDate.addDays(7*i),s.end=s.start.addDays(this.getColCount()),s.events=[],s.lines=[],s.index=i,s.minHeight=this.cellHeight,s.calendar=this,s.belongsHere=function(t){return t.end().getTime()===t.start().getTime()&&t.start().getTime()===this.start.getTime()||!(t.end().getTime()<=this.start.getTime()||t.start().getTime()>=this.end.getTime())},s.getPartStart=function(t){return DayPilot.DateUtil.max(this.start,t.start())},s.getPartEnd=function(t){return DayPilot.DateUtil.min(this.end,t.end())},s.getStartColumn=function(t){var e=this.getPartStart(t);return DayPilot.DateUtil.daysDiff(this.start,e)},s.getWidth=function(t){return DayPilot.DateUtil.daysSpan(this.getPartStart(t),this.getPartEnd(t))+1},s.putIntoLine=function(t,e,n,i){for(var a=this,s=0;s<this.lines.length;s++){var l=this.lines[s];if(l.isFree(e,n))return l.addEvent(t,e,n,i,s),s}var l=[];return l.isFree=function(t,e){for(var n=!0,i=0;i<this.length;i++)t+e-1<this[i].part.colStart||t>this[i].part.colStart+this[i].part.colWidth-1||(n=!1);return n},l.addEvent=function(t,e,n,i,s){t.part.colStart=e,t.part.colWidth=n,t.part.row=i,t.part.line=s,t.part.startsHere=a.start.getTime()<=t.start().getTime(),t.part.endsHere=a.end.getTime()>=t.end().getTime(),this.push(t)},l.addEvent(t,e,n,i,this.lines.length),this.lines.push(l),this.lines.length-1},s.getStart=function(){for(var t=0,e=0;e<a.rows.length&&e<this.index;e++)t+=a.rows[e].getHeight()},s.getHeight=function(){return Math.max(this.lines.length*a.lineHeight()+a.cellHeaderHeight+a.cellMarginBottom,this.calendar.cellHeight)},this.rows.push(s)}this.endDate=this.firstDate.addDays(7*t)},this.visibleStart=function(){return a.firstDate},this.visibleEnd=function(){return a.endDate},this.getHeight=function(){for(var t=this.headerHeight,e=0;e<this.rows.length;e++)t+=this.rows[e].getHeight();return t},this.getWidth=function(t,e){return 7*e.y+e.x-(7*t.y+t.x)+1},this.getMinCoords=function(t,e){return 7*t.y+t.x<7*e.y+e.x?t:e},this.F=function(t){var e=this.theme||this.cssClassPrefix;return e?e+t:""},this.K=function(){var e=this.nav.top;e.setAttribute("unselectable","on"),e.style.MozUserSelect="none",e.style.KhtmlUserSelect="none",e.style.WebkitUserSelect="none",e.style.position="relative",this.width&&(e.style.width=this.width),e.style.height=this.getHeight()+"px",e.onselectstart=function(t){return!1},this.hideUntilInit&&(e.style.visibility="hidden"),this.visible||(e.style.display="none"),e.className=this.F("_main");var n=document.createElement("div");this.nav.cells=n,n.style.position="absolute",n.style.left="0px",n.style.right="0px",n.setAttribute("unselectable","on"),e.appendChild(n);var i=document.createElement("div");this.nav.events=i,i.style.position="absolute",i.style.left="0px",i.style.right="0px",i.setAttribute("unselectable","on"),e.appendChild(i),e.onmousemove=function(e){if(t.resizingEvent){var n=DayPilot.mo3(a.nav.top,e);if(!n)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var s,l,o=t.resizingEvent;o.start;if("w-resize"===o.direction){l=i;var r=o.event.end();r.getDatePart()===r&&(r=r.addDays(-1));var h=a.getCellFromDate(r);s=a.getWidth(i,h)}else l=a.getCellFromDate(o.event.start()),s=a.getWidth(l,i);s<1&&(s=1),a.drawShadow(l.x,l.y,0,s)}else if(t.movingEvent){var n=DayPilot.mo3(a.nav.top,e);if(!n)return;if(n.x===t.movingEvent.coords.x&&n.y===t.movingEvent.coords.y)return;var d=3,c=Math.abs(n.x-t.movingEvent.coords.x)+Math.abs(n.y-t.movingEvent.coords.y);if(c<=d)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var v=t.movingEvent.event,u=t.movingEvent.offset,s=a.cellMode?1:DayPilot.DateUtil.daysSpan(v.start(),v.end())+1;s<1&&(s=1),a.drawShadow(i.x,i.y,0,s,u,v)}else if(t.timeRangeSelecting){var n=DayPilot.mo3(a.nav.top,e);if(!n)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var l=t.timeRangeSelecting,f=7*l.y+l.x,p=7*i.y+i.x,s=Math.abs(p-f)+1;s<1&&(s=1);var y=f<p?l:i;t.timeRangeSelecting.from={x:y.x,y:y.y},t.timeRangeSelecting.width=s,t.timeRangeSelecting.moved=!0,a.drawShadow(y.x,y.y,0,s,0,null)}}},this.j=function(){this.nav.top.style.height=this.getHeight()+"px";for(var t=0;t<this.cells.length;t++)for(var e=0;e<this.cells[t].length;e++)this.cells[t][e].style.top=this.getRowTop(e)+"px",this.cells[t][e].style.height=this.rows[e].getHeight()+"px"},this.getCellBelowPoint=function(t,e){for(var n=Math.floor(this.nav.top.clientWidth/this.getColCount()),i=Math.min(Math.floor(t/n),this.getColCount()-1),a=null,s=this.headerHeight,l=0,o=0;o<this.rows.length;o++){var r=s;if(s+=this.rows[o].getHeight(),e<s){l=e-r,a=o;break}}null===a&&(a=this.rows.length-1);var h={};return h.x=i,h.y=a,h.relativeY=l,h},this.getCellFromDate=function(t){for(var e=DayPilot.DateUtil.daysDiff(this.firstDate,t),n={x:0,y:0};e>=7;)n.y++,e-=7;return n.x=e,n},this.i=function(){var t=document.createElement("div");t.oncontextmenu=function(){return!1},this.nav.cells.appendChild(t),this.cells=[];for(var e=0;e<this.getColCount();e++){this.cells[e]=[];var n=document.createElement("div");n.setAttribute("unselectable","on"),n.style.position="absolute",n.style.left=this.getCellWidth()*e+"%",n.style.width=this.getCellWidth()+"%",n.style.top="0px",n.style.height=this.headerHeight+"px";var i=e+this.getWeekStart();i>6&&(i-=7),n.className=this.F("_header");var a=document.createElement("div");a.setAttribute("unselectable","on"),a.innerHTML=s.locale().dayNames[i],n.appendChild(a),a.style.position="absolute",a.style.top="0px",a.style.bottom="0px",a.style.left="0px",a.style.right="0px",a.className=this.F("_header_inner"),a.innerHTML=s.locale().dayNames[i],t.appendChild(n);for(var l=0;l<this.rows.length;l++)this.L(e,l,t)}},this.g=function(){for(var t=0;t<this.cells.length;t++)for(var e=0;e<this.cells[t].length;e++){var n=a.cells[t][e];a.M(n)}this.nav.cells.innerHTML=""},this.M=function(t){!function(){var n=t.domArgs;if(t.domArgs=null,n&&"function"==typeof a.onBeforeCellDomRemove&&a.onBeforeCellDomRemove(n),n&&"function"==typeof a.onBeforeCellDomAdd){var i=n&&n.w;if(i){var s=a.N.reactDOM&&isReactCmp(n.element),l=a.A.z&&(isVueCmp(n.element)||e(n.element));s?a.N.O(i):l&&(a.A.B=!0,a.A.C(i),a.A.B=!1)}}}(),t.onclick=null},this.P=function(){return 2===a.api},this.L=function(n,i,l){var o=this.rows[i],r=this.firstDate.addDays(7*i+n),h=r.getDay(),d=null;d=1===h?s.locale().monthNames[r.getMonth()]+" "+h:""+h;var c=!a.isWeekend(r),v={"start":r,"end":r.addDays(1),"properties":{"headerHtml":d,"backColor":null,"business":c,"html":null}},u={};u.control=a,u.cell=v,"function"==typeof a.onBeforeCellRender&&a.onBeforeCellRender(u);var f=u.cell.properties,p=document.createElement("div");if(p.setAttribute("unselectable","on"),p.style.position="absolute",p.style.cursor="default",p.style.left=this.getCellWidth()*n+"%",p.style.width=this.getCellWidth()+"%",p.style.top=this.getRowTop(i)+"px",p.style.height=o.getHeight()+"px",p.className=this.F("_cell"),f.business){var c=this.F("_cell_business");DayPilot.Util.addClass(p,c)}var y=(this.startDate.addMonths(-1).getMonth(),this.startDate.addMonths(1).getMonth(),this.startDate.getMonth(),document.createElement("div"));y.setAttribute("unselectable","on"),p.appendChild(y),y.style.position="absolute",y.style.left="0px",y.style.right="0px",y.style.top="0px",y.style.bottom="0px",y.className=this.F("_cell_inner"),f.backColor&&(y.style.backgroundColor=u.cell.properties.backColor),p.onmousedown=function(e){"Disabled"!==a.timeRangeSelectedHandling&&(a.clearShadow(),t.timeRangeSelecting={"root":a,"x":n,"y":i,"from":{x:n,y:i},"width":1})},p.onclick=function(){var t=function(t){var e=new DayPilot.Date(t),n=e.addDays(1);a.Q(e,n)};if("Disabled"!==a.timeRangeSelectedHandling)return void t(r)};var g=document.createElement("div");if(g.setAttribute("unselectable","on"),g.style.height=this.cellHeaderHeight+"px",g.className=this.F("_cell_header"),g.onclick=function(t){if("Enabled"===a.cellHeaderClickHandling){t.stopPropagation();var e={};e.control=a,e.start=r,e.end=r.addDays(1),e.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onCellHeaderClick&&(a.onCellHeaderClick(e),e.preventDefault.value)||"function"==typeof a.onCellHeaderClicked&&a.onCellHeaderClicked(e)}},g.innerHTML=f.headerHtml,y.appendChild(g),f.html){var m=document.createElement("div");m.style.height=o.getHeight()-this.cellHeaderHeight+"px",m.style.overflow="hidden",m.innerHTML=f.html,y.appendChild(m)}!function(){if("function"==typeof a.onBeforeCellDomAdd||"function"==typeof a.onBeforeCellDomRemove){var t={};if(t.control=a,t.cell=v,t.element=null,p.domArgs=t,"function"==typeof a.onBeforeCellDomAdd&&a.onBeforeCellDomAdd(t),t.element){var n=y;if(n){t.w=n;if(e(t.element)){if(!a.A.z)throw new DayPilot.Exception("Can't reach Vue");a.A.B=!0,a.A.J(t.element,n),a.A.B=!1}else n.appendChild(t.element)}}}}(),this.cells[n][i]=p,l.appendChild(p)},this.getWeekStart=function(){if("Auto"===a.weekStarts){var t=s.locale();return t?t.weekStarts:0}return a.weekStarts||0},this.getColCount=function(){return 7},this.getCellWidth=function(){return 14.285},this.getRowTop=function(t){for(var e=this.headerHeight,n=0;n<t;n++)e+=this.rows[n].getHeight();return e},this.R=function(t,e,n){var i={};i.action=t,i.parameters=n,i.data=e,i.header=this.S();var a="JSON"+DayPilot.JSON.stringify(i);this.backendUrl&&DayPilot.request(this.backendUrl,this.T,a,this.ajaxError)},this.T=function(t){a.b(t.responseText)},this.S=function(){var t={};return t.control="dpm",t.id=this.id,t.v=this.v,t.visibleStart=new DayPilot.Date(this.firstDate),t.visibleEnd=t.visibleStart.addDays(this.days),t.startDate=a.startDate,t.timeFormat=this.timeFormat,t.weekStarts=this.weekStarts,t},this.eventClickCallBack=function(t,e){this.R("EventClick",e,t)},this.G=function(e){t.movingEvent=null,t.resizingEvent=null;var n=this,e=e||window.event;e.ctrlKey;e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),a.eventClickSingle(n,e)},this.eventClickSingle=function(t,e){var n=t.event;if(n&&n.client.clickEnabled())if(a.P()){var i={};if(i.e=n,i.control=a,i.div=t,i.originalEvent=e,i.meta=e.metaKey,i.ctrl=e.ctrlKey,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventClick&&(a.l.apply(function(){a.onEventClick(i)}),i.preventDefault.value))return;switch(a.eventClickHandling){case"CallBack":a.eventClickCallBack(n);break;case"ContextMenu":var s=n.client.contextMenu();s?s.show(n):a.contextMenu&&a.contextMenu.show(n)}"function"==typeof a.onEventClicked&&a.l.apply(function(){a.onEventClicked(i)})}else switch(a.eventClickHandling){case"CallBack":a.eventClickCallBack(n);break;case"JavaScript":a.onEventClick(n)}},this.H=function(){var t=this;return a.U(t.event),!1},this.U=function(t){if(this.event=t,!t.client.rightClickEnabled())return!1;var e={};if(e.e=t,e.preventDefault=function(){this.preventDefault.value=!0},"function"!=typeof a.onEventRightClick||(a.onEventRightClick(e),!e.preventDefault.value)){switch(a.eventRightClickHandling){case"ContextMenu":var n=t.client.contextMenu();n?n.show(t):a.contextMenu&&a.contextMenu.show(this.event)}return"function"==typeof a.onEventRightClicked&&a.onEventRightClicked(e),!1}},this.I=function(t){if(a.P()){var e={};if(e.e=t,e.control=a,e.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventDelete&&(a.l.apply(function(){a.onEventDelete(e)}),e.preventDefault.value))return;switch(a.eventDeleteHandling){case"CallBack":a.eventDeleteCallBack(t);break;case"PostBack":a.eventDeletePostBack(t);break;case"Update":a.events.remove(t)}"function"==typeof a.onEventDeleted&&a.l.apply(function(){a.onEventDeleted(e)})}else switch(a.eventDeleteHandling){case"PostBack":a.eventDeletePostBack(t);break;case"CallBack":a.eventDeleteCallBack(t);break;case"JavaScript":a.onEventDelete(t)}},this.eventDeleteCallBack=function(t,e){this.R("EventDelete",e,t)},this.eventDeletePostBack=function(t,e){this.V("EventDelete",e,t)},this.eventMoveCallBack=function(t,e,n,i,a){if(!e)throw"newStart is null";if(!n)throw"newEnd is null";var s={};s.e=t,s.newStart=e,s.newEnd=n,s.position=a,this.R("EventMove",i,s)},this.W=function(t,e,n,i,s,l){var o=t.start().getTimePart(),r=t.end().getDatePart();r.getTime()!==t.end().getTime()&&(r=r.addDays(1));var h=DayPilot.DateUtil.diff(t.end(),r),d=this.getDateFromCell(e,n);d=d.addDays(-i);var c=DayPilot.DateUtil.daysSpan(t.start(),t.end())+1,v=d.addDays(c),u=d.addTime(o),f=v.addTime(h);if(a.P()){var p={};if(p.e=t,p.control=a,p.newStart=u,p.newEnd=f,p.ctrl=s.ctrlKey,p.shift=s.shiftKey,p.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventMove&&(a.l.apply(function(){a.onEventMove(p)}),p.preventDefault.value))return;switch(a.eventMoveHandling){case"CallBack":a.eventMoveCallBack(t,u,f);break;case"Update":t.start(u),t.end(f),a.events.update(t)}"function"==typeof a.onEventMoved&&a.l.apply(function(){a.onEventMoved(p)})}else switch(a.eventMoveHandling){case"CallBack":a.eventMoveCallBack(t,u,f);break;case"JavaScript":a.onEventMove(t,u,f)}},this.eventResizeCallBack=function(t,e,n,i){if(!e)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=t,a.newStart=e,a.newEnd=n,this.R("EventResize",i,a)},this.X=function(t,e,n){var i=t.start().getTimePart(),s=t.end().getDatePart();s.getTime()!==t.end().getTime()&&(s=s.addDays(1));var l=DayPilot.DateUtil.diff(t.end(),s),o=this.getDateFromCell(e.x,e.y),r=o.addDays(n),h=o.addTime(i),d=r.addTime(l);if(a.P()){var c={};if(c.e=t,c.control=a,c.newStart=h,c.newEnd=d,c.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventResize&&(a.l.apply(function(){a.onEventResize(c)}),c.preventDefault.value))return;switch(a.eventResizeHandling){case"CallBack":a.eventResizeCallBack(t,h,d);break;case"Update":t.start(h),t.end(d),a.events.update(t)}"function"==typeof a.onEventResized&&a.l.apply(function(){a.onEventResized(c)})}else switch(a.eventResizeHandling){case"CallBack":a.eventResizeCallBack(t,h,d);break;case"JavaScript":a.onEventResize(t,h,d)}},this.timeRangeSelectedCallBack=function(t,e,n){var i={};i.start=t,i.end=e,this.R("TimeRangeSelected",n,i)},this.Q=function(t,e){if(this.P()){var n={};if(n.control=a,n.start=t,n.end=e,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onTimeRangeSelect&&(a.l.apply(function(){a.onTimeRangeSelect(n)}),n.preventDefault.value))return;switch(a.timeRangeSelectedHandling){case"CallBack":a.timeRangeSelectedCallBack(t,e)}"function"==typeof a.onTimeRangeSelected&&a.l.apply(function(){a.onTimeRangeSelected(n)})}else switch(a.timeRangeSelectedHandling){case"CallBack":a.timeRangeSelectedCallBack(t,e);break;case"JavaScript":a.onTimeRangeSelected(t,e)}},this.l={},this.l.scope=null,this.l.notify=function(){a.l.scope&&a.l.scope["$apply"]()},this.l.apply=function(t){t()},this.clearSelection=function(){a.clearShadow()},this.commandCallBack=function(t,e){var n={};n.command=t,this.R("Command",e,n)},this.isWeekend=function(t){return t=new DayPilot.Date(t),0===t.dayOfWeek()||6===t.dayOfWeek()},this.Y={},this.Y.locale=function(){var t=DayPilot.Locale.find(a.locale);return t?t:DayPilot.Locale.US},this.Y.Z=function(){return"Disabled"!==a.xssProtection};var s=this.Y;this.debug=function(t,e){this.debuggingEnabled&&(a.debugMessages||(a.debugMessages=[]),a.debugMessages.push(t),"undefined"!=typeof console&&console.log(t))},this.dispose=function(){var t=a;t.a||(t.a=!0,t.g(),t.c(),t.nav.top.removeAttribute("style"),t.nav.top.removeAttribute("class"),t.nav.top.innerHTML="",t.nav.top.dp=null,t.nav.top.onmousemove=null,t.nav.top=null)},this.disposed=function(){return this.a},this.$=function(){t.globalHandlers||(t.globalHandlers=!0,DayPilot.re(document,"mouseup",t.gMouseUp))},this.loadFromServer=function(){return!(!this.backendUrl&&"function"!=typeof WebForm_DoCallback)&&("undefined"==typeof a.events.list||!a.events.list)},this.p=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.show=function(){a.visible=!0,a.nav.top.style.display=""},this.hide=function(){a.visible=!1,a.nav.top.style.display="none"},this._=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Month() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Month: The placeholder element not found: '"+id+"'."}},this.aa=function(){this.d(),this.K(),this.i(),this.$(),this.R("Init")},this.ba=function(t,e){return a.Y.Z()?DayPilot.Util.escapeTextHtml(t,e):DayPilot.Util.isNullOrUndefined(e)?DayPilot.Util.isNullOrUndefined(t)?"":t:e},this.A={},this.A.z=null,this.A.ca=function(t,e,n){var i=a.A.z;if("function"==typeof i.createVNode&&"function"==typeof i.render){var s=i.createVNode(t,n);i.render(s,e)}},this.A.J=function(t,e,n){var i=a.A.z;if("function"==typeof i.render){var s=t;DayPilot.isArray(t)&&(s=i.h("div",n,t)),i.render(s,e)}},this.A.C=function(t){var e=a.A.z;"function"==typeof e.render&&e.render(null,t)},this.internal={},this.internal.loadOptions=this.o,this.internal.xssTextHtml=a.ba,this.internal.enableVue=function(t){a.A.z=t},this.internal.vueRef=function(){return a.A.z},this.internal.vueRendering=function(){return a.A.B},this.init=function(){return this._(),this.loadFromServer()?void this.aa():(this.d(),this.f(),this.K(),this.i(),this.p(),this.k(),this.$(),this.fireAfterRenderDetached(null,!1),this.m=!0,this)},this.Init=this.init,Object.defineProperty(this,"durationBarVisible",{get:function(){return a.eventBarVisible}}),this.o(i)},t.gMouseUp=function(e){if(t.movingEvent){var n=t.movingEvent;if(!n.event)return;if(!n.event.calendar)return;if(!n.event.calendar.shadow)return;if(!n.event.calendar.shadow.start)return;var i=t.movingEvent.event.calendar,a=t.movingEvent.event,s=i.shadow.start,l=i.shadow.position,o=t.movingEvent.offset;i.clearShadow(),t.movingEvent=null;var e=e||window.event;return i.W(a,s.x,s.y,o,e,l),e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),t.movingEvent=null,!1}if(t.resizingEvent){var n=t.resizingEvent;if(!n.event)return;if(!n.event.calendar)return;if(!n.event.calendar.shadow)return;if(!n.event.calendar.shadow.start)return;var i=t.resizingEvent.event.calendar,a=t.resizingEvent.event,s=i.shadow.start,r=i.shadow.width;return i.clearShadow(),t.resizingEvent=null,i.X(a,s,r),e.cancelBubble=!0,t.resizingEvent=null,!1}if(t.timeRangeSelecting){if(t.timeRangeSelecting.moved){
var h=t.timeRangeSelecting,i=h.root,s=new DayPilot.Date(i.getDateFromCell(h.from.x,h.from.y)),d=s.addDays(h.width);i.Q(s,d),i.clearShadow()}t.timeRangeSelecting=null}},DayPilot.Month=t.Month,"undefined"!=typeof jQuery&&!function(t){t.fn.daypilotMonth=function(t){var e=null,n=this.each(function(){if(!this.daypilot){var n=new DayPilot.Month(this.id);this.daypilot=n;for(name in t)n[name]=t[name];n.Init(),e||(e=n)}});return 1===this.length?e:n}}(jQuery),function(){var t=DayPilot.am();t&&t.directive("daypilotMonth",["$parse",function(t){return{"restrict":"E","template":"<div></div>","replace":!0,"link":function(e,n,i){var a=new DayPilot.Month(n[0]);a.l.scope=e,a.init();var s=i["id"];s&&(e[s]=a);var l=i["publishAs"];if(l){(0,t(l).assign)(e,a)}for(var o in i)0===o.indexOf("on")&&!function(n){a[n]=function(a){var s=t(i[n]);e["$apply"](function(){s(e,{"args":a})})}}(o);var r=e["$watch"],h=i["config"]||i["daypilotConfig"],d=i["events"]||i["daypilotEvents"];r.call(e,h,function(t){for(var e in t)a[e]=t[e];a.update()},!0),r.call(e,d,function(t){a.events.list=t,a.update()},!0)}}}])}(),"undefined"!=typeof Sys&&Sys.Application&&Sys.Application.notifyScriptLoaded&&Sys.Application.notifyScriptLoaded()}}();