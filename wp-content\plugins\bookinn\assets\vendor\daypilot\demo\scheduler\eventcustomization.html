﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Customization (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">Read more about <a href="https://doc.daypilot.org/scheduler/event-customization/">event
        customization</a> [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            // behavior and appearance
            cellWidth: 80,
            eventHeight: 40,

            // view
            startDate: new DayPilot.Date("2025-03-01"),
            days: new DayPilot.Date("2025-03-01").daysInMonth(),
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Day", format: "d"}
            ],
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"}
            ],
            onBeforeEventRender: args => {
                if (args.e.type === "disabled") {
                    args.e.barColor = "#9a0";
                    args.e.barBackColor = "#ac0";
                    args.e.moveDisabled = true;
                    args.e.areas = [{html: "Disabled", right: 2, bottom: 2}];
                }
            },
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            }
        });
        dp.init();

        const app = {
            init() {
                this.loadEventData();
            },
            loadEventData() {
                // generate and load events
                const events = [
                    {
                        start: "2025-03-02T12:00:00",
                        end: "2025-03-05T12:00:00",
                        id: 1,
                        resource: "A",
                        text: "Event 1",
                        type: "disabled"
                    },
                    {
                        start: "2025-03-01T00:00:00",
                        end: "2025-03-06T00:00:00",
                        id: 2,
                        resource: "B",
                        text: "Event 1"
                    }
                ];

                dp.update({events});
            }
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

