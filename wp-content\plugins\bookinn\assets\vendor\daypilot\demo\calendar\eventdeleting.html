﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Deleting (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">Read more about <a href="https://doc.daypilot.org/calendar/event-deleting/">event deleting</a>.</div>

    <div id="dp"></div>

    <script type="text/javascript">

        var dp = new DayPilot.Calendar("dp");

        // view
        dp.startDate = "2021-03-25";
        dp.viewType = "Week";

        // event creating
        dp.onTimeRangeSelected = function (args) {
            var name = prompt("New event name:", "Event");
            if (!name) return;
            var e = new DayPilot.Event({
                start: args.start,
                end: args.end,
                id: DayPilot.guid(),
                text: name
            });
            dp.events.add(e);
            dp.clearSelection();
        };

        dp.eventDeleteHandling = "Update";
        dp.onEventDelete = function (args) {
/*
            if (!confirm("Do you really want to delete this event?")) {
                args.preventDefault();
            }
*/
        };

        dp.headerDateFormat = "dddd";
        dp.init();

        var e = new DayPilot.Event({
            start: new DayPilot.Date("2021-03-25T12:00:00"),
            end: new DayPilot.Date("2021-03-25T12:00:00").addHours(3).addMinutes(15),
            id: "1",
            text: "Special event"
        });
        dp.events.add(e);

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

