﻿<!DOCTYPE html>
<html>
<head>
    <title>Time Headers (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">The Scheduler can display multiple time header rows, each with a defined resolution and date format. <br>Read more about the <a href="https://doc.daypilot.org/scheduler/time-header-rows/">time
        header rows</a> [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            startDate: new DayPilot.Date("2025-03-01"),
            days: 90,
            scale: "Day",
            timeHeaders: [
                {groupBy: 'Month', height: 25},
                {groupBy: 'Week'},
                {groupBy: 'Day', format: "d"}
            ],
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"}
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) {
                    return;
                }
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
            onBeforeTimeHeaderRender: args => {
                if (args.header.level === 1) {
                    args.header.html = "Week " + args.header.text;
                }
            }
        });
        dp.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

