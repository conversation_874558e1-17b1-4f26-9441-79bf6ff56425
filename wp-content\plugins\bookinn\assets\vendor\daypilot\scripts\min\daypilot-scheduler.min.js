﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";function t(t){var e=t.touches[0].pageX,i=t.touches[0].pageY,n={};return n.x=e,n.y=i,n}if("undefined"==typeof DayPilot.Scheduler){var e=navigator.userAgent.indexOf("iPad")>-1||navigator.userAgent.indexOf("iPhone")>-1,i={},n=function(){},s=!1,r=function(){return document.body},a=function(){return document.createElement("div")};DayPilot.Scheduler=function(s,o){this.v="2025.3.696-lite";var l=this;this.isScheduler=!0,this.id=s,this.beforeCellRenderCaching=!0,this.businessBeginsHour=9,this.businessEndsHour=18,this.businessWeekends=!1,this.cellDuration=60,this.cellGroupBy="Day",this.cellSweeping=!0,this.cellSweepingCacheSize=1e3,this.cellWidth=40,this.cellsMarkBusiness=!0,this.cssClassPrefix="scheduler_default",this.days=1,this.durationBarHeight=3,this.durationBarVisible=!0,this.dynamicEventRendering="Progressive",this.dynamicEventRenderingMargin=50,this.dynamicEventRenderingMarginX=null,this.dynamicEventRenderingMarginY=null,this.dynamicEventRenderingCacheSweeping=!1,this.dynamicEventRenderingCacheSize=200,this.eventBorderRadius=null,this.eventEndSpec="DateTime",this.eventHeight=35,this.eventMinWidth=1,this.eventResizeMargin=5,this.eventTapAndHoldHandling="Move",this.eventTextWrappingEnabled=!1,this.eventsLoadMethod="GET",this.floatingEvents=!e,this.floatingTimeHeaders=!0,this.headerHeight=30,this.heightSpec="Max",this.height=600,this.locale="en-us",this.progressiveRowRendering=!0,this.progressiveRowRenderingPreload=25,this.rowHeaderWidth=80,this.rowMarginTop=0,this.rowMarginBottom=0,this.rowsLoadMethod="GET",this.scale="CellDuration",this.scrollDelayEvents=200,this.scrollDelayCells=e?100:0,this.scrollDelayFloats=0,this.scrollDelayRows=0,this.showToolTip=!0,this.snapToGrid=!0,this.startDate=DayPilot.Date.today(),this.tapAndHoldTimeout=300,this.timeHeaders=[{"groupBy":"Default"},{"groupBy":"Cell"}],this.timeHeaderTextWrappingEnabled=!1,this.timeFormat="Auto",this.useEventBoxes="Always",this.visible=!0,this.weekStarts="Auto",this.width=null,this.xssProtection="Enabled",this.eventClickHandling="Enabled",this.eventDeleteHandling="Disabled",this.eventMoveHandling="Update",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.timeHeaderClickHandling="Enabled",this.timeHeaderRightClickHandling="Enabled",this.timeRangeClickHandling="Enabled",this.timeRangeSelectedHandling="Enabled",this.onEventClick=null,this.onEventClicked=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onRowClick=null,this.onRowClicked=null,this.onTimeHeaderClick=null,this.onTimeHeaderClicked=null,this.onTimeHeaderRightClick=null,this.onTimeHeaderRightClicked=null,this.onTimeRangeClick=null,this.onTimeRangeClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onBeforeCellRender=null,this.onBeforeEventRender=null,this.onBeforeRowHeaderRender=null,this.onAfterUpdate=null,this.a=!1,this.b=-1,this.c=!0,this.rowlist=[],this.events={},this.cells={},this.elements={},this.elements.events=[],this.elements.bars=[],this.elements.text=[],this.elements.cells=[],this.elements.linesVertical=[],this.elements.range=[],this.elements.timeHeader=[],this.d={},this.d.cells=[],this.d.linesVertical={},this.d.linesHorizontal={},this.d.timeHeaderGroups=[],this.d.timeHeader={},this.d.events=[],this.nav={},this.scrollTo=function(t){l.f(t)},this.f=function(t){if(t){if(!l.g)return void(l.j=t);var e;if(t instanceof DayPilot.Date)e=this.getPixels(t).left;else if("string"==typeof t)e=this.getPixels(new DayPilot.Date(t)).left;else{if("number"!=typeof t)throw new DayPilot.Exception("Invalid scrollTo() parameter. Accepted parameters: string (ISO date), number (pixels), DayPilot.Date object");e=t}var i=l.k.clientWidth,n=l.nav.scroll.clientWidth;e<0&&(e=0),e>i-n&&(e=i-n),l.l(e)}},this.scrollToResource=function(t){DayPilot.complete(function(){var e;if("string"==typeof t||"number"==typeof t)e=l.m(t);else{if(!(t instanceof DayPilot.Row))throw new DayPilot.Exception("Invalid scrollToResource() argument: id or DayPilot.Row expected");e=l.m(t.id)}e&&setTimeout(function(){var t=e.top;l.nav.scroll.scrollTop=t},100)})},this.n=function(){if(this.floatingTimeHeaders&&this.timeHeader){var t=l.o();if(t){l.p();for(var e=t.pixels.left,i=t.pixels.right+t.sw,n=[],s=0;s<this.timeHeader.length;s++)for(var r=0;r<this.timeHeader[s].length;r++){var a=this.timeHeader[s][r],o=a.left,c=a.left+a.width,d=null;if(o<e&&e<c&&(d={},d.x=r,d.y=s,d.marginLeft=e-o,d.marginRight=0,d.div=l.d.timeHeader[r+"_"+s],n.push(d)),o<i&&i<c){d||(d={},d.x=r,d.y=s,d.marginLeft=0,d.div=l.d.timeHeader[r+"_"+s],n.push(d)),d.marginRight=c-i;break}}for(var h=0;h<n.length;h++){var d=n[h];l.q(d.div,d.marginLeft,d.marginRight)}}}},this.r=function(){l.n(),l.s()},this.t={};var c=l.t;c.u=function(t,e,i,n){var s=t,r=t+i,a=e,o=e+n;return l.elements.events.filter(function(t){var e=t.event,i=e.part.left,n=e.part.left+e.part.width,c=l.rowlist[e.part.dayIndex],d=c.top+e.part.top,h=d+l.eventHeight;if(DayPilot.Util.overlaps(i,n,s,r)&&DayPilot.Util.overlaps(d,h,a,o))return!0})},c.z=function(){var t=[],e=l.o();if(!e)return t;for(var i=e.pixels.left,n=0;n<l.elements.events.length;n++){var s=l.elements.events[n],r=s.event,a=r.part.left,o=r.part.left+r.part.width;a<i&&i<o&&t.push(s)}return t.area=e,t},this.s=function(){if(this.floatingEvents){var t=c.z();l.A=performance.now(),t.forEach(function(e){var i=e.event,n=t.area.pixels.left,s=i.part.left,r=n-s;l.B(e,r,0)}),l.C()}},this.elements.sections=[],this.elements.hsections=[],this.q=function(t,e,i){var n=a();n.className=l.D("_timeheader_float"),n.style.position="absolute",n.style.left=e+"px",n.style.right=i+"px",n.style.top="0px",n.style.bottom="0px",n.style.overflow="hidden";var s=a();s.className=l.D("_timeheader_float_inner");var r=t.cell.th;s.innerHTML=l.E(r.text,r.innerHTML),r.fontColor&&(s.style.color=r.fontColor),n.appendChild(s),n.F={marginLeft:e,marginRight:i},t.section=n,t.insertBefore(n,t.firstChild.nextSibling),t.firstChild.innerHTML="",this.elements.hsections.push(t)},this.p=function(){for(var t=0;t<this.elements.hsections.length;t++){var e=this.elements.hsections[t],i=e.cell;i&&e.firstChild&&(e.firstChild.innerHTML=l.E(i.th.text,i.th.innerHTML)),DayPilot.de(e.section),e.section=null}this.elements.hsections=[]},this.B=function(t,e,i){var n=t.section;if(n)return n.F&&n.F.marginLeft===e&&n.F.marginRight===i?void(n.F.stamp=l.A):(t.section.style.left=e+"px",t.section.style.right=i+"px",void(n.F={marginLeft:e,marginRight:i,stamp:l.A}));n=a(),n.className=l.D("_event_float"),n.style.position="absolute",n.style.left=e+"px",n.style.right=i+"px",n.style.top="0px",n.style.bottom="0px",n.style.overflow="hidden";var s=a();s.className=l.D("_event_float_inner"),s.innerHTML=t.event.client.html(),n.appendChild(s),n.F={marginLeft:e,marginRight:i,stamp:l.A},t.section=n,t.insertBefore(n,t.firstChild.nextSibling),t.firstChild.innerHTML="";var r=t.event,o=r.cache||r.data;o.fontColor&&(s.style.color=o.fontColor),this.elements.sections.push(t)},this.C=function(){for(var t=[],e=0;e<this.elements.sections.length;e++){var i=this.elements.sections[e];if(i.section&&i.section.F&&i.section.F.stamp===l.A)t.push(i);else{var n=i.event;n&&(i.firstChild.innerHTML=n.client.html()),DayPilot.de(i.section),i.section=null}}this.elements.sections=t},this.setScrollX=function(t){l.G.enabled?(l.G.scrollXRequested=t,setTimeout(function(){var t=l.G.scrollXRequested;"number"==typeof t&&l.l(t)},0)):l.l(t)},this.l=function(t){var e=l.nav.scroll,i=l.H();e.clientWidth+t>i&&(t=i-e.clientWidth),l.divTimeScroll.scrollLeft=t,e.scrollLeft=t},this.setScrollY=function(t){l.G.enabled?(l.G.scrollYRequested=t,setTimeout(function(){var t=l.G.scrollYRequested;"number"==typeof t&&l.I(t)},0)):l.I(t)},this.I=function(t){var e=l.nav.scroll,i=l.b;e.clientHeight+t>i&&(t=i-e.clientHeight),l.divResScroll.scrollTop=t,e.scrollTop=t},this.setScroll=function(t,e){l.setScrollX(t),l.setScrollY(e)},this.J=function(){if(this.nav.scroll){!function(){var t=l.H();l.k.style.height=l.b+"px",l.k.style.width=t+"px",t>l.nav.scroll.clientWidth?l.nav.scroll.style.overflowX="auto":l.nav.scroll.style.overflowX="hidden"}();var t=1;this.nav.scroll.style.height="30px";var e=this.K(),i=e+this.L()+t;if(e>=0&&(this.nav.scroll.style.height=e+"px",this.M.style.height=e+"px"),this.nav.divider&&((!i||isNaN(i)||i<0)&&(i=0),this.nav.divider.style.height=i+"px"),this.nav.top.style.height=i+"px",l.nav.resScrollSpace){var n=30;"Auto"===l.heightSpec&&(n=DayPilot.sh(l.nav.scroll)),l.nav.resScrollSpace.style.height=n+"px"}for(var s=0;s<this.elements.linesVertical.length;s++)this.elements.linesVertical[s].style.height=this.b+"px"}},this.N=function(){this.startDate=new DayPilot.Date(this.startDate).getDatePart(),this.timeHeader=[];var t=this.timeHeaders;t||(t=[{"groupBy":this.cellGroupBy},{"groupBy":"Cell"}]);for(var e=l.startDate.addDays(l.days),i=0;i<t.length;i++){var n=t[i].groupBy,s=t[i].format;"Default"===n&&(n=this.cellGroupBy);for(var r=[],a=l.startDate;a.ticks<e.ticks;){var o={};if(o.start=a,o.end=this.O(o.start,n),o.start.ticks===o.end.ticks)break;o.left=this.getPixels(o.start).left;var c=this.getPixels(o.end).left,d=c-o.left;o.width=d,"string"==typeof s?o.text=o.start.toString(s,v.P()):o.text=this.Q(o,n),d>0&&r.push(o),a=o.end}this.timeHeader.push(r)}},this.getPixels=function(t){var e=l.startDate.addDays(l.days);t=t.ticks>e.ticks?e.addTime(-1):t;var i=t.ticks-this.startDate.ticks,n=l.R(i),s=l.cellWidth,r=Math.floor(n/s),a=r*s;return{left:n,boxLeft:a,boxRight:a+s,i:r}},this.getDate=function(t,e,i){var n=this.S(t,i);if(!n)return null;var s=n.x,r=l.T(s);if(!r)return null;var a=i&&!e?r.end:r.start;return e?a.addTime(this.U(n.offset)):a},this.S=function(t,e){e&&(t-=1);var i=Math.floor(t/l.cellWidth),n=l.T(i);if(!n)return null;var s={};return s.x=i,s.offset=t%l.cellWidth,s.cell=n,s},this.V=function(t){var e=t.ticks-this.startDate.ticks,i=60*l.W()*1e3;if(e<0)return{past:!0};var n=Math.floor(e/i);return{i:n,current:l.T(n)}},this.R=function(t){var e=60*l.W()*1e3,i=l.cellWidth;return Math.floor(i*t/e)},this.U=function(t){var e=60*l.W()*1e3,i=l.cellWidth;return Math.floor(t/i*e)},this.X=function(t){DayPilot.Global.touch.start||i.Y||(l.Z={},l._(this,t))},this._=function(t,e){t.event&&l.aa(t,e)},this.ba=function(t){if(!DayPilot.Global.touch.active&&!DayPilot.Global.touch.start){var e=this.event;if(t.cancelBubble=!0,t.preventDefault(),!this.event.client.rightClickEnabled())return!1;l.ca(t);var i={};if(i.e=e,i.div=this,i.originalEvent=t,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventRightClick&&(l.onEventRightClick(i),i.preventDefault.value))return!1;switch(l.eventRightClickHandling){case"ContextMenu":var n=e.client.contextMenu();n?n.show(e):l.contextMenu&&l.contextMenu.show(this.event)}return"function"==typeof l.onEventRightClicked&&l.onEventRightClicked(i),!1}},this.T=function(t){var e=l.W(),i=60*e*1e3;return{start:l.startDate.addTime(t*i),end:l.startDate.addTime((t+1)*i),left:t*l.cellWidth,width:l.cellWidth}},this.aa=function(t,e){if("boolean"==typeof e)throw new DayPilot.Exception("Invalid _eventClickSingle parameters");var i=t.event;if(i){var n=e.ctrlKey,s=e.metaKey;if(i.client.clickEnabled()){l.ca(e);var r={};r.e=i,r.control=l,r.div=t,r.originalEvent=e,r.ctrl=n,r.meta=s,r.shift=e.shiftKey,r.preventDefault=function(){this.preventDefault.value=!0},r.toJSON=function(){return DayPilot.Util.copyProps(r,{},["e","ctrl","meta","shift"])},"function"==typeof l.onEventClick&&(l.onEventClick(r),r.preventDefault.value)||"function"==typeof l.onEventClicked&&l.onEventClicked(r)}}},this.da=function(t){var e={};if(e.e=t,e.preventDefault=function(){this.preventDefault.value=!0},e.control=l,e.toJSON=function(){return DayPilot.Util.copyProps(e,{},["e"])},"function"!=typeof l.onEventDelete||(l.onEventDelete(e),!e.preventDefault.value)){switch(l.eventDeleteHandling){case"Update":l.events.remove(t)}"function"==typeof l.onEventDeleted&&l.onEventDeleted(e)}},this.getScrollX=function(){return this.nav.scroll.scrollLeft},this.getScrollY=function(){return this.nav.scroll.scrollTop},this.ea=function(t,e,i,n){if("Disabled"!==this.eventResizeHandling){i=l.fa(i);var s={};s.e=t,s.async=!1,s.loaded=function(){r()},s.newStart=e,s.newEnd=i,s.what=n,s.preventDefault=function(){this.preventDefault.value=!0},s.control=l,s.toJSON=function(){return DayPilot.Util.copyProps(s,{},["e","async","newStart","newEnd"])};var r=function(){if(!s.preventDefault.value){switch(s.loaded={},e=s.newStart,i=s.newEnd,l.eventResizeHandling){case"Update":l.ga(s)}"function"==typeof l.onEventResized&&l.onEventResized(s)}};"function"==typeof l.onEventResize&&l.onEventResize(s),s.async||r()}},this.ha=function(t){t=t||{},clearTimeout(l.ia),l.timeHeader=null,l.cellProperties={},l.N(),l.ja(),l.events.ka(),l.ma.la(),l.clearSelection(),l.na(),l.oa(),l.pa(),l.qa(),l.ra(),l.sa(),l.ta(),l.va(),l.wa(),l.xa(),l.ya={},l.za(),l.J(),l.Aa(),t.immediateEvents?l.Ba():setTimeout(function(){l.Ba()},100),this.visible?l.c!==l.visible&&this.show():this.hide(),this.c=this.visible,this.r(),l.Ca(),this.Da()},this.Da=function(){if("function"==typeof l.onAfterUpdate){var t={};l.onAfterUpdate(t)}},this.update=function(t){if(!l.g)throw new DayPilot.Exception("You are trying to update a DayPilot.Scheduler object that hasn't been initialized.");if(l.a)throw new DayPilot.Exception("You are trying to update a DayPilot.Scheduler object that has been disposed already. Calling .dispose() destroys the object and makes it unusable.");d.request(t)};var d={};d.timeout=null,d.options=null,d.enabled=!1,d.request=function(t){d.enabled?(clearTimeout(d.timeout),d.Ea(t),d.timeout=setTimeout(d.doit)):(d.Ea(t),d.doit())},d.Ea=function(t){if(t){d.options||(d.options={});for(var e in t)d.options[e]=t[e]}},d.doit=function(){var t=d.options;return d.options=null,l.g?(l.Fa(t),l.ha({"immediateEvents":!0}),void l.Ga()):void l.Fa(t)},this.Ha=function(t){t.forEach(function(t){l.Ia(t.index)})},this.Ja=function(t){return t&&0!==t.length?t[0].isRow?t:t.map(function(t){return l.rowlist[t]}):[]},this.Ka=function(t,e,i){t=DayPilot.ua(t),t=l.Ja(t),l.Ha(t),this.La?(this.oa(),this.sa(),this.xa(),t.forEach(function(t){l.Ma(t.index)}),t.forEach(function(t){l.Na(t.index)}),this.Aa(),this.Oa()):(t.forEach(function(t){e||l.Ma(t.index),l.Na(t.index)}),t.forEach(function(t){l.Pa(t.index)}),l.Aa()),l.s(),i&&i(),this.za()},this.fa=function(t){return"DateTime"===l.eventEndSpec?t:t.getDatePart().ticks===t.ticks?t.addDays(-1):t.getDatePart()},this.Qa=function(t){return"DateTime"===l.eventEndSpec?t:t.getDatePart().addDays(1)},this.Ra=function(t){return"DateTime"===l.eventEndSpec?t:t.getDatePart()},this.Sa=function(t,e,i,n,s){if(l.Ta=null,"Disabled"!==l.eventMoveHandling){i=l.fa(i);var r={};r.e=t,r.newStart=e,r.newEnd=i,r.newResource=n,r.ctrl=!1,r.meta=!1,r.shift=!1,s&&(r.shift=s.shiftKey,r.ctrl=s.ctrlKey,r.meta=s.metaKey),r.control=l,r.areaData=DayPilot.Global.movingAreaData,r.toJSON=function(){return DayPilot.Util.copyProps(r,{},["e","newStart","newEnd","newResource","ctrl","meta","shift"])},r.preventDefault=function(){this.preventDefault.value=!0};var a=function(){if(r.loaded=function(){},r.preventDefault.value)return void l.Ua();switch(e=r.newStart,i=r.newEnd,l.eventMoveHandling){case"Update":l.ga(r)}l.Ua(),"function"==typeof l.onEventMoved&&l.onEventMoved(r)};r.async=!1,r.loaded=function(){a()},"function"==typeof l.onEventMove&&l.onEventMove(r),r.async||a()}},this.ga=function(t){var e=t.e,i=t.newStart,n=t.newEnd,s=t.newResource;e.start(i),e.end(n),e.resource(s),l.events.update(e),l.events.Va()},this.Wa=function(t){if(t)if(t.args)l.Xa(t.args.start,t.args.end,t.args.resource);else{var e=l.Ya(t);if(!e)return;l.Xa(e.start,e.end,e.resource)}},this.Xa=function(t,e,i){if("Disabled"!==l.timeRangeSelectedHandling){var n=e;e=l.fa(n);var s={};if(s.control=l,s.start=t,s.end=e,s.resource=i,s.preventDefault=function(){this.preventDefault.value=!0},s.toJSON=function(){return DayPilot.Util.copyProps(s,{},["start","end","resource"])},"function"==typeof l.onTimeRangeSelect){if(l.onTimeRangeSelect(s),s.preventDefault.value)return;t=s.start,e=s.end}e=l.Qa(e),l.Za(l.$a,t,e),l._a(l.$a),"function"==typeof l.onTimeRangeSelected&&l.onTimeRangeSelected(s)}},this.Za=function(t,e,i){if(t){var n,s=i;e.getTime()<l.startDate.getTime()?(t.start.x=0,t.start.time=l.startDate.getTime()):(n=l.V(e),t.start.x=n.i,t.start.time=e);var r=l.startDate.addDays(l.days);s.getTime()>r.getTime()?(t.end.x=l.ab(),t.end.time=r.getTime()):(n=l.V(s.addMilliseconds(-1)),t.end.x=n.i,t.end.time=i)}},this.bb=function(t,e){l.cb(t,e)},this.cb=function(t,e){var i={};i.resource=t,i.row=t,i.ctrl=e.ctrlKey,i.shift=e.shiftKey,i.meta=e.metaKey,i.originalEvent=e,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onRowClick&&(l.onRowClick(i),i.preventDefault.value)||"function"==typeof l.onRowClicked&&l.onRowClicked(i)},this.db=function(t){var e={};e.header=t,e.control=l,e.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onTimeHeaderClick&&(l.onTimeHeaderClick(e),e.preventDefault.value)||"function"==typeof l.onTimeHeaderClicked&&l.onTimeHeaderClicked(e)},this.getViewport=function(){var t=this.nav.scroll.scrollLeft,e=this.nav.scroll.scrollTop,i={},n=l.eb(t,e),s=l.fb(n),r=l.T(n.start.x),a=l.T(n.end.x);return i.start=l.getDate(t,!0),i.end=l.getDate(t+l.nav.scroll.clientWidth,!0,!0),i.resources=s,r&&(i.topLeft={"start":r.start,"end":r.end,x:n.start.x,y:n.start.y,"resource":s[0]}),a&&(i.bottomRight={"start":a.start,"end":a.end,x:n.end.x,y:n.end.y,"resource":s[s.length-1]}),i.rows=function(){return i.resources.map(function(t){return l.rows.find(t)})},i.events=function(){var t=[];return i.rows().forEach(function(e){t=t.concat(e.events.forRange(i.start,i.end))}),t},i},this.eb=function(t,e){var i={};i.start={},i.end={};var n=l.S(t),s=l.S(t+l.nav.scroll.clientWidth);n&&(i.start.x=n.x),s&&(i.end.x=s.x);var r=e,a=e+l.nav.scroll.clientHeight;i.start.y=l.gb(r).i,i.end.y=l.gb(a).i,i.start.x=DayPilot.Util.atLeast(i.start.x,0);var o=l.ab();return i.end.x>=o&&(i.end.x=o-1),i},this.fb=function(t){t||(t=this.eb(this.nav.scroll.scrollLeft,this.nav.scroll.scrollTop));var e=[];e.ignoreToJSON=!0;for(var i=t.start.y;i<=t.end.y;i++){var n=l.rowlist[i];n&&e.push(n.id)}return e},this.D=function(t){var e=this.theme||this.cssClassPrefix;return e?e+t:""},this.qa=function(){l.nav.top.className!==l.D("_main")&&(l.nav.top.className=l.D("_main"),l.nav.dh1.className=l.D("_divider_horizontal"),l.nav.dh2.className=l.D("_divider_horizontal"),l.divResScroll.className=l.D("_rowheader_scroll"),l.nav.divider.className=l.D("_divider")+" "+l.D("_splitter"),l.nav.scroll.className=l.D("_scrollable"),l.k.className=l.D("_matrix")+" "+l.D("_grid_main"))},this.hb=function(){this.nav.top.dispose=this.dispose},this.dispose=function(){var t=l;if(t.g&&!t.a){t.a=!0;for(var e in t.ib){var n=t.ib[e];DayPilot.isArray(n)?n.forEach(function(t){clearTimeout(t)}):clearTimeout(n)}t.wa(),t.divCells=null,t.divCorner=null,t.divEvents=null,t.divHeader&&(t.divHeader.rows=null),t.divHeader=null,t.divLines=null,t.divNorth=null,t.divRange=null,t.divResScroll=null,t.divStretch=null,t.divTimeScroll=null,t.M=null,t.k.calendar=null,t.k=null,t.nav.top.onmousemove=null,t.nav.top.onmouseout=null,t.nav.top.dispose=null,t.nav.top.ontouchstart=null,t.nav.top.ontouchmove=null,t.nav.top.ontouchend=null,t.nav.top.removeAttribute("style"),t.nav.top.removeAttribute("class"),t.nav.top.innerHTML="",t.nav.top.dp=null,t.nav.top=null,t.nav.scroll.onscroll=null,t.nav.scroll.root=null,t.nav.scroll=null,i.jb(t),h=null}},this.disposed=function(){return l.a},this.kb=function(t){var e=null;e=t.nodeType?t.event:t;var i=l.eventBorderRadius;"number"==typeof i&&(i+="px");var n=l.lb(e),s=l.rowlist,r=l.eventHeight,o=e.part&&e.part.top&&s[e.part.dayIndex]?e.part.top+s[e.part.dayIndex].top:n.top,c=n.left,d=n.width,h=document.createElement("div");h.style.position="absolute",h.style.width=d+"px",h.style.height=r+"px",h.style.left=c+"px",h.style.top=o+"px",h.style.overflow="hidden";var u=a();return h.appendChild(u),h.className=this.D("_shadow"),u.className=this.D("_shadow_inner"),i&&(h.style.borderRadius=i,u.style.borderRadius=i),l.divShadow.appendChild(h),h.calendar=l,h},this.gb=function(t){for(var e,i=l.rowlist,n={},s=0,r=0,a=i.length,o=0;o<a;o++){var c=i[o];if(r+=c.height,s=r-c.height,e=c,n.top=s,n.bottom=r,n.i=o,n.element=e,t<r)break}return n},this.mb=function(t){if(t>this.rowlist.length-1)throw new DayPilot.Exception("Row index too high");for(var e=0,i=0;i<=t;i++)e+=this.rowlist[i].height;var n=this.rowlist[t];return{top:e-n.height,height:n.height,bottom:e,i:t,data:n}},this.events.find=function(t){if(!l.events.list||"undefined"==typeof l.events.list.length)return null;if("function"==typeof t)return l.nb(t);for(var e=l.events.list.length,i=0;i<e;i++)if(l.events.list[i].id===t)return new DayPilot.Event(l.events.list[i],l);return null},this.events.findAll=function(t){if("function"==typeof t){for(var e=l.events.list.length,i=[],n=0;n<e;n++){var s=new DayPilot.Event(l.events.list[n],l);t(s)&&i.push(s)}return i}if("object"==typeof t)return l.events.findAll(function(e){for(var i in t)if(t[i]!==e.data[i])return!1;return!0});throw new DayPilot.Exception("function or object argument expected")},this.nb=function(t){for(var e=l.events.list.length,i=0;i<e;i++){var n=new DayPilot.Event(l.events.list[i],l);if(t(n))return n}return null},this.events.focus=function(t){var e=l.ob(t);e&&e.focus()},this.events.scrollIntoView=function(t){if(l.ob(t)){var e=t.start(),i=l.getViewport();!DayPilot.Util.overlaps(i.start,i.end,t.start(),t.end())&&DayPilot.Util.overlaps(l.pb(),l.qb(),t.start(),t.end())&&l.scrollTo(e,"fast","middle");var n=t.resource();l.getViewport().resources.indexOf(n)===-1&&l.scrollToResource(n)}},this.events.all=function(){for(var t=[],e=0;e<l.events.list.length;e++){var i=new DayPilot.Event(l.events.list[e],l);t.push(i)}return t},this.events.forRange=function(t,e){t=t?new DayPilot.Date(t):l.visibleStart(),e=e?new DayPilot.Date(e):l.visibleEnd();for(var i=[],n=0;n<l.events.list.length;n++){var s=new DayPilot.Event(l.events.list[n],l);DayPilot.Util.overlaps(s.start(),s.end(),t,e)&&i.push(s)}return i},this.events.load=function(t,e,i){if(!t)throw new DayPilot.Exception("events.load(): 'url' parameter required");var n=function(t){var e={};e.exception=t.exception,e.request=t.request,"function"==typeof i&&i(e)},s=function(t){var i,s=t.request;try{i=JSON.parse(s.responseText)}catch(t){var r={};return r.exception=t,void n(r)}if(DayPilot.isArray(i)){var a={};if(a.preventDefault=function(){this.preventDefault.value=!0},a.data=i,"function"==typeof e&&e(a),a.preventDefault.value)return;l.events.list=a.data,l.g&&l.update()}};if(l.eventsLoadMethod&&"POST"===l.eventsLoadMethod.toUpperCase())DayPilot.ajax({"method":"POST","contentType":"application/json","data":{"start":l.visibleStart().toString(),"end":l.visibleEnd().toString()},"url":t,"success":s,"error":n});else{var r=t,a="start="+l.visibleStart().toString()+"&end="+l.visibleEnd().toString();r+=r.indexOf("?")>-1?"&"+a:"?"+a,DayPilot.ajax({"method":"GET","url":r,"success":s,"error":n})}},this.events.rb=function(t){var e=[];return l.rowlist.forEach(function(i){l.sb(i.index);for(var n=0;n<i.events.length;n++){var s=i.events[n].data;if(l.tb(s,t)){e.push(i),i.events.splice(n,1);break}}}),e},this.events.ub=function(t){if(!t)return null;for(var e=l.rowlist,i=0;i<e.length;i++){var n=e[i];l.sb(n.index);for(var s=0;s<n.events.length;s++){var r=n.events[s];if(l.tb(r.data,t))return n.events[s]}}return null},this.events.vb=function(t){var e=[],i=l.wb(),n=DayPilot.indexOf(l.events.list,t);l.xb(n);var s=l.rowlist,r=!1;return s.forEach(function(s){if(!r){l.sb(s.index);var a=l.yb(t,s);a&&("function"==typeof l.onBeforeEventRender&&(a.cache=l.d.events[n]),e.push(s),i||(r=!0))}}),e},this.tb=function(t,e){return DayPilot.Util.isSameEvent(t,e)},this.events.update=function(t,e,i){if("object"==typeof t&&!(t instanceof DayPilot.Event)){var n=l.events.find(t.id);return l.events.remove(n),void l.events.add(t)}if(i=i||{},l.events.list.find(function(e){return l.tb(e,t.data)})){l.G.zb&&(l.G.skip=!0);var s=l.events.rb(t.data);t.commit(),s=s.concat(l.events.vb(t.data)),l.events.Ab(s)}},this.events.remove=function(t){if(t){if("string"==typeof t||"number"==typeof t){var e=l.events.find(t);return void l.events.remove(e)}var i=l.Bb(t.data);i&&l.events.list.splice(i.index,1),l.G.zb&&(l.G.skip=!0);var n=l.events.rb(t.data);l.events.Ab(n)}},this.events.add=function(t,e,i){var i=i||{},n=i.renderOnly;t instanceof DayPilot.Event||(t=new DayPilot.Event(t)),t.calendar=l,l.events.list||(l.events.list=[]);var s=l.Bb(t);if(n){if(!s)throw new DayPilot.Exception("Unexpected: event not found in list")}else{if(s)throw new DayPilot.Exception("The event you are trying to add using DayPilot.Scheduler.events.add() is already loaded. A unique ID is required.");s||l.events.list.push(t.data)}if(l.g){l.G.zb&&(l.G.skip=!0);var r=l.events.vb(t.data);l.events.Ab(r)}},this.events.Cb=function(t){var e=t instanceof DayPilot.Event?t.data:t,i=new DayPilot.Date(e.start),n=new DayPilot.Date(e.end);return!!l.events.list.find(function(t){if(l.tb(e,t))return!1;if(e.resource!==t.resource)return!1;var s=new DayPilot.Date(t.start),r=new DayPilot.Date(t.end);return DayPilot.Util.overlaps(i,n,s,r)})},this.events.Db={"rows":[]},this.events.Eb=null,this.events.ka=function(){clearTimeout(l.events.Eb),l.events.Eb=null,l.events.Db.rows=[]},this.events.Fb=0,this.events.Ab=function(t){var e=l.events.Db.rows;t.forEach(function(t){e.push(t)}),l.events.Db.rows=DayPilot.ua(e);var i=l.events.Va;l.events.Eb||(l.events.Eb=setTimeout(i,l.events.Fb))},this.events.Va=function(){clearTimeout(l.events.Eb),l.events.Eb=null;var t=l.events.Db.rows;l.events.Db.rows=[],l.Gb(t),l.Hb(),l.g&&(l.La&&l.oa(),l.J(),l.Ka(t))},this.G={},this.G.enabled=!1,this.G.skip=!1,this.G.skipUpdate=function(){return l.G.skip},this.G.skipped=function(){l.G.skip=!1},this.G.Ib=!1,this.G.zb=!1,this.Jb={},this.Jb.reactDOM=null,this.Jb.react=null,this.Kb=function(t){var e=l.startDate;if(t.ticks===e.ticks)return t;var i=e;if(t.ticks<e.ticks){for(;i.ticks>t.ticks;)i=i.addTime(60*-l.W()*1e3);return i}var n=this.V(t);if(n.current)return n.current.start;throw new DayPilot.Exception("getBoxStart(): time not found")},this.lb=function(t){var e=this.gb(l.coords.y);if("function"!=typeof t.end)throw new DayPilot.Exception("e.end function is not defined");if(!t.end())throw new DayPilot.Exception("e.end() returns null");var n=DayPilot.DateUtil.diff(t.rawend(),t.start());n=DayPilot.Util.atLeast(n,1);var s=v.Lb(n),r=0,a=l.coords.x;s&&(r=t.start().getTime()-this.Kb(t.start()).getTime());var o=0;if(i.Mb)if(s){var c=t.start(),d=this.Kb(c);o=i.Mb.getTime()-d.getTime();var h=60*l.W()*1e3;o=Math.floor(o/h)*h}else o=i.Mb.getTime()-t.start().getTime();var u=this.getDate(a,!0).addTime(-o);i.Nb&&(u=t.start());var f=l.snapToGrid;f&&(u=this.Kb(u)),u=u.addTime(r);var p=u.addTime(n),b=u,g=p,y=this.getPixels(b),m=this.getPixels(g.addTime(-1)),w=f?y.boxLeft:y.left,x=f&&s?m.boxRight-w:m.left-w,D={};return D.top=e.top,D.left=w,D.row=e.element,D.rowIndex=e.i,D.width=x,D.start=u,D.end=p,D.relativeY=l.coords.y-e.top,D},this.W=function(){switch(this.scale){case"CellDuration":return this.cellDuration;case"Minute":return 1;case"Hour":return 60;case"Day":return 1440;case"Week":return 10080}throw new DayPilot.Exception("can't guess cellDuration value")},this.Ob=function(t){return t.end.ticks-t.start.ticks},this.Pb=function(){var t=i.Qb.width,e=i.Qb.left,n=i.Rb,s=i.Nb.dpBorder,r=null,a=null,o=l.snapToGrid,c=!o;"left"===s?(r=l.getDate(e,c),a=n.rawend()):"right"===s&&(r=n.start(),a=l.getDate(e+t,c,!0)),i.Qb.start=r,i.Qb.end=a},this.Sb=function(){var t=l.coords,e=i.Nb.dpBorder,n=i.Nb.event,s=n.part.left;"right"===e&&(s+=n.part.width);var r,a,o=i.Nb.event.calendar.cellWidth,c=n.part.width,d=n.part.left,h=0,u=t.x-s,v=l.snapToGrid;if("right"===e){if(r=d,v){var f=l.S(c+d+u).cell,p=l.S(d).cell,b=p.left+p.width-d;a=f.left+f.width-d,a<b&&(a=b)}else a=c+u;var g=l.H();d+a>g&&(a=g-d),i.Qb.left=d,i.Qb.width=a,i.Qb.style.left=d+"px",i.Qb.style.width=a+"px"}else{if("left"!==e)throw new DayPilot.Exception("Invalid dpBorder.");v?(u>=c&&(u=c),r=Math.floor((d+u+0)/o)*o,r<h&&(r=h)):r=d+u,a=c-(r-d);var y=d+c,m=o;v?"Never"===l.useEventBoxes&&(m=c<o?c:1):m=1,a<m&&(a=m,r=y-a),i.Qb.left=r,i.Qb.width=a,i.Qb.style.left=r+"px",i.Qb.style.width=a+"px"}l.Pb()},this.Tb=function(){if(l.coords&&i.Ub){var t=i.Vb,e=this.lb(i.Ub);t.row=e.row,t.style.height=DayPilot.Util.atLeast(e.row.height,0)+"px",t.style.top=e.top+"px",t.style.left=e.left+"px",t.style.width=e.width+"px",t.start=e.start,t.end=e.end}},this.Wb=function(){return this.rowHeaderWidth},this.Xb=function(){return this.Yb(l.progressiveRowRenderingPreload)},this.Yb=function(t){t=t||0;var e=0,i=l.rowlist.length;if(l.progressiveRowRendering){var n=l.o();e=n.yStart,i=n.yEnd+1,e=DayPilot.Util.atLeast(0,e-t),i=Math.min(l.rowlist.length,i+t)}return{"start":e,"end":i}},this.pa=function(){function t(){var t=l.divHeader;t&&(t.rows=[]);var e=t;e&&(e.innerHTML="")}this.Zb=!0;var e=this.Wb();t();var i=this.divHeader;if(i.style.width=e+"px",i.style.height=l.b+"px",l.divHeader=i,l.progressiveRowRendering)n();else for(var s=this.rowlist.length,r=0;r<s;r++)l.$b(r);l._b(),this.divResScroll.appendChild(i)},this.ac=function(){if(l.progressiveRowRendering)for(var t=this.Xb(),e=0;e<l.rowlist.length;e++)t.start<=e&&e<t.end?l.$b(e):l.bc(e)},this._b=function(){if(!l.ma.cc()){var t=l.divHeader,e=a();e.style.position="absolute",t.appendChild(e),l.nav.resScrollSpace=e;var i=a();i.style.position="relative",i.style.height="100%",i.className=this.D("_rowheader"),e.appendChild(i);var n=this.Wb(),e=l.nav.resScrollSpace;e.style.width=n+"px",e.style.top=this.b+"px"}},this.bc=function(t){var e=l.divHeader.rows[t];e&&(DayPilot.de(e),l.divHeader.rows[t]=null)},this.Ia=function(t){this.bc(t),this.$b(t)},this.$b=function(t){var e=l.rowlist,i=l.divHeader;if(i&&!i.rows[t]){var n=this.Wb(),s=e[t];if(s){var r=this.dc(s),o=a();o.style.position="absolute",o.style.top=s.top+"px",i.rows[t]=o,o.row=s,o.index=t;var c=r.row,d=this.rowHeaderWidth;o.style.width=d+"px",o.style.border="0px none";var h=c.toolTip||c.toolTip;h&&(o.title=h),"undefined"!=typeof c.ariaLabel?o.setAttribute("aria-label",c.ariaLabel):o.setAttribute("aria-label",c.text||""),o.onclick=l.ec;var u=a();u.style.width=d+"px",u.className=this.D("_rowheader"),c.cssClass&&DayPilot.Util.addClass(u,c.cssClass),c.cssClass&&DayPilot.Util.addClass(u,c.cssClass);var v=c.backColor||c.backColor;v&&(u.style.background=v);var f=c.fontColor||c.fontColor;f&&(u.style.color=f);var p=c.horizontalAlignment||c.horizontalAlignment;p&&(u.style.textAlign=p),u.style.height=s.height+"px",u.style.overflow="hidden",u.style.position="relative";var b=a();switch(b.className=this.D("_rowheader_inner"),p){case"right":b.style.justifyContent="flex-end";break;case"left":b.style.justifyContent="flex-start";break;case"center":b.style.justifyContent="center"}u.appendChild(b);var g=a();g.style.position="absolute",g.style.bottom="0px",g.style.width="100%",g.style.height="0px",g.style.boxSizing="content-box",g.style.borderBottom="1px solid transparent",g.className=this.D("_resourcedivider"),u.appendChild(g);var y=a(),m=a();m.innerHTML=l.E(c.text,c.html),m.className=l.D("_rowheader_inner_text"),o.textDiv=m,o.cellDiv=u,y.appendChild(m),b.appendChild(y);var w=c.verticalAlignment||c.verticalAlignment;if(w)switch(b.style.display="flex",w){case"center":b.style.alignItems="center";break;case"top":b.style.alignItems="flex-start";break;case"bottom":b.style.alignItems="flex-end"}o.appendChild(u),i.appendChild(o),u.style.width=n+"px"}}},this.ec=function(t){var e=this.row,i=l.fc(e,this.index);l.bb(i,t)},this.gc=function(t){if("Disabled"!==l.timeHeaderClickHandling){var e={};e.start=this.cell.start,e.level=this.cell.level,e.end=this.cell.end,e.end||(e.end=new DayPilot.Date(e.start).addMinutes(l.W())),l.db(e)}
},this.hc=function(t){if("Disabled"!==l.timeHeaderRightClickHandling){t.cancelBubble=!0,t.preventDefault();var e={};e.start=this.cell.start,e.level=this.cell.level,e.end=this.cell.end,e.end||(e.end=new DayPilot.Date(e.start).addMinutes(l.W()));var i={};i.header=e,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onTimeHeaderRightClick&&(l.onTimeHeaderRightClick(i),i.preventDefault.value)||"function"==typeof l.onTimeHeaderRightClicked&&l.onTimeHeaderRightClicked(i)}},this.fc=function(t){return new DayPilot.Row(t,l)},this.sb=function(t){var e=l.rowlist,i=e[t];i.events||i.resetEvents()},this.ic={},this.na=function(t){if(t?this.events.list=t:this.events.list||(this.events.list=[]),null!=this.events.list&&!DayPilot.isArray(this.events.list))throw new DayPilot.Exception("DayPilot.Scheduler.events.list expects an array object");h.prepareRows(!0);var e,i=this.events.list,n="function"==typeof this.onBeforeEventRender;l.ic={};for(var s=0;s<i.length;s++){var r=i[s];if(r){if("object"!=typeof r)throw new DayPilot.Exception("Event data item must be an object");if(!r.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(r instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Scheduler: DayPilot.Event object detected in events.list array. Use raw event data instead.");if(!("string"==typeof r.id||"number"==typeof r.id))throw new DayPilot.Exception("All events must have an id property (string or number)");var a="_"+r.id;if(l.ic[a])throw new DayPilot.Exception("Duplicate event IDs are not allowed: "+a);l.ic[a]=!0,n&&this.xb(s),e=l.jc(r.resource);for(var o=0;e&&o<e.length;o++){var c=e[o],d=this.yb(r,c);d&&n&&(d.cache=this.d.events[s])}}}l.rowlist.forEach(function(t){l.kc(t)}),l.Hb()},this.lc={};var h=this.lc;h.rowcache={},h.prepareRows=function(t){h.rowcache={};for(var e=l.rowlist,i=0;i<e.length;i++){var n=e[i];if(t&&n.resetEvents(),l.sb(n.index),n.id){var s=typeof n.id+"_"+n.id;h.rowcache[s]||(h.rowcache[s]=[]),h.rowcache[s].push(n)}}},h.loadEvent=function(t){},this.jc=function(t){var e=typeof t+"_"+t;return h.rowcache[e]||[]},this.wb=function(){for(var t={},e=0;e<l.rowlist.length;e++){var i=l.rowlist[e],n=i.id;if(t[n])return!0;t[n]=!0}return!1},this.xb=function(t){var e=this.d.events,i=this.events.list[t],n={};i instanceof DayPilot.Event&&(i=i.data);for(var s in i)n[s]=i[s];if("string"==typeof n.start&&(n.start=new DayPilot.Date(n.start)),"string"==typeof n.end&&(n.end=new DayPilot.Date(n.end)),"function"==typeof this.onBeforeEventRender){var r={};r.e=n,r.data=n,this.onBeforeEventRender(r)}e[t]=n},this.kc=function(t){t.lines=[],t.sections=null,t.events.sort(this.nc);for(var e=0;e<t.events.length;e++){var i=t.events[e];t.putIntoLine(i)}},this.Gb=function(t){t=DayPilot.ua(t),t=l.Ja(t),t.forEach(function(t){l.kc(t)}),t.forEach(function(t){l.oc(t)})},this.yb=function(t,e){var i=new DayPilot.Date(t.start),n=new DayPilot.Date(t.end);n=l.Qa(n);var s=i.ticks,r=n.ticks,a=l.startDate.ticks,o=l.startDate.addDays(l.days).ticks;if(r<s)return null;var c=null;if("function"==typeof l.onBeforeEventRender){var d=DayPilot.indexOf(l.events.list,t);c=l.d.events[d]}if(e.id!==t.resource||(r<=a||s>=o)&&(s!==r||s!==a))return null;var h=new DayPilot.Event(t,l);h.part.dayIndex=l.rowlist.indexOf(e),h.part.start=a<s?i:l.startDate,h.part.end=o>r?n:l.startDate.addDays(l.days);var u=this.getPixels(h.part.start),f=this.getPixels(h.part.end.addTime(-1));h.part.start===h.part.end&&(f=this.getPixels(h.part.end.addMilliseconds(1)));var p=u.left,b=f.left;if(v.Lb(r-s)){var g=u.boxLeft,y=f.boxRight;h.part.left=g,h.part.width=y-g,h.part.barLeft=Math.max(p-h.part.left,0),h.part.barWidth=Math.max(b-p,1)}else h.part.left=p,h.part.width=Math.max(b-p,1),h.part.barLeft=0,h.part.barWidth=Math.max(b-p-1,1);var m=l.eventMinWidth;return h.part.width=Math.max(h.part.width,m),h.part.right=h.part.left+h.part.width,h.cache=c,e.events.push(h),h},this.nc=function(t,e){if(!(t&&e&&t.start&&e.start))return 0;var i=t.start().ticks-e.start().ticks;return 0!==i?i:e.end().ticks-t.end().ticks},this.rows={},this.rows.all=function(){for(var t=[],e=0;e<l.rowlist.length;e++){var i=l.fc(l.rowlist[e]);t.push(i)}return t},this.rows.each=function(t){l.rows.all().forEach(t)},this.rows.forEach=function(t){l.rows.all().forEach(t)},this.rows.find=function(t,e){if("string"==typeof t||"number"==typeof t||!t&&e){var i=l.jc(t);t||(i=l.rowlist);var n=null;return"string"==typeof e||e instanceof DayPilot.Date?(e=new DayPilot.Date(e),n=i.find(function(t){return e===t.start})):n=i[0],n?new DayPilot.Row(n,l):null}if("function"!=typeof t)throw new DayPilot.Exception("Invalid rows.find() argument: id or function expected");var s=e||0,r=l.rowlist.find(function(e,i){return!(i<s)&&t(l.fc(e))});if(r)return l.fc(r)},this.rows.load=function(t,e,i){if(!t)throw new DayPilot.Exception("rows.load(): 'url' parameter required");var n=function(t){var e={};e.exception=t.exception,e.request=t.request,"function"==typeof i&&i(e)},s=function(t){var i,s=t.request;try{i=JSON.parse(s.responseText)}catch(t){var r={};return r.exception=t,void n(r)}if(DayPilot.isArray(i)){var a={};if(a.preventDefault=function(){this.preventDefault.value=!0},a.data=i,"function"==typeof e&&e(a),a.preventDefault.value)return;l.resources=a.data,l.g&&l.update()}};l.rowsLoadMethod&&"POST"===l.rowsLoadMethod.toUpperCase()?DayPilot.ajax({"method":"POST","url":t,"success":s,"error":n}):DayPilot.ajax({"method":"GET","url":t,"success":s,"error":n})},this.rows.remove=function(t){if("number"==typeof t||"string"==typeof t){var e=l.rows.find(t);if(!e)throw new DayPilot.Exception("The row to be removed was not found");return void l.rows.remove(e)}var i=t.$.row.resource,n=DayPilot.indexOf(l.resources,i);l.resources.splice(n,1),l.update()},this.rows.add=function(t){l.resources.push(t),l.ha()},this.rows.update=function(t){if(!(t instanceof DayPilot.Row||"object"==typeof t))throw new DayPilot.Exception("DayPilot.Scheduler.rows.update() expects a DayPilot.Row object or a data object.");if(!(t instanceof DayPilot.Row)){var e=t;if(t=l.rows.find(e.id),!t)return;t.data=e}var i=t.index,n=l.rowlist,s=n[i],r=t.data,a=t.parent()?t.parent().$.row:null,o=l.pc(r),t=l.qc(o,a);t.level=s.level,t.index=i,t.top=s.top,t.height=s.height,n[i]=t,t.resetEvents(),l.sb(t.index),l.rc(t),l.kc(t),l.Ia(t.index),l.xa(),l.Aa()},this.rc=function(t){for(var e=l.events.list,i=e.length,n="function"==typeof l.onBeforeEventRender,s=0;s<i;s++){var r=e[s];if(r){if(r instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Scheduler: DayPilot.Event object detected in events.list array. Use raw event data instead.");if(r.resource===t.id){n&&this.xb(s);var a=this.yb(r,t);a&&n&&(a.cache=this.d.events[s])}}}},this.ja=function(){var t=this.resources;if({}.i=0,null!=t&&!DayPilot.isArray(t))throw new DayPilot.Exception("DayPilot.Scheduler.resources expects an array object");t=t||[],l.rowlist=[],this.sc(t)},this.pb=function(){return new DayPilot.Date(this.startDate)},this.qb=function(){return new DayPilot.Date(l.startDate).addDays(l.days)},this.visibleStart=function(){return this.pb()},this.visibleEnd=function(){return this.qb()},this.qc=function(t){var e={};return e.backColor=t.backColor,e.fontColor=t.fontColor,e.cssClass=t.cssClass,e.name=t.name,e.html=l.E(t.name,t.html),e.id=t.id,e.toolTip=t.toolTip,e.tags=t.tags,e.height=l.eventHeight,e.level=0,e.resource=t.F,e.lines=[],e.isRow=!0,e.getHeight=function(){return Math.max(l.eventHeight,this.lines.length*l.eventHeight)},e.resetEvents=function(){var t=this;t.events=[],t.events.forRange=function(e,i){e=new DayPilot.Date(e),i=i?new DayPilot.Date(i):l.startDate.addDays(l.days);for(var n=[],s=0;s<t.events.length;s++){var r=t.events[s],a=l.Qa(r.end());DayPilot.Util.overlaps(r.start(),a,e,i)&&n.push(r)}return n}},e.tc=function(){var t=[];return t.add=function(t){this.push(t)},t.isFree=function(t,e,i){for(var n=t+e-1,s=this.length,r=0;r<s;r++){var a=this[r];if(!(n<a.part.left||t>a.part.left+a.part.width-1)){if(DayPilot.contains(i,a.data))continue;return!1}}return!0},t},e.findFreeLine=function(t,i){for(var n=0;n<this.lines.length;n++){var s=this.lines[n];if(s.isFree(t,i))return n}var s=e.tc();return this.lines.push(s),this.lines.length-1},e.putIntoLine=function(t){var i=e.findFreeLine(t.part.left,t.part.width);return this.lines[i].add(t),i},e},this.sc=function(t){if(t)for(var e=l.rowlist,i=0;i<t.length;i++)if(t[i]){var n={};n.index=i;var s=this.pc(t[i],n),r=l.qc(s,parent);r.index=i,e.push(r)}},this.dc=function(t){var e={};return e.row=this.fc(t),DayPilot.Util.copyProps(t,e.row,["html","backColor","fontColor","cssClass","toolTip"]),"function"==typeof this.onBeforeRowHeaderRender&&this.onBeforeRowHeaderRender(e),e},this.pc=function(t,e){var i={get $data(){return this.F}};for(var n in e)i[n]=e[n];for(var n in t)i[n]=t[n];return i.html=l.E(t.name,t.html),i.F=t,i},this.uc=function(){this.nav.top.dp=this,this.nav.top.innerHTML="",DayPilot.Util.addClass(this.nav.top,this.D("_main")),this.nav.top.setAttribute("role","region"),this.nav.top.setAttribute("aria-label","scheduler"),this.nav.top.style.userSelect="none",this.nav.top.style.webkitUserSelect="none",this.nav.top.style.WebkitTapHighlightColor="rgba(0,0,0,0)",this.nav.top.style.WebkitTouchCallout="none",this.width&&(this.nav.top.style.width=this.width),this.nav.top.style.lineHeight="1.2",this.nav.top.style.position="relative",this.visible||(this.nav.top.style.display="none"),this.nav.top.ontouchstart=u.vc,this.nav.top.ontouchmove=u.wc,this.nav.top.ontouchend=u.xc;var t=this.rowHeaderWidth,e=a();e.style.position="absolute",e.style.left="0px",e.style.width=t+"px";var i=a();i.style.height="0px",i.style.boxSizing="content-box",i.style.borderTop="1px solid transparent",i.className=this.D("_divider_horizontal"),this.nav.dh1=i,this.nav.left=e,e.appendChild(this.yc()),e.appendChild(i),e.appendChild(this.zc());var n=a();n.style.position="absolute",n.style.left=t+"px",n.style.width="1px",n.style.height=this.L()+this.K()+"px",n.className=this.D("_splitter"),this.nav.divider=n;var s=a();s.style.marginLeft=t+1+"px",s.style.position="relative",this.nav.right=s;var r=a();r.style.position="absolute",r.style.top=this.L()+"px",r.style.width="100%",r.style.height="1px",r.style.boxSizing="border-box",r.style.borderBottom="1px solid transparent",r.setAttribute("data-dh2","true"),r.className=this.D("_divider_horizontal"),this.nav.dh2=r,s.appendChild(l.Ac()),s.appendChild(l.Bc()),s.appendChild(r);var o=a();o.style.clear="left";var c=a();c.style.height="1px",c.style.position="absolute",c.style.left="0px",c.style.right="0px",c.style.display="none",c.className=this.D("_divider_horizontal"),this.nav.dividerTop=c;var d=a();d.style.height="1px",d.style.position="absolute",d.style.left="0px",d.style.right="0px",d.style.display="none",d.className=this.D("_divider_horizontal")+" "+this.D("_divider_horizontal_frozen_bottom"),this.nav.dividerBottom=d,this.nav.top.appendChild(e),this.nav.top.appendChild(n),this.nav.top.appendChild(s),this.nav.top.appendChild(o),this.nav.top.appendChild(c),this.nav.top.appendChild(d)},this.va=function(){var t=this.L();this.nav.corner&&(this.nav.corner.style.height=t+"px"),this.divTimeScroll&&(this.divTimeScroll.style.height=t+"px"),this.divNorth&&(this.divNorth.style.height=t+"px"),this.nav.dh1&&this.nav.dh2&&(this.nav.dh1.style.top=t+"px",this.nav.dh2.style.top=t+"px"),this.nav.scroll.style.top=t+1+"px"},this.Cc=function(){var t=this.rowHeaderWidth;this.nav.corner&&(this.nav.corner.style.width=t+"px"),this.divResScroll.style.width=t+"px",this.nav.left.style.width=t+"px",this.nav.divider.style.left=t-1+"px",this.nav.right.style.marginLeft=t+"px"},this.Dc=function(){var t=this.rowHeaderWidth,e=this.divHeader;e.style.width=t+"px";for(var i=l.Xb(),n=i.start;n<i.end;n++){var s=e.rows[n];if(s){var r=l.rowHeaderWidth;s.style.width=r+"px";s.firstChild.style.width=r+"px"}}l.nav.resScrollSpace&&(l.nav.resScrollSpace.style.width=t+"px")},this.ta=function(){this.Cc(),this.Dc()},this.yc=function(){var t=this.rowHeaderWidth,e=a();l.nav.corner=e,e.style.width=t+"px",e.style.height=this.L()+"px",e.style.overflow="hidden",e.style.position="relative",e.oncontextmenu=function(){return!1},e.className=this.D("_corner");var i=a();return i.style.position="absolute",i.style.top="0px",i.style.left="0px",i.style.right="0px",i.style.bottom="0px",i.className=this.D("_corner_inner"),i.innerHTML="&nbsp;",this.divCorner=i,e.appendChild(i),e},this.L=function(){return l.timeHeaders?l.timeHeaders.length*l.headerHeight:0},this.Ec=null,this.zc=function(){var t=a();t.style.width=this.rowHeaderWidth+"px",t.style.height=this.K()+"px",t.style.overflow="hidden",t.style.position="relative",t.className=l.D("_rowheader_scroll");var n=l.ma.cc();n&&(t.style.overflowY="auto"),t.ontouchstart=function(){i.Fc=!0},t.oncontextmenu=function(){return!1},t.onscroll=function(){if(l.Ec&&clearTimeout(l.Ec),n){var s=function(){var e=l.Gc()-l.nav.scroll.offsetHeight;t.scrollTop=Math.min(t.scrollTop,e),l.nav.scroll.scrollTop=t.scrollTop};e?i.Fc&&(l.Ec=setTimeout(s,10)):l.Ec=setTimeout(s,10)}else l.Ec=setTimeout(function(){l.nav.scroll.scrollTop=t.scrollTop},500)},t.setAttribute("role","region"),t.setAttribute("aria-label","scheduler rows");var s=a();return this.divHeader=s,t.appendChild(s),this.divResScroll=t,this.M=t,t},this.Hc=null,this.Ic=null,this.Jc=function(){var t=function(){l.Kc(),l.Ca()},e=function(){var e=l.nav.top;if(e){if(!l.Hc)return l.Hc={},void(l.Hc.width=e.offsetWidth);l.Hc.width!==e.offsetWidth&&(l.Hc.width=e.offsetWidth,t())}};if(!l.Ic){var i=new ResizeObserver(DayPilot.debounce(e,100));i.observe(l.nav.top),l.Ic=i}},this.Kc=function(){l.a||(l.J(),l.Lc(),l.d.drawArea=null)},this.Lc=function(){var t=l.$a;l.clearSelection(),l.$a=t,l._a(t,{"justDraw":!0})},this.Ac=function(){var t=a();t.style.overflow="hidden",t.style.display="block",t.style.position="absolute",t.style.top="0px",t.style.width="100%",t.style.height=this.L()+"px",t.style.overflow="hidden",t.className=l.D("_timeheader_scroll"),this.divTimeScroll=t;var e=a();return e.style.width=this.H()+5e3+"px",this.divNorth=e,t.appendChild(e),t},this.K=function(){var t=0,e=l.heightSpec;return"Fixed"===e?this.height?this.height:0:(t=l.Gc(),"Max"===e&&t>l.height?l.height:t)},this.Gc=function(){var t;return this.b!==-1?(t=this.b,this.b>0&&"auto"===l.nav.scroll.style.overflowX&&(t+=DayPilot.sh(l.nav.scroll)+1)):t=this.rowlist.length*l.eventHeight,t},this.Bc=function(){var t=a();t.style.overflow="auto",t.style.overflowX="auto",t.style.overflowY="auto",t.style.height=this.K()+"px",t.style.top=this.L()+1+"px",t.style.position="absolute",t.style.width="100%",t.className=this.D("_scrollable"),t.oncontextmenu=function(){return!1},this.nav.scroll=t,this.k=a();var e=this.k;e.style.userSelect="none",e.style.webkitUserSelect="none",e.calendar=this,e.style.position="absolute";var i=this.H();return i>0&&(e.style.width=i+"px"),e.onmousedown=this.Mc,e.onmousemove=this.Nc,e.onmouseup=this.Oc,e.oncontextmenu=this.Pc,e.className=this.D("_matrix"),this.divStretch=a(),this.divStretch.style.position="absolute",this.divStretch.style.height="1px",e.appendChild(this.divStretch),this.divCells=a(),this.divCells.style.position="absolute",this.divCells.oncontextmenu=this.Pc,e.appendChild(this.divCells),this.divLines=a(),this.divLines.style.position="absolute",this.divLines.oncontextmenu=this.Pc,e.appendChild(this.divLines),this.divSeparators=a(),this.divSeparators.style.position="absolute",this.divSeparators.oncontextmenu=this.Pc,e.appendChild(this.divSeparators),this.divRange=a(),this.divRange.style.position="absolute",this.divRange.oncontextmenu=this.Pc,e.appendChild(this.divRange),this.divEvents=a(),this.divEvents.style.position="absolute",e.appendChild(this.divEvents),this.divShadow=a(),this.divShadow.style.position="absolute",e.appendChild(this.divShadow),t.appendChild(e),t},this.Qc=function(){i.Rc||(i.Rc=!0,DayPilot.re(document,"mouseup",i.Sc),DayPilot.re(document,"touchmove",i.Tc),DayPilot.re(document,"touchend",i.Uc))},this.Vc=function(){this.nav.scroll.root=this,this.nav.scroll.onscroll=this.Ca,l.Wc=this.nav.scroll.scrollLeft,l.Xc=this.nav.scroll.scrollTop,this.divNorth&&(l.Yc=this.divNorth.clientWidth)},this.Zc={},this.Zc.step=300,this.Zc.delay=10,this.Zc.mode="display",this.Zc.layers=!1,this.oc=function(t){for(var e=0,i=0;i<t.lines.length;i++){for(var n=t.lines[i],s=0;s<n.length;s++){var r=n[s];r.part.line=i,r.part.top=e+l.rowMarginTop,r.part.right=r.part.left+r.part.width}e+=l.eventHeight}},this.ia=null,this.Ba=function(t){if(!l.a){var e=this.Zc.step;"display"===this.Zc.mode?this.divEvents.style.display="none":"visibility"===this.Zc.mode&&(this.divEvents.style.visibility="hidden"),this.divEvents.setAttribute("role","region"),this.divEvents.setAttribute("aria-label","scheduler events");var i="Progressive"===this.dynamicEventRendering,n=this.o(),s=n.pixels.top,r=n.pixels.bottom;l.rowlist.filter(function(t){var e=t.top-l.dynamicEventRenderingMargin,n=e+t.height+2*l.dynamicEventRenderingMargin;return!i||!(r<=e||s>=n)}).forEach(function(i){l.oc(i);for(var n=0;n<i.lines.length;n++)for(var s=i.lines[n],r=0;r<s.length;r++){var a=s[r],o=l.$c(a);if(t&&o&&(e--,e<=0))return l.divEvents.style.visibility="",l.divEvents.style.display="",void(l.ia=setTimeout(function(){l.Ba(t)},l.Zc.delay))}}),this.divEvents.style.display="",this.s()}},this.Na=function(t){var e=l.rowlist[t];this.oc(e);for(var i=0;i<e.lines.length;i++)for(var n=e.lines[i],s=0;s<n.length;s++){var r=n[s];this.$c(r)}},this.wa=function(){if(this.elements.events)for(var t=this.elements.events.length,e=0;e<t;e++){var i=this.elements.events[e];this._c(i)}this.elements.events=[]},this.Ma=function(t){if(this.elements.events){for(var e=this.elements.events.length,i=[],n=0;n<e;n++){var s=this.elements.events[n];s.event.part.dayIndex===t&&(this._c(s),i.push(n))}for(var n=i.length-1;n>=0;n--)this.elements.events.splice(i[n],1)}},this._c=function(t){t.remove(),t.onclick=null,t.oncontextmenu=null,t.onmouseover=null,t.onmouseout=null,t.onmousemove=null,t.onmousedown=null,t.ondblclick=null,t.event&&(t.event.rendered=null,t.event=null)},this.ad=function(){if("Progressive"===this.dynamicEventRendering){if(l.dynamicEventRenderingCacheSweeping){var t=l.dynamicEventRenderingCacheSize||0;this.divEvents.style.display="none";for(var e=[],i=0,n=this.elements.events.length,s=n-1;s>=0;s--){var r=this.elements.events[s];this.bd(r.event)?t>0?(t--,e.unshift(r)):(this._c(r),i++):e.unshift(r)}this.elements.events=e,this.divEvents.style.display=""}}},this.cd=function(t){for(var e=this.o(),i=this.elements.cells.length,n=i-1;n>=0;n--){var s=this.elements.cells[n];e.xStart<s.coords.x&&s.coords.x<=e.xEnd&&e.yStart<s.coords.y&&s.coords.y<=e.yEnd||(t>0?t--:this.dd(s))}},this.dd=function(t){if(t&&t.coords){var e=t.coords.x,i=t.coords.y;DayPilot.rfa(l.elements.cells,t),DayPilot.de(t),l.d.cells[e+"_"+i]=null}},this.ed=function(){var t="Progressive"===this.dynamicEventRendering;if(!this.nav.scroll)return!1;for(var e=this.nav.scroll.scrollTop,i=e+this.nav.scroll.clientHeight,n=0;n<this.rowlist.length;n++){var s=this.rowlist[n],r=s.top,a=s.top+s.height;if(!t||!(i<=r||e>=a))for(var o=0;o<s.lines.length;o++)for(var l=s.lines[o],c=0;c<l.length;c++){var d=l[c];if(this.fd(d))return!0}}return!1},this.fd=function(t){if(t.rendered)return!1;var e="Progressive"===this.dynamicEventRendering,i=this.nav.scroll.scrollLeft,n=i+this.nav.scroll.clientWidth,s=t.part.left,r=t.part.left+t.part.width;return!e||!(n<=s||i>=r)},this.bd=function(t){if(!t.rendered)return!0;var e=this.o(),i=e.pixels.top,n=e.pixels.bottom,s=e.pixels.left-this.dynamicEventRenderingMargin,r=e.pixels.right+this.dynamicEventRenderingMargin,a=t.part.left,o=t.part.right,c=t.part.top,d=t.part.top+l.eventHeight;return r<=a||s>=o||(n<=c||i>=d)},this.$c=function(t,e){e=e||{};var i=e.forced;if(t.rendered)return!1;var n="Progressive"===this.dynamicEventRendering,s=t.part.dayIndex,r=l.divEvents,o=l.rowlist,c=o[s],d=c.top,h=this.o(),v=h.pixels.left-this.dynamicEventRenderingMargin,f=h.pixels.right+this.dynamicEventRenderingMargin,p=h.pixels.top,b=h.pixels.bottom,g=t.part.left,y=t.part.left+t.part.width,m=t.part.top+d,w=m+l.eventHeight,x=f<=g||v>=y,D=b<=m||p>=w;if(!i&&n&&(x||D))return!1;var C=t.part.width,T=l.eventHeight,E=t.cache||t.data,R=E.borderRadius||l.eventBorderRadius;"number"==typeof R&&(R+="px"),C=DayPilot.Util.atLeast(0,C),T=DayPilot.Util.atLeast(0,T);var k=a();k.style.position="absolute",k.style.left=t.part.left+"px",k.style.top=d+t.part.top+"px",k.style.width=C+"px",k.style.height=T+"px",l.eventTextWrappingEnabled||(k.style.whiteSpace="nowrap"),k.style.overflow="hidden",k.className=this.D("_event"),E.cssClass&&DayPilot.Util.addClass(k,E.cssClass);"number"==typeof t.part.line&&DayPilot.Util.addClass(k,this.D("_event_line"+t.part.line)),R&&(k.style.borderRadius=R),this.showToolTip&&(k.title=t.client.toolTip()||""),k.onmousemove=this.gd,k.onmousedown=this.hd,k.onmouseup=this.jd,k.ontouchstart=u.kd,k.ontouchmove=u.ld,k.ontouchend=u.md,t.client.clickEnabled()&&(k.onclick=this.X),"undefined"!=typeof E.ariaLabel?k.setAttribute("aria-label",E.ariaLabel):k.setAttribute("aria-label",E.text),k.setAttribute("tabindex","-1");var S=a();S.className=l.D("_event_inner"),R&&(S.style.borderRadius=R),E.backColor&&(S.style.background=E.backColor),E.fontColor&&(S.style.color=E.fontColor),"darker"===E.borderColor&&E.backColor?S.style.borderColor=DayPilot.ColorUtil.darker(E.backColor,2):S.style.borderColor=E.borderColor,E.backImage&&(S.style.backgroundImage="url("+E.backImage+")",E.backRepeat&&(S.style.backgroundRepeat=E.backRepeat)),k.appendChild(S);var H=t.start().getTime()===t.part.start.getTime(),M=t.rawend().getTime()===t.part.end.getTime();if(H||DayPilot.Util.addClass(k,this.D("_event_continueleft")),M||DayPilot.Util.addClass(k,this.D("_event_continueright")),t.client.barVisible()&&C>0){var _=100*t.part.barLeft/C,U=Math.ceil(100*t.part.barWidth/C),P=a();P.className=this.D("_event_bar"),P.style.position="absolute",E.barBackColor&&(P.style.backgroundColor=E.barBackColor);var N=a();N.className=this.D("_event_bar_inner"),N.style.left=_+"%",0<U&&U<=1?N.style.width="1px":N.style.width=U+"%",E.barColor&&(N.style.backgroundColor=E.barColor),E.barImageUrl&&(N.style.backgroundImage="url("+E.barImageUrl+")"),P.appendChild(N),k.appendChild(P)}k.row=s,k.event=t,S.innerHTML=t.client.innerHTML();var G=[];return function(){if("Disabled"!==l.eventDeleteHandling&&!E.deleteDisabled){var t=l.durationBarVisible?l.durationBarHeight:0;G.push({v:"Hover",w:17,h:17,top:t+2,right:2,css:l.D("_event_delete"),onClick:function(t){l.da(t.source)}})}}(),E.areas&&(G=G.concat(E.areas)),DayPilot.Areas.attach(k,t,{areas:G}),this.elements.events.push(k),r.appendChild(k),t.rendered=!0,!0},this.E=function(t,e){return l.ma.nd()?DayPilot.Util.escapeTextHtml(t,e):DayPilot.Util.isNullOrUndefined(e)?DayPilot.Util.isNullOrUndefined(t)?"":t:e},this.Oa=function(){for(var t=0;t<this.elements.events.length;t++){var e=this.elements.events[t],i=e.event,n=i.part.dayIndex,s=l.rowlist[n],r=s.top,a=r+i.part.top,o=s.height,c=l.eventHeight,d=!1;i.part.top+c>o&&(c=Math.max(0,o-i.part.top),d=!0),e.style.top=a+"px",e.style.height=c+"px"}},this.od=function(t){if(!t)return null;for(var e=0;e<l.elements.events.length;e++){var i=l.elements.events[e];if(i.event===t||i.event.data===t.data)return i}return null},this.ob=function(t){var e=l.events.ub(t.data);if(!e)return null;var i=l.rowlist[e.part.dayIndex];return l.oc(i),l.$c(e,{"forced":!0}),l.od(e)},this.gd=function(t){for(var e=this;e&&!e.event;)e=e.parentNode;e.event;l.pd(e,t),l.ca(t)},this.Z={},this.hd=function(t){l.ca(t);var e=DayPilot.Util.mouseButton(t);t.preventDefault(),t.stopPropagation(),e.left&&("w-resize"===this.style.cursor||"e-resize"===this.style.cursor?(i.Y=!0,i.Nb=this,i.Rb=this.event,i.qd=DayPilot.mc(t),r().style.cursor=this.style.cursor):("move"===this.style.cursor||this.event.client.moveEnabled())&&l.rd(this,t))},this.jd=function(t){DayPilot.Util.mouseButton(t).right&&l.ba.call(this,t)},this.rd=function(t,e){var i=l.Z;l.ca(e),i.sd=!0,i.Z=t,i.Ub=t.event,i.qd=DayPilot.mc(e),i.td=DayPilot.mo3(t,e).x,i.Mb=l.getDate(l.coords.x,!0)},this.ud={};var u=l.ud;"undefined"==typeof DayPilot.Global.touch&&(DayPilot.Global.touch={}),DayPilot.Global.touch.active=!1,DayPilot.Global.touch.start=!1,u.ib=[],u.kd=function(e){if(!DayPilot.Global.touch.active&&!DayPilot.Global.touch.start){e.stopPropagation(),u.vd(),DayPilot.Global.touch.start=!0,DayPilot.Global.touch.active=!1;var i=this;l.coords=u.wd(e);var n=l.tapAndHoldTimeout;u.ib.push(setTimeout(function(){DayPilot.Global.touch.active=!0,DayPilot.Global.touch.start=!1,e.preventDefault();var n=i.event;switch(l.eventTapAndHoldHandling){case"Move":if(n.client.moveEnabled()){var s=t(e);u.rd(i,s)}}},n))}},u.ld=function(t){u.vd(),DayPilot.Global.touch.start=!1},u.md=function(t){if(!DayPilot.Util.isMouseEvent(t)){if(u.vd(),DayPilot.Global.touch.start){DayPilot.Global.touch.start=!1,t.preventDefault(),t.stopPropagation();var e=this;setTimeout(function(){l.aa(e,t)})}setTimeout(function(){DayPilot.Global.touch.start=!1,DayPilot.Global.touch.active=!1},500)}},u.vc=function(t){if(!(DayPilot.Global.touch.active||DayPilot.Global.touch.start||t.touches.length>1||"Disabled"===l.timeRangeSelectedHandling)){u.vd(),DayPilot.Global.touch.start=!0,DayPilot.Global.touch.active=!1;var e=l.tapAndHoldTimeout;u.ib.push(setTimeout(function(){DayPilot.Global.touch.active=!0,DayPilot.Global.touch.start=!1,t.preventDefault(),l.coords=u.wd(t),u.xd=l.yd()},e));l.coords=u.wd(t)}},u.wc=function(t){if(u.vd(),DayPilot.Global.touch.start=!1,i.Nb)return t.preventDefault(),void u.zd();if(DayPilot.Global.touch.active){if(t.preventDefault(),l.coords=u.wd(t),i.Z)return void u.Ad();if(u.xd){var e=u.xd;e.end={x:Math.floor(l.coords.x/l.cellWidth),"time":l.getDate(l.coords.x,!0)},l._a(e)}}},u.debug=function(t){},u.xc=function(t){u.vd();if(DayPilot.Global.touch.active){if(i.Z){t.preventDefault();var e=i.Ub;if(l!==i.Vb.calendar)return;var n=i.Vb.start,s=i.Vb.end,a=i.Vb.row.id;DayPilot.Util.removeClass(i.Z,l.D("_event_moving_source")),DayPilot.de(i.Vb),i.Vb.calendar=null,r().style.cursor="",i.Z=null,i.Ub=null,i.Vb=null,l.Sa(e,n,s,a)}if(u.xd){var o=u.xd;u.xd=null;var c=l.elements.range2;c&&c.overlapping?l.clearSelection():l.Wa(o)}}else if(DayPilot.Global.touch.start){if(l.coords.x<l.getScrollX())return;var o=l.yd();l._a(o);var c=l.elements.range2;c&&c.overlapping?l.clearSelection():l.Wa(o)}setTimeout(function(){DayPilot.Global.touch.start=!1,DayPilot.Global.touch.active=!1},500)},u.vd=function(){for(var t=0;t<u.ib.length;t++)clearTimeout(u.ib[t]);u.ib=[]},u.wd=function(t){function e(t,e,i){var n=DayPilot.abs(i);return{x:t-n.x,y:e-n.y,toString:function(){return"x: "+this.x+", y:"+this.y}}}var i=l.k,n=t.touches?t.touches[0]:t;return e(n.pageX,n.pageY,i)},u.rd=function(t,e){i.Z=t,i.Ub=t.event,i.qd=e;var n=DayPilot.abs(t);i.td=e.x-n.x,i.Mb=l.getDate(l.coords.x,!0),i.Vb=l.kb(t),l.Tb()},u.zd=function(){if(!i.Qb){var t=i.Nb;i.Qb=l.kb(t)}l.Sb()},u.Ad=function(){if(!i.Vb){var t=i.Z;i.Vb=l.kb(t)}i.Vb.calendar.Tb()},this.pd=function(t,e){var n=this.eventResizeMargin,s=t;if("undefined"!=typeof i){var r=DayPilot.mo3(t,e);if(r&&(l.eventOffset=r,!i.Nb&&!i.Z)){var a=s.event.part.start.toString()===s.event.start().toString(),o=s.event.part.end.toString()===s.event.rawend().toString();r.x<=n&&s.event.client.resizeEnabled()?a?(t.style.cursor="w-resize",t.dpBorder="left"):t.style.cursor="not-allowed":t.offsetWidth-r.x<=n&&s.event.client.resizeEnabled()?o?(t.style.cursor="e-resize",t.dpBorder="right"):t.style.cursor="not-allowed":i.Nb||i.Z||(s.event.client.clickEnabled()?t.style.cursor="pointer":t.style.cursor="default")}}},this.ab=function(){var t=l.W();return l.days*(1440/t)},this.Ya=function(t){var t=t||i.xd||l.$a;if(!t)return null;var e=l.rowlist[t.start.y];if(!e)return null;var n,s,r=t,a=r.end.time>r.start.time,o=e.id,c=a?r.start.x:r.end.x,d=a?r.end.x:r.start.x,h=l.snapToGrid;h?(n=l.T(c).start,s=l.T(d).end):a?(n=r.start.time,s=r.end.time):(n=r.end.time,s=r.start.time);var u=new DayPilot.Selection(n,s,o,l);return u.allowed=!t.div||!t.div.overlapping,u},this.ra=function(){this.d.timeHeader={},l.elements.timeHeader.length>0&&(l.elements.timeHeader=[]);var t=a();t.style.position="relative",this.nav.timeHeader=t;for(var e=0;e<this.timeHeader.length;e++)for(var i=this.timeHeader[e],n=0;n<i.length;n++)this.Bd(n,e);var s=this.divNorth;1===s.childNodes.length?s.replaceChild(t,s.childNodes[0]):(s.innerHTML="",s.appendChild(t));var r=this.H();s.style.width=r+5e3+"px",r>0&&(this.divStretch.style.width=r+"px")},this.Q=function(t,e){var i=null,n=this.ma.P(),e=e||this.cellGroupBy,s=t.start;switch(e){case"Minute":i=s.toString("m");break;case"Hour":i="Clock12Hours"===l.ma.Cd()?s.toString("h tt",n):s.toString("H",n);break;case"Day":i=s.toString(n.datePattern);break;case"Week":i=1===v.Dd()?s.weekNumberISO():s.weekNumber();break;case"Month":i=s.toString("MMMM yyyy",n);break;case"Quarter":i="Q"+Math.floor(s.getMonth()/3+1);break;case"Year":i=s.toString("yyyy");break;case"None":i="";break;case"Cell":var r=(t.end.ticks-t.start.ticks)/6e4;i=this.Ed(s,r);break;default:throw new DayPilot.Exception("Invalid groupBy value: "+e)}return i},this.Ed=function(t,e){var i=this.ma.P(),e=e||this.cellDuration;return e<1?t.toString("ss"):e<60?t.toString("mm"):e<1440?"Clock12Hours"===l.ma.Cd()?t.toString("h tt",i):t.toString("H",i):e<10080?t.toString("d"):10080===e?1===v.Dd()?t.weekNumberISO():t.weekNumber():t.toString("MMMM yyyy",i)},this.O=function(t,e,i){var n,s=l.startDate.addDays(l.days);e=e||this.cellGroupBy;var r=60;switch(e){case"Minute":t.getMinutes()+t.getSeconds()+t.getMilliseconds()>0&&(t=t.getDatePart().addHours(t.getHours()).addMinutes(t.getMinutes())),n=t.addMinutes(1);break;case"Hour":t.getHours()+t.getMinutes()+t.getSeconds()+t.getMilliseconds()>0&&(t=t.getDatePart().addHours(t.getHours())),n=t.addHours(1);break;case"Day":n=t.getDatePart().addDays(1);break;case"Week":for(n=t.getDatePart().addDays(1);n.dayOfWeek()!==v.Dd();)n=n.addDays(1);break;case"Month":t=t.getDatePart(),n=t.addMonths(1),n=n.firstDayOfMonth();for(var a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;!a;)n=n.addHours(1),a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;break;case"Quarter":for(t=t.getDatePart(),n=t.addMonths(1),n=n.firstDayOfMonth();n.getMonth()%3;)n=n.addMonths(1);for(var a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;!a;)n=n.addHours(1),a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;break;case"Year":t=t.getDatePart(),n=t.addYears(1),n=n.firstDayOfYear();for(var a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;!a;)n=n.addHours(1),a=DayPilot.DateUtil.diff(n,t)/6e4%r===0;break;case"None":n=s;break;case"Cell":var o=this.V(t);o.current&&(n=o.current.end);break;default:throw i?new DayPilot.Exception("Invalid scale value: "+e):new DayPilot.Exception("Invalid groupBy value: "+e)}return n.getTime()>s.getTime()&&(n=s),n},this.Bd=function(t,e){var i=this.nav.timeHeader,n=this.timeHeader[e][t],s=e<this.timeHeader.length-1,r=n.left,o=n.width,c=e*l.headerHeight,d=l.headerHeight,h=a();h.style.position="absolute",h.style.top=c+"px",h.style.left=r+"px",h.style.width=o+"px",h.style.height=d+"px",n.toolTip&&(h.title=n.toolTip),h.setAttribute("aria-hidden","true"),n.cssClass&&DayPilot.Util.addClass(h,n.cssClass),h.style.userSelect="none",h.style.webkitUserSelect="none",h.oncontextmenu=function(){return!1},h.cell={},h.cell.start=n.start,h.cell.end=n.end,h.cell.level=e,h.cell.th=n,h.onclick=this.gc,h.oncontextmenu=this.hc,DayPilot.re(h,DayPilot.touch.start,function(t){t.stopPropagation()}),h.style.overflow="hidden",l.timeHeaderTextWrappingEnabled||(h.style.whiteSpace="nowrap");var u=a();u.innerHTML=l.E(n.text,n.innerHTML),n.backColor&&(u.style.background=n.backColor),n.fontColor&&(u.style.color=n.fontColor);var v=this.D("_timeheadercol"),f=this.D("_timeheadercol_inner");s&&(v=this.D("_timeheadergroup"),f=this.D("_timeheadergroup_inner")),DayPilot.Util.addClass(h,v),DayPilot.Util.addClass(u,f),DayPilot.Util.addClass(h,l.D("_timeheader_cell")),DayPilot.Util.addClass(u,l.D("_timeheader_cell_inner")),h.appendChild(u),this.d.timeHeader[t+"_"+e]=h,
this.elements.timeHeader.push(h),i.appendChild(h)},this.Hb=function(){l.rowlist.forEach(function(t){var e=t.getHeight()+l.rowMarginTop+l.rowMarginBottom;t.height!==e&&(l.La=!0),t.height=e}),l.La&&(l.d.drawArea=null)},this.sa=function(){l.rowlist.forEach(function(t){var e=l.divHeader;if(e){var i=t.index;if(e.rows[i]){var n=e.rows[i];l.Zb&&(n.style.top=t.top+"px");var s=t.height;n&&n.firstChild&&parseInt(n.firstChild.style.height,10)!==s&&(n.firstChild.style.height=s+"px")}}}),l.Zb&&l.nav.resScrollSpace&&(l.nav.resScrollSpace.style.top=l.b+"px")},this.Mc=function(t){if(!DayPilot.Global.touch.start&&!DayPilot.Global.touch.active){i.Fd,1,l.ca(t);var e=DayPilot.Util.mouseButton(t);if(l.Gd())return!1;if(e.middle||e.right)return!1;if(l.Hd(l.coords))return!1;var n=e.left?"left":e.right?"right":e.middle?"middle":"unknown";return l.Id=n,i.xd=l.yd(),i.xd&&(i.Jd=l),!1}},this.yd=function(){var t={},e=l.S(l.coords.x).x,i=l.getDate(l.coords.x,!0);return t.start={y:l.gb(l.coords.y).i,x:e,"time":i},t.end={x:e,"time":i},t.calendar=l,l._a(t),t},this.Oc=function(t){if(l.Z={},l.$a){if(DayPilot.Util.mouseButton(t).left){var e=l.$a;if(l.Hd(l.coords)){var n=function(t){return function(){i.Fd=null;var e=l.Ya(t);if(e){var n={};n.start=e.start,n.end=e.end,n.resource=e.resource,n.preventDefault=function(){n.preventDefault.value=!0},"function"==typeof l.onTimeRangeClick&&l.onTimeRangeClick(n),n.preventDefault.value||"function"==typeof l.onTimeRangeClicked&&l.onTimeRangeClicked(n)}}};"Disabled"!==l.timeRangeClickHandling&&n(e)()}}}},this.Gd=function(){return!!(i.Nb||i.Z||i.xd)||!!l.Z.sd},this.dragInProgress=function(){return l.Gd()},this.ca=function(t){var e=DayPilot.mo3(l.k,t);e=e||{},e.stamp=e.x+"_"+e.y,l.coords&&l.coords.stamp===e.stamp||(l.coords=e)},this.Nc=function(t){if(!DayPilot.Global.touch.active){var e=DayPilot.mc(t);if(l.ca(t),l.Z.sd){DayPilot.distance(l.Z.qd,e)>2&&(DayPilot.Util.copyProps(l.Z,i),r().style.cursor="move",l.Z={})}i.Nb&&i.Rb.calendar===l?(i.Nb.event||(i.Nb.event=i.Rb),l.Kd()):i.Ub?l.Ld():i.xd&&i.xd.calendar===l&&(i.xd.moved=!0,l.Md())}},this.Md=function(){var t=i.xd,e=l.S(l.coords.x).x,n=l.getDate(l.coords.x,!0);t.end={x:e,"time":n},l._a(t)},this.Kd=function(){i.Qb||(i.Qb=l.kb(i.Nb)),l.Sb()},this.Ld=function(){if(!i.Vb){DayPilot.Util.addClass(i.Z,l.D("_event_moving_source"));var t=i.Ub;i.Vb=l.kb(t)}l.Tb()},this.Pc=function(t){return t.cancelBubble=!0,!1},this.Hd=function(t){var e=l.$a;if(!e||!e.start||!e.end)return!1;var i=this.mb(e.start.y),n=e.start.x<e.end.x,s=(n?e.start.x:e.end.x)*this.cellWidth,r=(n?e.end.x:e.start.x)*this.cellWidth+this.cellWidth,a=i.top,o=i.bottom;return t.x>=s&&t.x<=r&&t.y>=a&&t.y<=o},this._a=function(t){function e(t){var e,i,o,c,d=t.end.time>t.start.time,h=t.start.y,u=t.start.time&&t.end.time;if(s||!u){var v=d?t.start.x:t.end.x,f=d?t.end.x:t.start.x,p=l.T(v),b=l.T(f);e=p.left,i=b.left+b.width}else d?(o=t.start.time,c=t.end.time):(o=t.end.time,c=t.start.time),e=l.getPixels(o).left,i=l.getPixels(c).left;var g=i-e,y=l.elements.range2;if(!y){y=a(),y.style.position="absolute";var m=a();m.className=l.D("_shadow_inner"),n&&(y.style.borderRadius=n,m.style.borderRadius=n),y.appendChild(m),l.divShadow.appendChild(y)}return y.className=l.D("_shadow"),y.firstChild.innerHTML="",y.style.left=e+"px",y.style.top=r[h].top+"px",y.style.width=g+"px",y.style.height=r[h].height-1+"px",y.calendar=l,l.elements.range2=y,y}if(t=t||i.xd){var n=l.eventBorderRadius;"number"==typeof n&&(n+="px");var s=l.snapToGrid,r=l.rowlist;e(t)}},this.Nd=function(t){return{"start":{"x":t.start.x,"y":t.start.y,"time":t.start.time},"end":{"x":t.end.x,"time":t.end.time},"calendar":t.calendar,"args":t.args}},this.ib={},this.ib.Ba=null,this.ib.drawCells=null,this.ib.drawRows=null,this.ib.click=null,this.ib.resClick=[],this.ib.updateFloats=null,this.Ca=function(){if(!l.a){l.za();var t=l.nav.scroll;if(l.Wc=t.scrollLeft,l.Xc=t.scrollTop,l.Yc=t.clientWidth,l.divTimeScroll&&(l.divTimeScroll.scrollLeft=l.Wc),e&&i.Fc?n():l.divResScroll.scrollTop=l.Xc,l.progressiveRowRendering&&(l.ib.drawRows&&(clearTimeout(l.ib.drawRows),l.ib.drawRows=null),l.scrollDelayRows>0?l.ib.drawRows=setTimeout(function(){l.ac()},l.scrollDelayRows):l.ac()),l.ib.drawCells&&(clearTimeout(l.ib.drawCells),l.ib.drawCells=null),l.scrollDelayCells>0)l.ib.drawCells=setTimeout(l.Od(),l.scrollDelayCells);else{l.Od()()}l.ib.Ba&&(clearTimeout(l.ib.Ba),l.ib.Ba=null),l.scrollDelayEvents>0?l.ib.Ba=setTimeout(l.Pd(),l.scrollDelayEvents):l.Ba(),l.ib.updateFloats&&(clearTimeout(l.ib.updateFloats),l.ib.updateFloats=null),l.scrollDelayFloats>0?l.ib.updateFloats=setTimeout(function(){l.r()},l.scrollDelayFloats):l.r(),l.onScrollCalled=!0}},this.Od=function(){return function(){l&&l.Aa()}},this.Pd=function(){return function(){l&&(l.ed()?setTimeout(function(){l.ad(),setTimeout(function(){l.Ba(!0)},50)},50):l.s())}},this.za=function(){l.d.drawArea=null},this.show=function(){l.visible=!0,l.c=!0,l.nav.top.style.display="",l.Qd(),l.Kc(),l.Ca()},this.hide=function(){l.visible=!1,l.c=!1,l.nav.top.style.display="none"},this.Bb=function(t){if(!l.events.list)return null;for(var e=0;e<this.events.list.length;e++){var i=this.events.list[e];if(l.tb(i,t)){var n={};return n.ex=i,n.index=e,n}}return null},this.Rd=function(){var t=this.o(),e=t.xStart,i=t.xEnd-t.xStart,n=t.yStart,s=t.yEnd-t.yStart;this.cellProperties||(this.cellProperties={});for(var r=0;r<=i;r++){for(var a=e+r,o=0;o<s;o++){var l=n+o;this.Sd(a,l)}this.Td(a)}for(var c=this.Xb(),l=c.start;l<c.end;l++)this.Ud(l)},this.Aa=function(){if(!l.a){var t=l.rowlist;if(t&&t.length>0){if(this.cellSweeping){var e=this.cellSweepingCacheSize;this.cd(e)}this.Rd()}this.La=!1}},this.o=function(){if(l.d.drawArea)return l.d.drawArea;if(!this.nav.scroll)return null;var t=l.Xc,e={},i=null!=this.dynamicEventRenderingMarginX?this.dynamicEventRenderingMarginX:this.dynamicEventRenderingMargin,n=null!=this.dynamicEventRenderingMarginY?this.dynamicEventRenderingMarginY:this.dynamicEventRenderingMargin,s=l.Wc-i,r=s+l.Yc+2*i,a=0,o=0;a=l.S(s).x,o=l.S(r,!0).x;var c=this.ab();o=Math.min(o,c-1),a=DayPilot.Util.atLeast(a,0);var d=t-n,h=t+this.nav.scroll.offsetHeight+2*n,u=this.gb(d).i,v=this.gb(h).i;v<this.rowlist.length&&v++,e.xStart=a,e.xEnd=o,e.yStart=u,e.yEnd=v;var f=l.nav.scroll;return 0===f.clientWidth&&(f=l.divTimeScroll),e.pixels={},e.pixels.left=f.scrollLeft,e.pixels.right=f.scrollLeft+f.clientWidth,e.pixels.top=f.scrollTop,e.pixels.bottom=f.scrollTop+f.clientHeight,e.pixels.width=f.scrollWidth,e.sw=DayPilot.sw(l.nav.scroll),l.d.drawArea=e,e},this.H=function(){return l.ab()*l.cellWidth},this.Ud=function(t){var e=l.rowlist,i=l.divLines,n="y_"+t;if(!this.d.linesHorizontal[n]){var s=e[t],r=s.height,o=s.top+r-1,c=this.H(),d=a();d.style.left="0px",d.style.top=o+"px",d.style.width=c+"px",d.style.height="1px",d.style.fontSize="1px",d.style.lineHeight="1px",d.style.overflow="hidden",d.style.position="absolute",d.className=this.D("_matrix_horizontal_line"),i.appendChild(d),this.d.linesHorizontal[n]=d}},this.Td=function(t){var e=l.T(t);if(e){var i=l.divLines,n=l.b,s="x_"+t;if(!this.d.linesVertical[s]){var r=e.left+e.width-1,o=a();o.style.left=r+"px",o.style.top="0px",o.style.width="1px",o.style.height=n+"px",o.style.fontSize="1px",o.style.lineHeight="1px",o.style.overflow="hidden",o.style.position="absolute",o.className=this.D("_matrix_vertical_line"),i.appendChild(o),this.elements.linesVertical.push(o),this.d.linesVertical[s]=o}}},this.oa=function(){l.b=l.Vd(l.rowlist)},this.Vd=function(t){for(var e=0,i=0;i<t.length;i++){var n=t[i];n.top=e,e+=n.height}return e},this.xa=function(){l.elements.cells=[],l.d.cells=[],l.divCells.innerHTML="",l.Wd()},this.Wd=function(){l.divLines.innerHTML="",l.d.linesVertical={},l.d.linesHorizontal={},l.elements.linesVertical=[]},this.Pa=function(t){var e=[];for(var i in l.d.cells)e.push(l.d.cells[i]);e.filter(function(e){return e&&e.coords&&e.coords.y===t}).forEach(function(t){l.dd(t)})},this.Sd=function(t,e){if(this.g){var i=l.T(t);if(i){var n=l.rowlist,s=l.divCells,r=t+"_"+e;if(!this.d.cells[r]){var o=this.Xd(t,e),c=l.Yd(t,e),d=a();if(d.style.left=i.left+"px",d.style.top=n[e].top+"px",d.style.width=i.width+"px",d.style.height=n[e].height+"px",d.style.position="absolute",o&&o.backColor&&(d.style.backgroundColor=o.backColor),d.className=this.D("_cell"),d.coords={},d.coords.x=t,d.coords.y=e,o){if(o.cssClass&&DayPilot.Util.addClass(d,o.cssClass),d.innerHTML=l.E(o.text,o.html),o.backImage&&(d.style.backgroundImage='url("'+o.backImage+'")'),o.backRepeat&&(d.style.backgroundRepeat=o.backRepeat),o.business&&l.cellsMarkBusiness&&DayPilot.Util.addClass(d,l.D("_cell_business")),o.disabled&&DayPilot.Util.addClass(d,l.D("_cell_disabled")),o.backColor&&(d.style.backgroundColor=o.backColor),o.fontColor&&(d.style.color=o.fontColor),o.horizontalAlignment||o.verticalAlignment){switch(d.style.display="flex",o.horizontalAlignment){case"right":d.style.justifyContent="flex-end";break;case"left":d.style.justifyContent="flex-start";break;case"center":d.style.justifyContent="center"}switch(o.verticalAlignment){case"center":d.style.alignItems="center";break;case"top":d.style.alignItems="flex-start";break;case"bottom":d.style.alignItems="flex-end"}}DayPilot.Areas.attach(d,c.cell,{"areas":o.areas||c.cell.areas})}s.appendChild(d),this.elements.cells.push(d),this.d.cells[r]=d}}}},this.Yd=function(t,e){var i=l.T(t);if(!i)return null;var n=l.rowlist[e],s=n.id,r=i.start,a=i.end,o={};if(o.cell={x:t,y:e,start:r,end:a,resource:s,row:l.fc(n),properties:l.Xd(t,e)},o.control=l,"function"==typeof this.onBeforeCellRender){var c=t+"_"+e;if(l.beforeCellRenderCaching&&l.ya[c])return o;l.ya[c]=!0,this.onBeforeCellRender(o)}return o},this.clearSelection=function(){this.Zd()},this.$d=function(t,e,i){t=new DayPilot.Date(t),e=new DayPilot.Date(e);var n=l.m(i),s=l.V(t);if(!s.current)throw new DayPilot.Exception("Time range selection 'start' out of timeline");var r=l.V(new DayPilot.Date(e).addMilliseconds(-1));if(!r.current)throw new DayPilot.Exception("Time range selection 'end' out of timeline");var a={};return a.start={y:n.index,x:s.i,"time":t},a.end={x:r.i,"time":e},a.calendar=this,a},this.selectTimeRange=function(t,e,i,n){var s=l.$d(t,e,i);l._a(s),n||setTimeout(function(){l.Wa(s)},0)},this.Ua=function(){var t=i.Vb&&i.Vb.source;t&&DayPilot.Util.removeClass(t,l.D("_event_moving_source")),DayPilot.de(i.Vb),i.Vb=null,DayPilot.Global.movingLink&&(DayPilot.Global.movingLink.clear(),DayPilot.Global.movingLink=null)},this.Zd=function(){l.divShadow&&(l.divShadow.innerHTML=""),l.elements.range=[],l.elements.range2=null,l.$a=null,l._d=null},this.ma={};var v=this.ma;v.la=function(){delete l.d.headerHeight},v.nd=function(){return"Disabled"!==l.xssProtection},v.P=function(){return DayPilot.Locale.find(l.locale)},v.Cd=function(){return"Auto"!==l.timeFormat?l.timeFormat:v.P().timeFormat},v.Dd=function(){if("Auto"===l.weekStarts){var t=v.P();return t?t.weekStarts:0}return l.weekStarts||0},v.cc=function(){var t=navigator.userAgent.toLowerCase();return t.indexOf("mobile")!==-1||t.indexOf("android")!=-1},v.Lb=function(t){return"Always"===l.useEventBoxes||"Never"!==l.useEventBoxes&&t<60*l.W()*1e3},this.Xd=function(t,e){var i=t+"_"+e,n=l.rowlist;if(this.cellProperties||(this.cellProperties={}),this.cellProperties[i])return this.cellProperties[i];if(!this.cellProperties[i]){var s=n[e],r=s.id,a=l.T(t),o=a.start,c=a.end,d={};d.start=o,d.end=c,d.resource=r;var h={};h.business=l.isBusiness(d),this.cellProperties[i]=h}return this.cellProperties[i]},this.isBusiness=function(t,e){var i=t.start,n=t.end,s=(n.getTime()-i.getTime())/6e4;if(s<=1440&&!(l.businessWeekends||e||0!==t.start.dayOfWeek()&&6!==t.start.dayOfWeek()))return!1;if(s<720){var r=i.getHours();r+=i.getMinutes()/60,r+=i.getSeconds()/3600,r+=i.getMilliseconds()/36e5;var a=this.businessBeginsHour,o=this.businessEndsHour;if(0===o&&(o=24),a===o)return!1;if(!(a<o))return r<o||r>=a;if(r<a)return!1;if(o>=24)return!0;if(r>=o)return!1}return!0},this.Qd=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.ae=function(t){this.heightSpec="Fixed",this.height=t-(this.L()+2),this.J()},this.setHeight=this.ae,this.m=function(t){return l.jc(t)[0]},this.be=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw new DayPilot.Exception("DayPilot.Scheduler() constructor requires the target element or its ID as a parameter");if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw new DayPilot.Exception("DayPilot.Scheduler: The placeholder element not found: '"+s+"'.")}},this.init=function(){if(this.g)throw new DayPilot.Exception("This instance is already initialized. Use update() to change properties.");if(this.be(),this.nav.top.dp){if(this.nav.top.dp===l)return l;throw new DayPilot.Exception("The target placeholder was already initialized by another DayPilot component instance.")}return this.ce(),this.Jc(),this},this.ce=function(){this.uc(),this.Qc(),this.hb(),this.Vc(),this.ha();var angular=l.G.enabled;l.scrollToDate?l.scrollTo(l.scrollToDate):l.scrollX||l.scrollY?l.setScroll(l.scrollX,l.scrollY):angular||l.Ca(),l.scrollToResourceId&&(l.scrollToResource(l.scrollToResourceId),l.scrollToResourceId=null);var t=function(){l.scrollY&&l.setScroll(l.scrollX,l.scrollY)};setTimeout(t,200),this.za(),this.Ga(),this.g=!0;var e=l.j;e?l.scrollTo(e):l.Ca()},this.ee=null,this.Fa=function(t){if(t){var e={"resources":{"preInit":function(){var t=this.data;t&&(l.resources=t)}},"events":{"preInit":function(){var t=this.data;t&&(DayPilot.isArray(t.list)?l.events.list=t.list:l.events.list=t)},"postInit":function(){}},"scrollTo":{"preInit":function(){},"postInit":function(){this.data&&l.f(this.data)}},"scrollX":{"postInit":function(){this.data&&l.l(this.data)}},"scrollY":{"postInit":function(){this.data&&l.I(this.data)}}};l.ee=e,l.G.scrollToRequested&&(e.scrollTo.data=l.G.scrollToRequested,l.G.scrollToRequested=null),l.G.scrollXRequested&&(e.scrollX.data=l.G.scrollXRequested,l.G.scrollXRequested=null),l.G.scrollYRequested&&(e.scrollY.data=l.G.scrollYRequested,l.G.scrollYRequested=null);for(var i in t)e[i]||(l[i]=t[i]);for(var i in t)if(e[i]){var n=e[i];n.data=t[i],n.preInit&&n.preInit()}}},this.Ga=function(){var t=l.ee;for(var e in t){var i=t[e];i.postInit&&i.postInit()}l.ee={}},this.fe={},this.fe.ge=null,this.fe.he=function(t,e,i){var n=l.fe.ge;if("function"==typeof n.createVNode&&"function"==typeof n.render){var s=n.createVNode(t,i);n.render(s,e)}},this.fe.ie=function(t,e){var i=l.fe.ge;if("function"==typeof i.render){var n=t;DayPilot.isArray(t)&&(n=i.h("div",null,t)),i.render(n,e)}},this.fe.je=function(t){var e=l.fe.ge;"function"==typeof e.render&&e.render(null,t)},this.internal={},this.internal.adjustEndIn=l.Qa,this.internal.adjustEndNormalize=l.Ra,this.internal.xssTextHtml=l.E,this.internal.touch=l.ud,this.internal.skipUpdate=l.G.skipUpdate,this.internal.skipped=l.G.skipped,this.internal.loadOptions=l.Fa,this.internal.postInit=l.Ga,this.internal.enableAngular2=function(){l.G.enabled=!0},this.internal.eventsFromAttr=function(){l.G.zb=!0},this.internal.resourcesFromAttr=function(){l.G.Ib=!0},this.internal.evImmediateRefresh=function(){l.events.Va()},this.internal.enableReact=function(t,e){l.Jb.react=t,l.Jb.reactDOM=e},this.internal.reactRefs=function(){return DayPilot.Util.copyProps(l.Jb,{},["react","reactDOM"])},this.internal.enableVue=function(t){l.fe.ge=t},this.internal.vueRef=function(){return l.fe.ge},this.internal.vueRendering=function(){return l.fe.ke},this.Fa(o)},s="2025.3.696"===(new DayPilot.Scheduler).v,DayPilot.Row=function(t,e){if(!t)throw new DayPilot.Exception("Now row object supplied when creating DayPilot.Row");if(!e)throw new DayPilot.Exception("No parent control supplied when creating DayPilot.Row");this.le={};var i=this.le;i.id=t.id,i.name=t.name,i.data=t.resource,i.tags=t.tags;var n=this;n.isRow=!0,n.menuType="resource",n.name=t.name,n.id=t.id,n.tags=t.tags,n.index=t.index,n.calendar=e,n.data=t.resource,n.me=t,n.$={},n.$.row=t,n.toJSON=function(t){var e={};return e.start=this.start,e.name=this.name,e.id=this.id,e.index=this.index,e},n.events={},n.events.all=function(){for(var t=[],e=0;e<n.me.events.length;e++)t.push(n.me.events[e]);return t},n.events.isEmpty=function(){return 0===n.me.events.length},n.events.forRange=function(t,e){return n.me.events.forRange(t,e)},n.events.totalDuration=function(){var t=0;return n.events.all().forEach(function(e){t+=e.part.end.getTime()-e.part.start.getTime()}),new DayPilot.Duration(t)},n.remove=function(){e.rows.remove(n)},n.addClass=function(t){var i=e.divHeader,s=i.rows[n.index];DayPilot.Util.addClass(s,t),n.$.row.cssClass=DayPilot.Util.addClassToString(n.$.row.cssClass,t),n.data.cssClass=t},n.removeClass=function(t){var i=e.divHeader,s=i.rows[n.index];DayPilot.Util.removeClass(s,t),n.$.row.cssClass=DayPilot.Util.removeClassFromString(n.$.row.cssClass,t),n.data.cssClass=DayPilot.Util.removeClassFromString(n.data.cssClass,t)}},i.Z=null,i.Ub=null,i.qd=null,i.Nb=null,i.Rb=null,i.Y=!1,i.Rc=!1,i.Fd=null,i.ne=null,i.jb=function(t){DayPilot.ue(document,"mouseup",i.Sc),DayPilot.ue(document,"touchmove",i.Tc),DayPilot.ue(document,"touchend",i.Uc),i.Rc=!1},i.Tc=function(t){if(i.Nb){var e=i.Nb.event.calendar;e.coords=e.ud.wd(t),e.ud.zd(),t.preventDefault()}if(i.Z){t.preventDefault();var e=i.Ub.calendar;e.coords=e.ud.wd(t),e.ud.Ad()}},i.Uc=function(t){i.Fc=!1,i.Sc(t)},i.Sc=function(t){if(i.Nb){var e=function(){var t=i.Rb,e=t.calendar;r().style.cursor="",i.Nb=null,i.Rb=null,DayPilot.de(i.Qb),i.Qb=null,e&&(e.oe=null)};if(setTimeout(function(){i.Y=!1}),!i.Qb)return void e();var s=i.Rb,a=s.calendar,o=i.Qb.start,l=i.Qb.end,c="left"===i.Nb.dpBorder?"start":"end";e(),a.ea(s,o,l,c)}else if(i.Ub){var e=function(){DayPilot.Global.movingAreaData=null;var t=i.Vb&&i.Vb.calendar;i.Vb&&(DayPilot.de(i.Vb),i.Vb.calendar=null),r().style.cursor="",i.Z=null,i.Ub=null,t&&(t.Ta=null)};if(!i.Vb)return void e();var s=i.Ub,a=i.Vb.calendar;if(!a)return void e();if(i.Vb.source=i.Z,!i.Vb.row)return void e();var o=i.Vb.start,l=i.Vb.end,d=i.Vb.row.id;i.Vb.calendar=null,r().style.cursor="",i.Z=null,i.Ub=null,a.Sa(s,o,l,d,t),DayPilot.Global.movingAreaData=null}else if(i.xd){var h=DayPilot.Util.mouseButton(t),u=i.xd,a=u.calendar,e=function(){};a._d=null;t.ctrlKey||t.metaKey;if(i.Fd)return clearTimeout(i.Fd),i.Fd=null,void e();a.$a=u,i.xd=null;var v=function(t){return function(){i.Fd=null,a.Wa(t),"Hold"!==a.timeRangeSelectedHandling&&"HoldForever"!==a.timeRangeSelectedHandling?n():a.$a=t}},f=a.Nd(u);return e(),h.left?(v(f)(),t.cancelBubble=!0,!1):void(i.Fd=null)}i.td=null,i.Mb=null}}}(DayPilot);