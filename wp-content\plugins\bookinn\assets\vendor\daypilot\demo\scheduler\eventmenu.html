﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Context Menu (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">Activate the context menu by right-clicking an event. Read more about the <a
        href="https://doc.daypilot.org/scheduler/event-context-menu/">event context menu</a> [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            startDate: new DayPilot.Date("2025-08-01"),
            cellGroupBy: "Month",
            days: new DayPilot.Date("2025-08-01").daysInMonth(),
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Day", format: "d"}
            ],
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
                {name: "Room G", id: "G"},
            ],
            contextMenu: new DayPilot.Menu({
                items: [
                    {
                        text: "Delete", onClick: args => dp.events.remove(args.source)
                    },
                    {text: "-"},
                    {
                        text: "Change color", onClick: args => {
                            args.source.data.barColor = "#cc0000";
                            args.source.data.barBackColor = "#e06666";
                            dp.events.update(args.source);
                        }
                    },
                    {text: "-"},
                    {
                        text: "Submenu", items: [
                            {
                                text: "Delete", onClick: args => dp.events.remove(args.source)
                            },
                        ]
                    },
                    {
                        text: "Disabled menu item", onClick: () => alert("disabled"), disabled: true
                    }
                ]
            }),
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) return;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            }
        });

        dp.init();



        const app = {
            init() {
                this.loadEventData();
            },
            loadEventData() {
                const events = [
                    {
                        start: new DayPilot.Date("2025-08-05T00:00:00"),
                        end: new DayPilot.Date("2025-08-10T12:00:00"),
                        id: DayPilot.guid(),
                        resource: "B",
                        text: "Event",
                    }
                ];
                dp.update({events});
            }
        };
        app.init();


    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

