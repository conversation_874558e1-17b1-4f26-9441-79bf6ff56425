﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
if("undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";if("undefined"==typeof DayPilot.Menu||!DayPilot.Menu.def){var e=function(){},t={};t.mouse=null,t.menu=null,t.handlersRegistered=!1,t.hideTimeout=null,t.waitingSubmenu=null,DayPilot.Menu=function(n){var i=this,o=null;this.v="2025.3.696-lite",this.zIndex=120,this.cssClassPrefix="menu_default",this.cssOnly=!0,this.menuTitle=null,this.showMenuTitle=!1,this.hideOnMouseOut=!1,this.hideAfter=200,this.theme=null,this.onShow=null,this.b=function(){},n&&DayPilot.isArray(n)&&(this.items=n),this.toJSON=function(){return null},this.show=function(n,u){u=u||{};var a=null;if(n?"string"==typeof n.id||"number"==typeof n.id?a=n.id:"function"==typeof n.id?a=n.id():"function"==typeof n.value&&(a=n.value()):a=null,"undefined"!=typeof DayPilot.Bubble&&DayPilot.Bubble.hideActive(),u.submenu||t.menuClean(),this.b.submenu=null,null!==t.mouse){i.cssOnly||(i.cssOnly=!0);var s=null;if(n&&n.isRow&&n.$.row.task?(s=new DayPilot.Task(n.$.row.task,n.calendar),s.menuType="resource"):s=n&&n.isEvent&&n.data.task?new DayPilot.Task(n,n.calendar):n,"function"==typeof i.onShow){var c={};if(c.source=s,c.menu=i,c.preventDefault=function(){c.preventDefault.value=!0},i.onShow(c),c.preventDefault.value)return}var l=document.createElement("div");if(l.style.position="absolute",l.style.top="0px",l.style.left="0px",l.style.display="none",l.style.overflow="hidden",l.style.zIndex=this.zIndex+1,l.className=this.c("main"),l.onclick=function(e){e.cancelBubble=!0,this.parentNode.removeChild(this)},this.hideOnMouseOut&&(l.onmousemove=function(e){clearTimeout(t.hideTimeout)},l.onmouseleave=function(e){i.delayedHide({"hideParent":!0})}),!this.items||0===this.items.length)throw"No menu items defined.";if(this.showMenuTitle){var r=document.createElement("div");r.innerHTML=this.menuTitle,r.className=this.c("title"),l.appendChild(r)}for(var d=0;d<this.items.length;d++){var m=this.items[d],f=document.createElement("div");if(DayPilot.Util.addClass(f,this.c("item")),m.items&&(DayPilot.Util.addClass(f,this.c("item_haschildren")),DayPilot.Util.addClass(l,this.c("withchildren"))),"undefined"!=typeof m&&!m.hidden){if("-"===m.text){var h=document.createElement("div");h.addEventListener("click",function(e){e.stopPropagation()}),f.appendChild(h)}else{var v=document.createElement("a");if(v.style.position="relative",v.style.display="block",m.cssClass&&DayPilot.Util.addClass(v,m.cssClass),m.disabled)DayPilot.Util.addClass(v,i.c("item_disabled"));else{if(m.onclick||m.onClick){v.item=m,v.onclick=function(e,t){return function(n){if("function"==typeof e.onClick){var i={};if(i.item=e,i.source=t.source,i.originalEvent=n,i.preventDefault=function(){i.preventDefault.value=!0},e.onClick(i),i.preventDefault.value)return void n.stopPropagation()}e.onclick&&e.onclick.call(t,n)}}(m,v);var p=function(e,t){return function(e){e.stopPropagation(),e.preventDefault(),t.source.calendar&&t.source.calendar.internal.touch&&(t.source.calendar.internal.touch.active=!0)}},b=function(e,n){return function(i){i.stopPropagation(),i.preventDefault();var o=function(){window.setTimeout(function(){n.source.calendar&&n.source.calendar.internal.touch&&(n.source.calendar.internal.touch.active=!1)},500)};if("function"==typeof e.onClick){var u={};if(u.item=e,u.source=n.source,u.originalEvent=i,u.preventDefault=function(){u.preventDefault.value=!0},e.onClick(u),u.preventDefault.value)return void o()}e.onclick&&e.onclick.call(n,i),t.menuClean(),o()}};DayPilot.reNonPassive(v,"touchstart",p(m,v)),DayPilot.reNonPassive(v,"touchend",b(m,v))}if(m.items&&!m.disabled){var y=function(e,t){return function(n){n.preventDefault(),n.stopPropagation(),i.d(e,t)}};v.ontouchend=y(m,v)}if(m.onclick)e();else if(m.href)v.href=m.href.replace(/\x7B0\x7D/gim,a),m.target&&v.setAttribute("target",m.target);else if(m.command){var y=function(e,t){return function(n){var i=t.source,o=e;o.action=o.action?o.action:"CallBack";var u=i.calendar||i.root;if(i instanceof DayPilot.Link)return void u.internal.linkMenuClick(o.command,i,o.action);if(i instanceof DayPilot.Selection)return void u.internal.timeRangeMenuClick(o.command,i,o.action);if(i instanceof DayPilot.Event)return void u.internal.eventMenuClick(o.command,i,o.action);if(i instanceof DayPilot.Selection)return void u.internal.timeRangeMenuClick(o.command,i,o.action);if(i instanceof DayPilot.Task)return void("resource"===i.menuType?u.internal.resourceHeaderMenuClick(o.command,t.menuSource,o.action):u.internal.eventMenuClick(o.command,t.menuSource,o.action));switch(i.menuType){case"resource":return void u.internal.resourceHeaderMenuClick(o.command,i,o.action);case"selection":return void u.internal.timeRangeMenuClick(o.command,i,o.action);default:return void u.internal.eventMenuClick(o.command,i,o.action)}n.preventDefault()}};v.onclick=y(m,v),v.ontouchend=y(m,v)}}m.items&&v.addEventListener("click",function(e){e.stopPropagation()}),v.source=s,v.menuSource=n;var w=document.createElement("span");if(w.className=i.c("item_text"),w.innerHTML=DayPilot.Util.escapeTextHtml(m.text,m.html),v.appendChild(w),m.image){var g=document.createElement("img");g.src=m.image,g.style.position="absolute",g.style.top="0px",g.style.left="0px",v.appendChild(g)}if(m.icon){var C=document.createElement("span");C.className=i.c("item_icon");var M=document.createElement("i");M.className=m.icon,C.appendChild(M),v.appendChild(C)}if(m.symbol){var k="http://www.w3.org/2000/svg",x=document.createElementNS(k,"svg");x.setAttribute("width","100%"),x.setAttribute("height","100%");var T=document.createElementNS(k,"use");T.setAttribute("href",m.symbol),x.appendChild(T);var E=document.createElement("span");E.className=i.c("item_symbol"),E.style.position="absolute",E.style.top="0px",E.style.left="0px",E.appendChild(x),v.appendChild(E)}var P=function(e,n){return function(){var o=(n.source,e),u=t.waitingSubmenu;if(u){if(u.parent===o)return;clearTimeout(u.timeout),t.waitingSubmenu=null}e.disabled||(t.waitingSubmenu={},t.waitingSubmenu.parent=o,t.waitingSubmenu.timeout=setTimeout(function(){t.waitingSubmenu=null,i.d(o,n)},300))}};v.onmouseover=P(m,v),f.appendChild(v)}l.appendChild(f)}}var S=function(e){window.setTimeout(function(){t.menuClean(),DayPilot.MenuBar.deactivate()},100)};l.onclick=S,l.ontouchend=S,l.onmousedown=function(e){e=e||window.event,e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()},l.oncontextmenu=function(){return!1},document.body.appendChild(l),i.b.visible=!0,i.b.source=n,l.style.display="";var D=l.offsetHeight,N=l.offsetWidth;l.style.display="none";var B=document.documentElement.clientHeight,H=window.innerWidth,_="number"==typeof u.windowMargin?u.windowMargin:5;if(function(){var e=u.initiator;if(e){var t=e.div,n=e.e,i=e.area,a=i.visibility||i.v||"Visible",s=e.a;if("Visible"!==a&&(s=DayPilot.Areas.createArea(t,n,i),t.appendChild(s),o=s),s){var c=DayPilot.abs(s);u.x=c.x,u.y=c.y+c.h+2}}}(),function(){var e="number"==typeof u.x?u.x:t.mouse.x+1,n="number"==typeof u.y?u.y:t.mouse.y+1,o=document.body.scrollTop||document.documentElement.scrollTop,a=document.body.scrollLeft||document.documentElement.scrollLeft,s=0,c=0;if(n-o>B-D&&0!==B){s=n-(n-o-(B-D)+_)}else s=n;if(i.b.y=s,l.style.top=s+"px","right"===u.align&&(e-=N),e-a>H-N&&0!==H){c=e-(e-a-(H-N)+_)}else c=e;i.b.x=c,l.style.left=c+"px"}(),u.parentLink){var L=u.parentLink,O=parseInt(new DayPilot.StyleReader(l).get("border-top-width")),A=DayPilot.abs(u.parentLink.parentNode),U=A.x+L.offsetWidth,R=A.y-O;U+N>H&&(U=Math.max(0,A.x-N));var I=document.body.scrollTop+document.documentElement.scrollTop;R+D-I>B&&(R=Math.max(0,B-D+I)),l.style.left=U+"px",l.style.top=R+"px"}l.style.display="",this.addShadow(l),this.b.div=l,u.submenu||(DayPilot.Menu.active=this)}},this.update=function(){if(i.b.visible){var e=i.b.source;i.hide(),i.show(e,{"x":i.b.x,"y":i.b.y})}},this.d=function(e,t){var n=e,o=t.source;if((!i.b.submenu||i.b.submenu.item!==e)&&(i.b.submenu&&i.b.submenu.item!==e&&(DayPilot.Util.removeClass(i.b.submenu.link.parentNode,i.c("item_haschildren_active")),i.b.submenu.menu.hide(),i.b.submenu=null),e.items)){var u=i.cloneOptions();u.items=e.items,i.b.submenu={},i.b.submenu.menu=new DayPilot.Menu(u),i.b.submenu.menu.f=i,i.b.submenu.menu.show(o,{"submenu":!0,"parentLink":t,"parentItem":n}),i.b.submenu.item=e,i.b.submenu.link=t,DayPilot.Util.addClass(t.parentNode,i.c("item_haschildren_active"))}},this.c=function(e){var t=this.theme||this.cssClassPrefix,n=this.cssOnly?"_":"";return t?t+n+e:""},this.cloneOptions=function(){return DayPilot.Util.copyProps(u,{},["cssClassPrefix","theme","hideAfter","hideOnMouseOut","zIndex"])},this.hide=function(e){e=e||{},this.b.submenu&&this.b.submenu.menu.hide();var n=t.waitingSubmenu;if(n&&(t.waitingSubmenu=null,clearTimeout(n.timeout)),this.removeShadow(),this.b.div&&this.b.div.parentNode===document.body&&document.body.removeChild(this.b.div),o&&(DayPilot.de(o),o=null),i.b.visible=!1,i.b.source=null,i.f&&e.hideParent&&i.f.hide(e),DayPilot.Menu.active===i&&(DayPilot.Menu.active=null),"function"==typeof this.onHide){var u={};this.onHide(u)}},this.delayedHide=function(e){t.hideTimeout=setTimeout(function(){i.hide(e)},i.hideAfter)},this.cancelHideTimeout=function(){clearTimeout(t.hideTimeout)},this.init=function(e){return t.mouseMove(e),this},this.addShadow=function(e){},this.removeShadow=function(){};var u=DayPilot.isArray(n)?null:n;if(u)for(var a in u)this[a]=u[a]},DayPilot.MenuBar=function(e,t){var n=this;t=t||{},this.items=[],this.theme="menubar_default",this.windowMargin=0,this.nav={},this.elements={},this.elements.items=DayPilot.list(),this.g=null,this.i=!1;for(var i in t)this[i]=t[i];this.j=function(e){return this.theme+"_"+e},this.k=function(){this.nav.top=document.getElementById(e);var t=this.nav.top;t.className=this.j("main"),DayPilot.list(n.items).forEach(function(e){var i=document.createElement("span");i.innerHTML=DayPilot.Util.escapeTextHtml(e.text,e.html),i.className=n.j("item"),e.cssClass&&i.classList.add(e.cssClass),i.data=e,i.onclick=function(t){if(n.active&&n.active.item===e)n.l();else if(e.children)return void n.m(i);if("function"==typeof e.onClick){var o={};o.item=e,o.originalEvent=t,e.onClick(o)}},i.onmousedown=function(e){e.stopPropagation()},i.onmouseover=function(){n.active&&n.active.item!==e&&n.m(i)},t.appendChild(i),n.elements.items.push(i)})},this.l=function(){var e=n.j("item_active");n.elements.items.forEach(function(t){DayPilot.Util.removeClass(t,e)}),n.active&&n.active.menu&&n.active.menu.hide(),n.active=null},this.n=function(e){return!!n.active&&n.active.item===e.data},this.m=function(e){if(!n.n(e)){n.l();var t=e.data,i=n.active={};i.item=t,i.div=e;var o=n.j("item_active");DayPilot.Util.addClass(e,o);var u=DayPilot.abs(e);if(t.children){i.menu=new DayPilot.Menu({"items":t.children});var a=u.x;"right"===t.align&&(a+=u.w),i.menu.show(null,{"x":a,"y":u.y+u.h,"align":t.align,"windowMargin":n.windowMargin})}DayPilot.MenuBar.active=n}},this.init=function(){return this.k(),this.i=!0,this},this.dispose=function(){this.i&&(this.nav.top.innerHTML="",this.elements.items=[])}},DayPilot.MenuBar.deactivate=function(){DayPilot.MenuBar.active&&(DayPilot.MenuBar.active.l(),DayPilot.MenuBar.active=null)},t.menuClean=function(){"undefined"!=typeof DayPilot.Menu.active&&DayPilot.Menu.active&&(DayPilot.Menu.active.hide(),DayPilot.Menu.active=null)},t.mouseDown=function(e){"undefined"!=typeof t&&(t.menuClean(),DayPilot.MenuBar.deactivate())},t.wheel=function(e){"undefined"!=typeof t&&(t.menuClean(),DayPilot.MenuBar.deactivate())},t.mouseMove=function(e){"undefined"!=typeof t&&(t.mouse=t.mousePosition(e))},t.touchMove=function(e){"undefined"!=typeof t&&(t.mouse=t.touchPosition(e))},t.touchStart=function(e){"undefined"!=typeof t&&(t.mouse=t.touchPosition(e))},t.touchEnd=function(e){},t.touchPosition=function(e){if(!e||!e.touches)return null;var t=e.touches[0],n={};return n.x=t.pageX,n.y=t.pageY,n},t.mousePosition=function(e){return DayPilot.mo3(null,e)},DayPilot.Menu.touchPosition=function(e){e.touches&&(t.mouse=t.touchPosition(e))},DayPilot.Menu.hide=function(e){if(e=e||{},e.calendar){var n=DayPilot.Menu.active;if(n){var i=n.b.source;i&&i.calendar===e.calendar&&t.menuClean()}}else t.menuClean()},t.handlersRegistered||"undefined"==typeof document||(DayPilot.re(document,"mousemove",t.mouseMove),DayPilot.re(document,"mousedown",t.mouseDown),DayPilot.re(document,"wheel",t.wheel),DayPilot.re(document,"touchmove",t.touchMove),DayPilot.re(document,"touchstart",t.touchStart),DayPilot.re(document,"touchend",t.touchEnd),t.handlersRegistered=!0),DayPilot.Menu.def={}}}(DayPilot);