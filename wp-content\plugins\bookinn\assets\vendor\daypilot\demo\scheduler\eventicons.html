﻿<!DOCTYPE html>
<html>
<head>
    <title>Event Icons (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">
        This demo uses SVG symbol icons which are displayed using event active areas. Read more about <a href="https://doc.daypilot.org/scheduler/event-icons/">event icons</a>.
    </div>

    <div id="dp"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-01-01",
            days: 365,
            scale: "Day",
            timeHeaders: [
                {groupBy: "Month", format: "MMMM yyyy"},
                {groupBy: "Day", format: "d"}
            ],
            resources: [
              {name: "Room 1", id: "A"},
              {name: "Room 2", id: "B"},
              {name: "Room 3", id: "C"},
              {name: "Room 4", id: "D"},
              {name: "Person 1", id: "E"},
              {name: "Person 2", id: "F"},
              {name: "Person 3", id: "G"},
              {name: "Person 4", id: "H"},
              {name: "Tool 1", id: "I"},
              {name: "Tool 2", id: "J"},
              {name: "Tool 3", id: "K"},
              {name: "Tool 4", id: "L"}
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "New Event");
                dp.clearSelection();
                if (modal.canceled) return;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
            onBeforeEventRender: args => {
                args.data.areas = [
                    {right: 10, top: "calc(50% - 7px)", width: 18, height: 18, symbol: "../icons/daypilot.svg#checkmark-2", backColor: "#999999", fontColor: "#ffffff", padding: 2, style: "border-radius: 50%" }
                ];
            },
            height: 350
        });

        dp.init();

        dp.scrollTo("2025-03-01");

        const app = {
            loadEventData() {
                // generate and load events
                const events = [];
                for (let i = 0; i < 12; i++) {
                    const duration = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const durationDays = Math.floor(Math.random() * 6) + 1; // 1 to 6
                    const start = Math.floor(Math.random() * 6) + 2; // 2 to 7

                    const event = {
                        start: new DayPilot.Date("2025-03-05T12:00:00").addDays(start),
                        end: new DayPilot.Date("2025-03-05T12:00:00").addDays(start).addDays(durationDays).addHours(duration),
                        id: DayPilot.guid(),
                        resource: String.fromCharCode(65 + i),
                        text: `Event ${i + 1}`,
                        barColor: app.barColor(i),
                        barBackColor: app.barBackColor(i)
                    };

                    events.push(event);
                }

                dp.update({events});
            },
            barColor(i) {
                const colors = ["#3c78d8", "#6aa84f", "#f1c232", "#cc0000"];
                return colors[i % 4];
            },
            barBackColor(i) {
                const colors = ["#a4c2f4", "#b6d7a8", "#ffe599", "#ea9999"];
                return colors[i % 4];
            },
            init() {
                this.loadEventData();
            }
        };
        app.init();

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->


</body>
</html>

