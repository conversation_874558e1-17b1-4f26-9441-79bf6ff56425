<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
    <symbol id="bubble" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <path d="M19,5 C19.5522847,5 20.0522847,5.22385763 20.4142136,5.58578644 C20.7761424,5.94771525 21,6.44771525 21,7 L21,7 L21,15 C21,15.5522847 20.7761424,16.0522847 20.4142136,16.4142136 C20.0522847,16.7761424 19.5522847,17 19,17 L19,17 L11.9795349,17 L7,20.5568107 L7,17 L5,17 C4.44771525,17 3.94771525,16.7761424 3.58578644,16.4142136 C3.22385763,16.0522847 3,15.5522847 3,15 L3,15 L3,7 C3,6.44771525 3.22385763,5.94771525 3.58578644,5.58578644 C3.94771525,5.22385763 4.44771525,5 5,5 L5,5 Z" stroke="currentColor" stroke-width="2"></path>
        </g>
    </symbol>
    <symbol id="chevron-down-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-down-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-left-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-left-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-right-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) scale(-1, 1) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-right-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) scale(-1, 1) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-up-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="chevron-up-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) " points="18 4 6 12 18 20"></polyline>
        </g>
    </symbol>
    <symbol id="figure" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <path d="M12,11.5 C13.4336884,11.5 14.7409542,11.9985771 15.7229284,12.8202217 C16.6923626,13.6313737 17.3445681,14.7583319 17.4756317,16.0260392 L17.4756317,16.0260392 L17.4979566,21.5 L6.5,21.5 L6.5,16.0273735 C6.6430767,14.7582808 7.30211548,13.6301836 8.27609304,12.8185356 C9.26075446,11.9979844 10.5672177,11.5 12,11.5 Z" stroke="currentColor" fill="currentColor"></path>
            <circle stroke="currentColor" fill="currentColor" cx="12" cy="6" r="3.5"></circle>
        </g>
    </symbol>
    <symbol id="figure-wider" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <path d="M17.3986028,11.7703936 C19.1328676,14.2210558 20,18.2975913 20,24 L20,24 L4,24 C4,18.2975913 4.8671324,14.2210558 6.6013972,11.7703936 C8.02399116,13.1559564 9.91922525,14 12,14 C14.0454991,14 15.9116952,13.1843322 17.3261881,11.8424218 Z" fill="currentColor"></path>
            <circle fill="currentColor" cx="12" cy="6" r="5"></circle>
        </g>
    </symbol>
    <symbol id="hamburger-menu" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"></line>
            <line x1="5" y1="7" x2="19" y2="7" stroke="currentColor" stroke-width="2"></line>
            <line x1="5" y1="17" x2="19" y2="17" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="i-circle" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <circle stroke="currentColor" stroke-width="2" cx="12" cy="12" r="9"></circle>
            <circle fill="currentColor" cx="12" cy="8" r="2"></circle>
            <line x1="12" y1="13" x2="12" y2="16" stroke="currentColor" stroke-width="4" stroke-linecap="round"></line>
        </g>
    </symbol>
    <symbol id="minus-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="minus-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="4"></line>
        </g>
    </symbol>
    <symbol id="minichevron-down-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-down-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) rotate(-90.000000) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-left-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-left-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-right-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) scale(-1, 1) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-right-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) scale(-1, 1) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-up-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="minichevron-up-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <polyline stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) " points="16 6 8 12 16 18"></polyline>
        </g>
    </symbol>
    <symbol id="move-horizontal" viewBox="0 0 24 12">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <circle fill="currentColor" cx="4" cy="3" r="1"></circle>
            <circle fill="currentColor" cx="8" cy="3" r="1"></circle>
            <circle fill="currentColor" cx="8" cy="6" r="1"></circle>
            <circle fill="currentColor" cx="8" cy="9" r="1"></circle>
            <circle fill="currentColor" cx="12" cy="6" r="1"></circle>
            <circle fill="currentColor" cx="12" cy="9" r="1"></circle>
            <circle fill="currentColor" cx="16" cy="6" r="1"></circle>
            <circle fill="currentColor" cx="16" cy="9" r="1"></circle>
            <circle fill="currentColor" cx="20" cy="6" r="1"></circle>
            <circle fill="currentColor" cx="20" cy="9" r="1"></circle>
            <circle fill="currentColor" cx="12" cy="3" r="1"></circle>
            <circle fill="currentColor" cx="16" cy="3" r="1"></circle>
            <circle fill="currentColor" cx="20" cy="3" r="1"></circle>
            <circle fill="currentColor" cx="4" cy="6" r="1"></circle>
            <circle fill="currentColor" cx="4" cy="9" r="1"></circle>
        </g>
    </symbol>
    <symbol id="move-vertical" viewBox="0 0 12 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <circle fill="currentColor" cx="3" cy="4" r="1"></circle>
            <circle fill="currentColor" cx="6" cy="4" r="1"></circle>
            <circle fill="currentColor" cx="6" cy="8" r="1"></circle>
            <circle fill="currentColor" cx="6" cy="12" r="1"></circle>
            <circle fill="currentColor" cx="3" cy="8" r="1"></circle>
            <circle fill="currentColor" cx="3" cy="12" r="1"></circle>
            <circle fill="currentColor" cx="3" cy="16" r="1"></circle>
            <circle fill="currentColor" cx="6" cy="16" r="1"></circle>
            <circle fill="currentColor" cx="9" cy="8" r="1"></circle>
            <circle fill="currentColor" cx="3" cy="20" r="1"></circle>
            <circle fill="currentColor" cx="9" cy="4" r="1"></circle>
            <circle fill="currentColor" cx="6" cy="20" r="1"></circle>
            <circle fill="currentColor" cx="9" cy="20" r="1"></circle>
            <circle fill="currentColor" cx="9" cy="16" r="1"></circle>
            <circle fill="currentColor" cx="9" cy="12" r="1"></circle>
        </g>
    </symbol>
    <symbol id="padlock" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <rect fill="currentColor" x="4" y="10" width="16" height="12"></rect>
            <circle stroke="currentColor" stroke-width="2" cx="12" cy="9.5" r="7"></circle>
        </g>
    </symbol>
    <symbol id="plus-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"></line>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) "></line>
        </g>
    </symbol>
    <symbol id="plus-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="4"></line>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="4" transform="translate(12.000000, 12.000000) rotate(90.000000) translate(-12.000000, -12.000000) "></line>
        </g>
    </symbol>
    <symbol id="plus-circle" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <circle stroke="currentColor" stroke-width="2" cx="12" cy="12" r="9"></circle>
            <line x1="12" y1="7" x2="12" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
            <line x1="7" y1="12" x2="17" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
        </g>
    </symbol>
    <symbol id="resize-bottom" viewBox="0 0 24 12">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="4" y1="8" x2="20" y2="8" stroke="currentColor" stroke-width="2"></line>
            <line x1="7" y1="5" x2="17" y2="5" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="resize-left" viewBox="0 0 12 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="4" y1="4" x2="4" y2="20" stroke="currentColor" stroke-width="2"></line>
            <line x1="7" y1="7" x2="7" y2="17" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="resize-right" viewBox="0 0 12 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="8" y1="4" x2="8" y2="20" stroke="currentColor" stroke-width="2"></line>
            <line x1="5" y1="7" x2="5" y2="17" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="resize-top" viewBox="0 0 24 12">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="4" y1="4" x2="20" y2="4" stroke="currentColor" stroke-width="2"></line>
            <line x1="7" y1="7" x2="17" y2="7" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="x-2" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="4.5" y1="4.5" x2="19.5" y2="19.5" stroke="currentColor" stroke-width="2"></line>
            <line x1="19.5" y1="4.5" x2="4.5" y2="19.5" stroke="currentColor" stroke-width="2"></line>
        </g>
    </symbol>
    <symbol id="x-4" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
            <line x1="4.5" y1="4.5" x2="19.5" y2="19.5" stroke="currentColor" stroke-width="4"></line>
            <line x1="19.5" y1="4.5" x2="4.5" y2="19.5" stroke="currentColor" stroke-width="4"></line>
        </g>
    </symbol>
    <symbol id="x-box" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <line x1="7" y1="7" x2="17" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
            <line x1="17" y1="7" x2="7" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
            <rect stroke="currentColor" stroke-width="2" x="3" y="3" width="18" height="18"></rect>
        </g>
    </symbol>
    <symbol id="x-circle" viewBox="0 0 24 24">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <line x1="8" y1="8" x2="16" y2="16" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
            <line x1="16" y1="8" x2="8" y2="16" stroke="currentColor" stroke-width="2" stroke-linecap="round"></line>
            <circle stroke="currentColor" stroke-width="2" cx="12" cy="12" r="9"></circle>
        </g>
    </symbol>
    <symbol id="checkmark-2" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="20 6 9 17 4 12"></polyline>
        </g>
    </symbol>
    <symbol id="checkmark-4" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="20 6 9 17 4 12"></polyline>
        </g>
    </symbol>
    <symbol id="threedots-v" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
        </g>
    </symbol>
    <symbol id="threedots-h" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cy="12" cx="12" r="1"></circle>
            <circle cy="12" cx="5" r="1"></circle>
            <circle cy="12" cx="19" r="1"></circle>
        </g>
    </symbol>
    <symbol id="edit" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <!-- Unchanged lines -->
            <line x1="14.5" y1="5.5" x2="18.5" y2="9.5"></line>
            <line x1="8.5" y1="19.5" x2="4.5" y2="15.5"></line>

            <!-- Joined path -->
            <path d="M 21.5 6.5 L 17.5 2.5 L 3.5 16.5 L 3.5 20.5 L 7.5 20.5 L 21.5 6.5"></path>
        </g>
    </symbol>
    <symbol id="calendar" viewBox="0 0 24 24">
        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
            <line x1="8"  y1="2" x2="8"  y2="6" />
            <line x1="16" y1="2" x2="16" y2="6" />
            <line x1="3"  y1="10" x2="21" y2="10" />
        </g>
        <g fill="currentColor" stroke="none">
            <circle cx="7.5"  cy="14.5" r="1.4" />
            <circle cx="12"   cy="14.5" r="1.4" />
            <circle cx="16.5" cy="14.5" r="1.4" />
            <circle cx="7.5"  cy="18"   r="1.4" />
            <circle cx="12"   cy="18"   r="1.4" />
            <circle cx="16.5" cy="18"   r="1.4" />
        </g>
    </symbol>
</svg>
