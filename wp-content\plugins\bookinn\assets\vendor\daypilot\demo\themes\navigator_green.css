﻿/* navigator styles */

.navigator_green_main 
{
	border-left: 1px solid #A0A0A0;
	border-right: 1px solid #A0A0A0;
	border-bottom: 1px solid #A0A0A0;
    background-color: white;
    color: #000000;
}

.navigator_green_month {
    font-family: Tahoma;
    font-size: 8pt;
    /*border: 1px solid black;*/
}
.navigator_green_day {
    color: black;
    text-align: center;
    /*background-color: white;*/
}
.navigator_green_weekend {
    background-color: #f0f0f0;
}
.navigator_green_dayheader {
    color: black;
    text-align: center;
}

.navigator_green_weeknumber {
	text-align: center;
}

.navigator_green_line 
{
	border-bottom: 1px solid #A0A0A0;
}

.navigator_green_dayother {
    color: gray;
}
.navigator_green_todaybox
{
	border: 1px solid red;
}

.navigator_green_select 
{
    background-color: #ddd;
}
.navigator_green_title, .navigator_green_titleleft, .navigator_green_titleright {
	text-align: center;
	border-top: 1px solid #A0A0A0;

	color: #ffffff;
	background: #666;
	background: -moz-linear-gradient(
		top,
		#777 0%,
		#666);
	background: -webkit-gradient(
		linear, left top, left bottom, 
		from(#777),
		to(#666));
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr="#777777", endColorStr="#666666");
	text-shadow:
		0px -1px 1px rgba(000,000,000,0.2),
		0px 1px 0px rgba(255,255,255,0.3);

}
.navigator_green_busy {
	font-weight: bold;
}
