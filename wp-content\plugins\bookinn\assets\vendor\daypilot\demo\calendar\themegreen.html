﻿<!DOCTYPE html>
<html>
<head>
    <title>Green CSS Theme (Open-Source JavaScript Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->

    <link href="../themes/calendar_green.css?v=2025.3.696" type="text/css" rel="stylesheet"/>

</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note"><b>Note:</b> You can create a theme using the online <strong>DayPilot Theme Designer</strong>: <a
        href="https://themes.daypilot.org/">https://themes.daypilot.org/</a></div>

    <div id="dp"></div>

    <div id="print"></div>

    <script type="text/javascript">

        const dp = new DayPilot.Calendar("dp", {
            theme: "calendar_green",
            viewType: "Week",
            startDate: "2023-03-20",
            showAllDayEvents: true,
            onTimeRangeSelected: function (args) {
                var name = prompt("New event name:", "Event");
                if (!name) return;
                var e = new DayPilot.Event({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: "Event"
                });
                dp.events.add(e);
                dp.clearSelection();
                console.log("Created");
            }
        });

        dp.init();

        const events = [
            {
                start: "2023-03-20T12:00:00",
                end: "2023-03-20T15:00:00",
                id: 1,
                text: "Event 1"
            },
            {
                start: "2023-03-21T11:00:00",
                end: "2023-03-21T16:00:00",
                id: 2,
                text: "Event 2"
            },
            {
                start: "2023-03-22T09:30:00",
                end: "2023-03-22T12:00:00",
                id: 3,
                text: "Event 3"
            },
            {
                start: "2023-03-22T13:00:00",
                end: "2023-03-22T15:30:00",
                id: 4,
                text: "Event 4"
            },
            {
                start: "2023-03-22T14:00:00",
                end: "2023-03-22T16:30:00",
                id: 5,
                text: "Event 5"
            },
        ];

        dp.update({events});

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

