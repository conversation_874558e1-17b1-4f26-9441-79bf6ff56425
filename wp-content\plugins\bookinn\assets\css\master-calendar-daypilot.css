/* Enhanced Master Calendar Container */
.bookinn-calendar-master-enhanced {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.04);
	margin-bottom: 24px;
	overflow: hidden;
	border: 1px solid #eee;
}

/* Calendar Header */
.bookinn-calendar-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 20px 24px;
	background: #fff;
	border-bottom: 1px solid #eee;
	flex-wrap: wrap;
	gap: 16px;
}

.bookinn-section-title h3 {
	margin: 0 0 4px 0;
	color: #f8f9fa;
	font-size: 18px;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 8px;
}

.bookinn-section-title p {
	margin: 0;
	color: #6c757d;
	font-size: 14px;
}

.bookinn-calendar-controls {
	display: flex;
	align-items: center;
	gap: 16px;
	flex-wrap: wrap;
}

.bookinn-calendar-filters {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

.bookinn-filter-group {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.bookinn-filter-group label {
	font-size: 11px;
	font-weight: 500;
	color: var(--bookinn-text-secondary, #4a5568);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.bookinn-date-input {
	padding: 6px 10px;
	border: 1px solid #ddd;
	border-radius: 2px;
	font-size: 12px;
	background: #fff;
	color: #222;
	min-width: 120px;
	box-shadow: none;
}

.bookinn-date-input:focus {
	outline: none;
	border-color: #888;
	box-shadow: none;
}

/* Status Legend */
.bookinn-calendar-legend {
	padding: 8px 12px;
	background: #fff;
	border-bottom: 1px solid #eee;
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;
}

.bookinn-legend-title {
	color: #222;
	font-size: 13px;
	font-weight: 500;
}

.bookinn-legend-items {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.bookinn-legend-item {
	display: flex;
	align-items: center;
	gap: 6px;
	cursor: pointer;
	padding: 2px 6px;
	border-radius: 2px;
	background: #fff;
	color: #222;
	border: 1px solid #eee;
	font-size: 13px;
	transition: background 0.2s;
}

.bookinn-legend-item:hover {
	background: #f5f5f5;
}

.bookinn-legend-item.inactive {
	opacity: 0.5;
}

.bookinn-legend-color {
	width: 12px;
	height: 12px;
	border-radius: 2px;
	border: 1px solid rgba(0,0,0,0.1);
}

.bookinn-legend-label {
	font-size: 11px;
	color: var(--bookinn-text-secondary, #4a5568);
	font-weight: 500;
}

/* Calendar Container */
.bookinn-calendar-master-container {
	background: #fff;
	padding: 0 0 12px 0;
	border-top: 1px solid #eee;
}

.bookinn-calendar-master-content {
	min-height: 600px;
	background: #fff;
	border-radius: 6px;
	box-shadow: none;
	border: 1px solid #eee;
	padding: 0;
	position: relative;
}

#bookinn-gstc-container {
	width: 100%;
	height: 600px;
	min-height: 400px;
	background: #fff;
	overflow: hidden;
}

/* Enhanced DayPilot Styles for BookInn */
.daypilot-scheduler-main {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	font-size: 13px;
	background: var(--bookinn-bg-primary, #ffffff);
	border: 1px solid var(--bookinn-border-light, #e2e8f0);
}

.daypilot-scheduler-rowheader {
	background: var(--bookinn-bg-secondary, #f8fafc);
	color: var(--bookinn-text-primary, #1a202c);
	font-weight: 500;
	border-right: 1px solid var(--bookinn-border-light, #e2e8f0);
}

.daypilot-scheduler-rowheader-inner {
	padding: 8px 12px;
}

/* Enhanced Room Header Display */
.bookinn-room-header {
	display: flex;
	flex-direction: column;
	gap: 2px;
	padding: 4px 0;
}

.bookinn-room-name {
	font-weight: 600;
	color: #000305;
	font-size: 14px;
}

.bookinn-room-details {
	font-size: 12px;
	color: var(--bookinn-text-muted, #718096);
	font-weight: 400;
}

/* Room Type Column Styling */
.bookinn-room-type {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 4px;
	height: 100%;
}

.bookinn-room-type-label {
	background: var(--bookinn-bg-tertiary, #f1f5f9);
	color: var(--bookinn-text-secondary, #4a5568);
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 11px;
	font-weight: 500;
	text-align: center;
	border: 1px solid var(--bookinn-border-light, #e2e8f0);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100px;
}

/* Enhanced Event Bars */
.daypilot-scheduler-event-bar {
	border-radius: 6px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	border: 1px solid transparent;
	font-weight: 500;
	font-size: 13px;
	padding: 2px 8px;
	transition: all 0.2s ease;
}

.daypilot-scheduler-event-bar:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Time Headers */
.daypilot-scheduler-timeheader {
	background: var(--bookinn-bg-secondary, #f8fafc);
	border-bottom: 1px solid var(--bookinn-border-light, #e2e8f0);
	font-weight: 600;
	color: var(--bookinn-text-secondary, #4a5568);
	font-size: 12px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.daypilot-scheduler-timeheader-cell {
	border-right: 1px solid var(--bookinn-border-light, #e2e8f0);
}

/* Cell Styling */
.daypilot-scheduler-cell {
	border-right: 1px solid var(--bookinn-border-light, #e2e8f0);
	border-bottom: 1px solid var(--bookinn-border-light, #e2e8f0);
	background: var(--bookinn-bg-primary, #ffffff);
}

.daypilot-scheduler-cell:hover {
	background-color: var(--bookinn-bg-secondary, #f8fafc);
}

/* Weekend cells */
.daypilot-scheduler-cell[data-weekend="true"] {
	background-color: #f8f9fa;
}

/* Booking Modal Styles */
.bookinn-booking-modal-content {
	padding: 20px;
	max-width: 500px;
}

.bookinn-booking-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 1px solid #e9ecef;
}

.bookinn-booking-header h4 {
	margin: 0;
	color: #f1f5f9;
	font-size: 18px;
	font-weight: 600;
}

.bookinn-status-badge {
	display: inline-block;
	padding: 4px 12px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.bookinn-booking-details {
	margin-bottom: 20px;
}

.bookinn-detail-row {
	display: flex;
	justify-content: space-between;
	padding: 8px 0;
	border-bottom: 1px solid #f1f3f4;
}

.bookinn-detail-row:last-child {
	border-bottom: none;
}

.bookinn-booking-actions {
	display: flex;
	gap: 12px;
	justify-content: flex-end;
	padding-top: 15px;
	border-top: 1px solid #e9ecef;
}

/* Booking Tooltip */
.bookinn-booking-tooltip {
	background: #f1f5f9;
	color: #fff;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	line-height: 1.4;
	max-width: 200px;
}

/* Button Styles - BookInn Pattern */
.bookinn-btn {
	display: inline-flex;
	align-items: center;
	gap: 6px;
	padding: 6px 12px;
	border: 1px solid transparent;
	border-radius: 4px;
	font-size: 13px;
	font-weight: 500;
	text-decoration: none;
	cursor: pointer;
	transition: all 0.2s ease;
	background: none;
}

.bookinn-btn-primary {
	background-color: var(--bookinn-primary, #1e40af);
	border-color: var(--bookinn-primary, #1e40af);
	color: var(--bookinn-text-white, #ffffff);
}

.bookinn-btn-primary:hover {
	background-color: var(--bookinn-primary-hover, #1d4ed8);
	border-color: var(--bookinn-primary-hover, #1d4ed8);
	transform: translateY(-1px);
}

.bookinn-btn-secondary {
	background-color: var(--bookinn-secondary, #64748b);
	border-color: var(--bookinn-secondary, #64748b);
	color: var(--bookinn-text-white, #ffffff);
}

.bookinn-btn-secondary:hover {
	background-color: var(--bookinn-gray-600, #475569);
	border-color: var(--bookinn-gray-600, #475569);
	transform: translateY(-1px);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
	.bookinn-calendar-header {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.bookinn-calendar-controls {
		justify-content: center;
	}

	.bookinn-legend-items {
		justify-content: center;
	}
}

@media (max-width: 900px) {
	#bookinn-gstc-container {
		height: 500px;
	}

	.bookinn-calendar-filters {
		flex-direction: column;
		align-items: stretch;
		gap: 12px;
	}

	.bookinn-filter-group {
		flex-direction: row;
		align-items: center;
		gap: 8px;
	}

	.bookinn-filter-group label {
		min-width: 80px;
		margin-bottom: 0;
	}

	.bookinn-date-input {
		flex: 1;
		min-width: 120px;
	}

	.bookinn-calendar-legend {
		flex-direction: column;
		align-items: stretch;
		gap: 12px;
	}

	.bookinn-legend-items {
		justify-content: flex-start;
	}

	/* Adjust room columns for mobile */
	.daypilot-scheduler-main {
		font-size: 12px;
	}

	.bookinn-room-type-label {
		font-size: 10px;
		padding: 2px 4px;
		max-width: 80px;
	}
}

@media (max-width: 600px) {
	.bookinn-calendar-master-enhanced {
		margin: 0 -10px;
		border-radius: 0;
	}

	.bookinn-calendar-header {
		padding: 16px;
	}

	.bookinn-calendar-legend {
		padding: 12px 16px;
	}

	#bookinn-gstc-container {
		height: 400px;
	}

	.bookinn-section-title h3 {
		font-size: 16px;
	}

	.bookinn-legend-items {
		gap: 8px;
	}

	.bookinn-legend-item {
		font-size: 12px;
		padding: 3px 6px;
	}

	.bookinn-legend-color {
		width: 10px;
		height: 10px;
	}

	/* Mobile booking modal */
	.bookinn-booking-modal-content {
		padding: 16px;
	}

	.bookinn-booking-actions {
		flex-direction: column;
		gap: 8px;
	}

	.bookinn-btn {
		justify-content: center;
		padding: 10px 16px;
	}
}

@media (max-width: 480px) {
	#bookinn-gstc-container {
		height: 350px;
	}

	.bookinn-calendar-header {
		padding: 12px;
	}

	.bookinn-calendar-legend {
		padding: 8px 12px;
	}

	.bookinn-filter-group {
		flex-direction: column;
		align-items: stretch;
	}

	.bookinn-filter-group label {
		min-width: auto;
		margin-bottom: 4px;
	}
}

/* Print Styles */
@media print {
	.bookinn-calendar-header,
	.bookinn-calendar-legend {
		background: #fff !important;
		border: 1px solid #000;
	}

	.bookinn-btn {
		display: none;
	}

	#bookinn-gstc-container {
		height: auto !important;
		min-height: 600px;
	}

	.daypilot-scheduler-event-bar {
		box-shadow: none !important;
		border: 1px solid #000 !important;
	}
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
	.bookinn-calendar-master-enhanced {
		background: #f1f5f9;
		color: #07090a;
	}

	.bookinn-calendar-header {
		background: #f8f9fa;
		border-color: #4a5f7a;
	}

	.bookinn-section-title h3 {
		color: #ecf0f1;
	}

	.bookinn-section-title p {
		color: #bdc3c7;
	}

	.bookinn-date-input {
		background: #ffffff;
		border-color: #4a5f7a;
		color: #000000;
	}

	.bookinn-calendar-legend {
		background: #fdfeff;
		border-color: #4a5f7a;
	}

	.daypilot-scheduler-main {
		background: #f1f5f9;
		border-color: #4a5f7a;
	}

	.daypilot-scheduler-rowheader {
		background: #ffffff;
		color: #0f1111;
	}
}
