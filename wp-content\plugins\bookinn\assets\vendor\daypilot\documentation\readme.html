﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <style type="text/css">
        p, body, td {
            font-family: Tahoma, Arial, Helvetica, sans-serif;
            font-size: 10pt;
        }

        body {
            padding: 0px;
            margin: 0px;
            background-color: #ffffff;
        }

        a {
            color: #1155a3;
        }

        .space {
            margin: 10px 0px 10px 0px;
        }

        .header {
            background: #003267;
            background: linear-gradient(to right, #011329 0%, #00639e 44%, #011329 100%);
            padding: 20px 10px;
            width: 100%;
            color: white;
            box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.75);
        }

        .header a {
            color: white;
        }

        .header h1 a {
            text-decoration: none;
        }

        .header h1 {
            padding: 0px;
            margin: 0px;
        }

        .main {
            padding: 10px
        }
    </style>
    <title>DayPilot Lite for JavaScript (Open-Source)</title>
</head>
<body>
<div class="header">
    <h1><a href='https://javascript.daypilot.org/open-source/'>DayPilot Lite for JavaScript (Open-Source)</a></h1>
    <div><a href="https://javascript.daypilot.org/">DayPilot for JavaScript</a> - HTML5 Calendar/Scheduling Components for JavaScript/Angular/React/Vue
    </div>
</div>

<div class="main">
    <h3>
        Links
    </h3>
    <ul>
        <li><a href="https://doc.daypilot.org/">Documentation</a></li>
        <li><a href="https://javascript.daypilot.org/demo/lite/">Demo</a></li>
        <li><a href="https://javascript.daypilot.org/download/">Download</a></li>
    </ul>
</div>
</body>
</html>
