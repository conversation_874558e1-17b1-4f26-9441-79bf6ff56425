﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
"undefined"==typeof DayPilot&&(DayPilot={}),function(DayPilot){"use strict";function e(t,a,i){var n=a.indexOf(".");if(n===-1)return void("__proto__"!==a&&"constructor"!==a&&(t[a]=i));var o=a.substring(0,n);if("__proto__"!==o&&"constructor"!==o){var l=a.substring(n+1),d=t[o];"object"==typeof d&&null!==d||(t[o]={},d=t[o]),e(d,l,i)}}function t(e,a,i){a=a||{},i=i||"";for(var n in e){var o=e[n];"object"==typeof o?"[object Array]"===Object.prototype.toString.call(o)?a[i+n]=o:o&&o.toJSON?a[i+n]=o.toJSON():t(o,a,i+n+"."):a[i+n]=o}return a}if(!DayPilot.ModalStatic){DayPilot.ModalStatic={},DayPilot.ModalStatic.list=[],DayPilot.ModalStatic.hide=function(){if(this.list.length>0){var e=this.list.pop();e&&e.hide()}},DayPilot.ModalStatic.remove=function(e){for(var t=DayPilot.ModalStatic.list,a=0;a<t.length;a++)if(t[a]===e)return void t.splice(a,1)},DayPilot.ModalStatic.close=function(e){DayPilot.ModalStatic.result(e),DayPilot.ModalStatic.hide()},DayPilot.ModalStatic.result=function(e){var t=DayPilot.ModalStatic.list;t.length>0&&(t[t.length-1].result=e)},DayPilot.ModalStatic.displayed=function(e){for(var t=DayPilot.ModalStatic.list,a=0;a<t.length;a++)if(t[a]===e)return!0;return!1},DayPilot.ModalStatic.stretch=function(){if(this.list.length>0){var e=this.list[this.list.length-1];e&&e.stretch()}},DayPilot.ModalStatic.last=function(){var e=DayPilot.ModalStatic.list;return e.length>0?e[e.length-1]:null};var a=function(){function e(){for(var e=document.querySelectorAll("style[nonce]"),t=0;t<e.length;t++){var a=e[t];if(a.nonce)return a.nonce}if(document.currentScript&&document.currentScript.nonce)return document.currentScript.nonce;for(var i=document.querySelectorAll("script[nonce]"),n=0;n<i.length;n++){var o=i[n];if(o.nonce)return o.nonce}return""}if("undefined"==typeof window){var t={};return t.add=function(){},t.commit=function(){},t}var a=document.createElement("style");a.nonce=e(),a.styleSheet||a.appendChild(document.createTextNode("")),(document.head||document.getElementsByTagName("head")[0]).appendChild(a);var i=!!a.styleSheet,t={};return t.rules=[],t.commit=function(){try{i&&(a.styleSheet.cssText=this.rules.join("\n"))}catch(e){}},t.add=function(e,t,n){if(i)return void this.rules.push(e+"{"+t+"}");if(a.sheet.insertRule)"undefined"==typeof n&&(n=a.sheet.cssRules.length),a.sheet.insertRule(e+"{"+t+"}",n);else{if(!a.sheet.addRule)throw"No CSS registration method found";a.sheet.addRule(e,t,n)}},t},i="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogICB3aWR0aD0iMTAiCiAgIGhlaWdodD0iMTUiCj4KICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLDUpIj4KICAgIDxwYXRoCiAgICAgICBpZD0icGF0aDMxNzMiCiAgICAgICBzdHlsZT0iZmlsbDpub25lO3N0cm9rZTojOTk5OTk5O3N0cm9rZS13aWR0aDoxLjg1MTk2ODUzO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZSIKICAgICAgIGQ9Ik0gMC45NTQxNDgzOCwwLjY4MTYwMzEgNS4wMzkwNjI1LDUuNDExNTM4NiA5LjEyMzk3NjYsMC42ODE2MDMxIgogICAgICAgIC8+CiAgPC9nPgo8L3N2Zz4K",n=new a;n.add(".modal_default_main","border: 10px solid #ccc; max-width: 90%;"),n.add(".modal_default_main:focus","outline: none;"),n.add(".modal_default_content","padding: 10px 0px;"),n.add(".modal_default_inner","padding: 20px;"),n.add(".modal_default_input","padding: 10px 0px;"),n.add(".modal_default_buttons","margin-top: 10px;"),n.add(".modal_default_buttons","padding: 10px 0px;"),n.add(".modal_default_form_item","padding: 10px 0px; position: relative;"),n.add(".modal_default_form_item_level1","border-left: 2px solid #ccc; margin-left: 10px; padding-left: 20px;"),n.add(".modal_default_form_item.modal_default_form_title","font-size: 1.5rem; font-weight: bold;"),n.add(".modal_default_form_item input[type=text]","width: 100%; box-sizing: border-box;"),n.add(".modal_default_form_item textarea","width: 100%; height: 200px; box-sizing: border-box;"),n.add(".modal_default_form_item input[type=select]","width: 100%; box-sizing: border-box;"),n.add(".modal_default_form_item label","display: block;"),n.add(".modal_default_form_item select","width: 100%; box-sizing: border-box;"),n.add(".modal_default_form_item_label","margin: 2px 0px;"),n.add(".modal_default_form_item_image img","max-width: 100%; height: auto;"),n.add(".modal_default_form_item_invalid",""),n.add(".modal_default_form_item_invalid_message","position: absolute; right: 0px; top: 9px; background-color: red; color: #ffffff; padding: 2px; border-radius: 2px;"),n.add(".modal_default_background","opacity: 0.5; background-color: #000;"),n.add(".modal_default_ok","padding: 3px; width: 80px;"),n.add(".modal_default_cancel","padding: 3px; width: 80px;"),n.add(".modal_default_form_item_date","position: relative;"),n.add(".modal_default_form_item_date:after","content: ''; position: absolute; right: 7px; top: 50%; margin-top: 3px; width: 10px; height: 15px; background-image:url("+"data:image/svg+xml;base64,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"+")"),"undefined"!=typeof navigator&&navigator.userAgent.indexOf("Edge")!==-1&&n.add(".modal_default_form_item_date input::-ms-clear","display: none;"),n.add(".modal_default_form_item_scrollable_scroll","width: 100%; height: 200px; box-sizing: border-box; border: 1px solid #ccc; overflow-y: auto;"),n.add(".modal_default_form_item_scrollable_scroll_content","padding: 5px;"),n.add(".modal_default_form_item_searchable","position: relative;"),n.add(".modal_default_form_item_searchable_icon",""),n.add(".modal_default_form_item_searchable_icon:after","content:''; position: absolute; right: 5px; top: 50%; margin-top: -8px; width: 10px; height: 15px; background-image:url("+i+");"),n.add(".modal_default_form_item_searchable_list","box-sizing: border-box; border: 1px solid #999; max-height: 150px; overflow-y: auto;"),n.add(".modal_default_form_item_searchable_list_item","background: white; padding: 2px; cursor: default;"),n.add(".modal_default_form_item_searchable_list_item_highlight","background: #ccc;"),n.add(".modal_default_form_item_time","position: relative;"),n.add(".modal_default_form_item_time_icon",""),n.add(".modal_default_form_item_time_icon:after","content:''; position: absolute; right: 5px; top: 50%; margin-top: -8px; width: 10px; height: 15px; background-image:url("+i+");"),n.add(".modal_default_form_item_time_list","box-sizing: border-box; border: 1px solid #999; max-height: 150px; overflow-y: auto;"),n.add(".modal_default_form_item_time_list_item","background: white; padding: 2px; cursor: default;"),n.add(".modal_default_form_item_time_list_item_highlight","background: #ccc;"),n.add(".modal_default_form_item_datetime_parent","display: flex;"),n.add(".modal_default_form_item_datetime .modal_default_form_item_time_main","margin-left: 5px;"),n.add(".modal_default_form_item_datetime input[type='text'].modal_default_input_date ",""),n.add(".modal_default_form_item_tabular_main","margin-top: 10px;"),n.add(".modal_default_form_item_tabular_table","display: table; width: 100%; xbackground-color: #fff; border-collapse: collapse;"),n.add(".modal_default_form_item_tabular_tbody","display: table-row-group;"),n.add(".modal_default_form_item_tabular_row","display: table-row;"),n.add(".modal_default_form_item_tabular_row.modal_default_form_item_tabular_header",""),n.add(".modal_default_form_item_tabular_cell.modal_default_form_item_tabular_rowaction","padding: 0px; width: 23px;"),n.add(".modal_default_form_item_tabular_cell","display: table-cell; border: 0px; padding: 2px 2px 2px 0px; cursor: default; vertical-align: bottom;"),n.add(".modal_default_form_item_tabular_header .modal_default_form_item_tabular_cell","padding-left: 0px; padding-bottom: 0px;"),n.add(".modal_default_form_item_tabular_table input[type=text], .modal_default_form_item_tabular_table input[type=number]","width:100%; box-sizing: border-box;"),n.add(".modal_default_form_item_tabular_table select","width:100%; height:100%; box-sizing: border-box;"),n.add(".modal_default_form_item_tabular_plus","display: inline-block; background-color: #ccc; color: white; width: 20px; height: 20px; border-radius: 10px; box-sizing: border-box; position: relative; margin-left: 3px; margin-top: 3px; cursor: pointer;"),n.add(".modal_default_form_item_tabular_plus:after","content: ''; position: absolute; left: 5px; top: 5px; width: 10px; height: 10px;   background-image: url(\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSA1LjAgMC41IEwgNS4wIDkuNSBNIDAuNSA1LjAgTCA5LjUgNS4wJyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojZmZmZmZmO3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==\")"),n.add(".modal_default_form_item_tabular_delete","display: inline-block; background-color: #ccc; color: white; width: 20px; height: 20px; border-radius: 10px; box-sizing: border-box; position: relative; margin-left: 3px; margin-top: 3px; cursor: pointer;"),n.add(".modal_default_form_item_tabular_delete:after","content: ''; position: absolute; left: 5px; top: 5px; width: 10px; height: 10px;   background-image: url(\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMC41IEwgOS41IDkuNSBNIDAuNSA5LjUgTCA5LjUgMC41JyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojZmZmZmZmO3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==\")"),n.add(".modal_default_form_item_tabular_disabled .modal_default_form_item_tabular_plus","display: none;"),n.add(".modal_default_form_item_tabular_plus_max.modal_default_form_item_tabular_plus","display: none;"),n.add(".modal_default_form_item_tabular_disabled .modal_default_form_item_tabular_delete","visibility: hidden;"),n.add(".modal_default_form_item_tabular_empty","height: 1px; margin: 5px 23px 5px 0px; background-color: #ccc;"),n.add(".modal_default_form_item_tabular_spacer .modal_default_form_item_tabular_cell","padding: 0px;"),n.add(".modal_min_main","border: 1px solid #ccc; max-width: 90%;"),n.add(".modal_min_background","opacity: 0.5; background-color: #000;"),n.add(".modal_min_ok","padding: 3px 10px;"),n.add(".modal_min_cancel","padding: 3px 10px;"),n.add(".navigator_modal_main","border-left: 1px solid #c0c0c0;border-right: 1px solid #c0c0c0;border-bottom: 1px solid #c0c0c0;background-color: white;color: #000000; box-sizing: content-box;"),n.add(".navigator_modal_main *, .navigator_modal_main *:before, .navigator_modal_main *:after","box-sizing: content-box;"),n.add(".navigator_modal_month","font-size: 11px;"),n.add(".navigator_modal_day","color: black;"),n.add(".navigator_modal_weekend","background-color: #f0f0f0;"),n.add(".navigator_modal_dayheader","color: black;"),n.add(".navigator_modal_line","border-bottom: 1px solid #c0c0c0;"),n.add(".navigator_modal_dayother","color: gray;"),n.add(".navigator_modal_todaybox","border: 1px solid red;"),n.add(".navigator_modal_title, .navigator_modal_titleleft, .navigator_modal_titleright","border-top: 1px solid #c0c0c0;border-bottom: 1px solid #c0c0c0;color: #333;background: #f3f3f3;"),n.add(".navigator_modal_busy","font-weight: bold;"),n.add(".navigator_modal_cell","text-align: center;"),n.add(".navigator_modal_select .navigator_modal_cell_box","background-color: #FFE794; opacity: 0.5;"),n.add(".navigator_modal_title","text-align: center;"),n.add(".navigator_modal_titleleft, .navigator_modal_titleright","text-align: center;"),n.add(".navigator_modal_dayheader","text-align: center;"),n.add(".navigator_modal_weeknumber","text-align: center;"),n.add(".navigator_modal_cell_text","cursor: pointer;"),n.add(".navigator_modal_todaysection","box-sizing: border-box; display: flex; align-items: center; justify-content: center; border-top: 1px solid var(--dp-nav-border-color);"),n.add(".navigator_modal_todaysection_button","cursor: pointer; color: #333; background-color: #f0f0f0; border: 1px solid var(--dp-nav-border-color); padding: 5px 10px; border-radius: 0px; "),n.commit(),DayPilot.Modal=function(e){this.autoFocus=!0,this.focus=null,this.autoStretch=!0,this.autoStretchFirstLoadOnly=!1,this.className=null,this.theme="modal_default",this.disposeOnClose=!0,this.dragDrop=!0,this.loadingHtml=null,this.maxHeight=null,this.scrollWithPage=!0,this.useIframe=!0,this.zIndex=99999,this.left=null,this.width=600,this.top=20,this.height=200,this.locale=null,this.closed=null,this.onClose=null,this.onClosed=null,this.onShow=null;var t=this;this.id="_"+(new Date).getTime()+"n"+10*Math.random(),this.a=!1,this.b=null,this.c=null,this.showHtml=function(e){if(DayPilot.ModalStatic.displayed(this))throw"This modal dialog is already displayed.";if(this.div||this.d(),this.e(),this.useIframe){var t=function(e,t){return function(){e.setInnerHTML(e.id+"iframe",t)}};window.setTimeout(t(this,e),0)}else e.nodeType?this.div.appendChild(e):this.div.innerHTML=e;this.e(),this.f(),this.g()},this.showUrl=function(e){if(DayPilot.ModalStatic.displayed(this))throw"This modal dialog is already displayed.";if(this.useIframe){this.div||this.d();var a=this.loadingHtml;a&&(this.iframe.src="about:blank",this.setInnerHTML(this.id+"iframe",a)),this.re(this.iframe,"load",this.h),this.iframe.src=e,this.e(),this.f(),this.g()}else t.i({"url":e,"success":function(e){var a=e.request.responseText;t.showHtml(a)},"error":function(e){t.showHtml("Error loading the modal dialog")}})},this.g=function(){if("function"==typeof t.onShow){var e={};e.root=t.j(),e.modal=t,t.onShow(e)}},this.j=function(){return t.iframe?t.iframe.contentWindow.document:t.div},this.i=function(e){var t=new XMLHttpRequest;if(t){var a=e.method||"GET",i=e.success||function(){},n=e.error||function(){},o=e.data,l=e.url;t.open(a,l,!0),t.setRequestHeader("Content-type","text/plain"),t.onreadystatechange=function(){if(4===t.readyState)if(200===t.status||304===t.status){var e={};e.request=t,i(e)}else if(n){var e={};e.request=t,n(e)}else window.console&&console.log("HTTP error "+t.status)},4!==t.readyState&&("object"==typeof o&&(o=JSON.stringify(o)),t.send(o))}},this.e=function(){delete this.result;var e=window,a=document,i=e.pageYOffset?e.pageYOffset:a.documentElement&&a.documentElement.scrollTop?a.documentElement.scrollTop:a.body.scrollTop;this.theme&&(this.hideDiv.className=this.theme+"_background"),this.zIndex&&(this.hideDiv.style.zIndex=this.zIndex),this.hideDiv.style.display="",window.setTimeout(function(){t.hideDiv&&(t.hideDiv.onclick=function(){t.hide({"backgroundClick":!0})})},500),this.theme?this.div.className=this.theme+"_main":this.div.className="",this.className&&(this.div.className+=" "+this.className),this.left?this.div.style.left=this.left+"px":this.div.style.marginLeft="-"+Math.floor(this.width/2)+"px",this.div.style.position="absolute",this.div.style.boxSizing="content-box",this.div.style.top=i+this.top+"px",this.div.style.width=this.width+"px",this.zIndex&&(this.div.style.zIndex=this.zIndex),this.height&&(this.useIframe||!this.autoStretch?this.div.style.height=this.height+"px":this.div.style.height=""),this.useIframe&&this.height&&(this.iframe.style.height=this.height+"px"),this.div.style.display="",this.l(),DayPilot.ModalStatic.remove(this),DayPilot.ModalStatic.list.push(this)},this.h=function(){t.iframe.contentWindow.modal=t,t.autoStretch&&t.stretch()},this.stretch=function(){var e=function(){return t.k().y},a=function(){return t.k().x};if(this.useIframe){for(var i=a()-40,n=this.width;n<i&&this.m();n+=10)this.div.style.width=n+"px",this.div.style.marginLeft="-"+Math.floor(n/2)+"px";for(var o=this.maxHeight||e()-2*this.top,l=this.height;l<o&&this.n();l+=10)this.iframe.style.height=l+"px",this.div.style.height=l+"px";this.autoStretchFirstLoadOnly&&this.ue(this.iframe,"load",this.h)}else this.div.style.height=""},this.m=function(){for(var e=this.iframe.contentWindow.document,t="BackCompat"===e.compatMode?e.body:e.documentElement,a=t.scrollWidth,i=e.body.children,n=0;n<i.length;n++){var o=i[n].offsetLeft+i[n].offsetWidth;a=Math.max(a,o)}return a>t.clientWidth},this.n=function(){for(var e=this.iframe.contentWindow.document,t="BackCompat"===e.compatMode?e.body:e.documentElement,a=t.scrollHeight,i=e.body.children,n=0;n<i.length;n++){var o=i[n].offsetTop+i[n].offsetHeight;a=Math.max(a,o)}return a>t.clientHeight},this.k=function(){var e=document;if("CSS1Compat"===e.compatMode&&e.documentElement&&e.documentElement.clientWidth){var t=e.documentElement.clientWidth,a=e.documentElement.clientHeight;return{x:t,y:a}}var t=e.body.clientWidth,a=e.body.clientHeight;return{x:t,y:a}},this.f=function(){this.a||(this.re(window,"resize",this.o),this.re(window,"scroll",this.p),this.dragDrop&&(this.re(document,"mousemove",this.q),this.re(document,"mouseup",this.r)),this.a=!0)},this.s=function(){this.ue(window,"resize",this.o),this.ue(window,"scroll",this.p),this.dragDrop&&(this.ue(document,"mousemove",this.q),this.ue(document,"mouseup",this.r)),this.a=!1},this.t=function(e){e.target===t.div&&(e.preventDefault(),t.div.style.cursor="move",t.u(),t.c=t.mc(e||window.event),t.b={x:t.div.offsetLeft,y:t.div.offsetTop})},this.q=function(e){if(t.c){var e=e||window.event,a=t.mc(e),i=a.x-t.c.x,n=a.y-t.c.y;t.div.style.marginLeft="0px",t.div.style.top=t.b.y+n+"px",t.div.style.left=t.b.x+i+"px"}},this.r=function(e){t.c&&(t.v(),t.div.style.cursor=null,t.c=null)},this.u=function(){if(this.useIframe){var e=document.createElement("div");e.style.backgroundColor="#ffffff",e.style.filter="alpha(opacity=80)",e.style.opacity="0.80",e.style.width="100%",e.style.height=this.height+"px",e.style.position="absolute",e.style.left="0px",e.style.top="0px",this.div.appendChild(e),this.mask=e}},this.v=function(){this.useIframe&&(this.div.removeChild(this.mask),this.mask=null)},this.o=function(){t.w(),t.l()},this.p=function(){t.w()},this.l=function(){if(!t.left&&t.div){var e=t.div.offsetWidth;t.div.style.marginLeft="-"+Math.floor(e/2)+"px"}},this.w=function(){if(t.hideDiv&&t.div&&"none"!==t.hideDiv.style.display&&"none"!==t.div.style.display){var e=t.z.scrollY();t.scrollWithPage||(t.div.style.top=e+t.top+"px")}},this.z={},this.z.container=function(){return t.container||document.body},this.z.scrollY=function(){var e=t.z.container();return e===document.body?window.pageYOffset?window.pageYOffset:document.documentElement&&document.documentElement.scrollTop?document.documentElement.scrollTop:document.body.scrollTop:e.scrollTop},this.re=function(e,t,a){e.addEventListener?e.addEventListener(t,a,!1):e.attachEvent&&e.attachEvent("on"+t,a)},this.ue=function(e,t,a){e.removeEventListener?e.removeEventListener(t,a,!1):e.detachEvent&&e.detachEvent("on"+t,a)},this.mc=function(e){return e.pageX||e.pageY?{x:e.pageX,y:e.pageY}:{x:e.clientX+document.documentElement.scrollLeft,y:e.clientY+document.documentElement.scrollTop}},this.abs=function(e){for(var t={x:e.offsetLeft,y:e.offsetTop};e.offsetParent;)e=e.offsetParent,t.x+=e.offsetLeft,t.y+=e.offsetTop;return t},this.d=function(){var e=t.z.container(),a=e===document.body,i=a?"fixed":"absolute",n=document.createElement("div");n.id=this.id+"hide",n.style.position=i,n.style.left="0px",n.style.top="0px",n.style.right="0px",n.style.bottom="0px",n.oncontextmenu=function(){return!1},n.onmousedown=function(){return!1},e.appendChild(n);var o=document.createElement("div");o.id=this.id+"popup",o.style.position=i,o.style.left="50%",o.style.top="0px",o.style.backgroundColor="white",o.style.width="50px",o.style.height="50px",this.dragDrop&&(o.onmousedown=this.t),o.addEventListener("keydown",function(e){e.stopPropagation()});var l=null;this.useIframe&&(l=document.createElement("iframe"),l.id=this.id+"iframe",l.name=this.id+"iframe",l.frameBorder="0",l.style.width="100%",l.style.height="50px",o.appendChild(l)),e.appendChild(o),this.div=o,this.iframe=l,this.hideDiv=n},this.setInnerHTML=function(e,a){var i=window.frames[e],n=i.contentWindow||i.document||i.contentDocument;n.document&&(n=n.document),null==n.body&&n.write("<body></body>"),a.nodeType?n.body.appendChild(a):n.body.innerHTML=a,t.autoStretch&&(t.autoStretchFirstLoadOnly&&t.A||(t.stretch(),t.A=!0))},this.close=function(e){this.result=e,this.hide()},this.closeSerialized=function(){for(var e=t.j(),a=e.querySelectorAll("input, textarea, select"),i={},n=0;n<a.length;n++){var o=a[n],l=o.name;if(l){var d=o.value;i[l]=d}}t.close(i)},this.hide=function(e){e=e||{};var a={};a.backgroundClick=!!e.backgroundClick,a.result=this.result,a.canceled="undefined"==typeof this.result,a.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof this.onClose&&(this.onClose(a),a.preventDefault.value)||(this.div&&(this.div.style.display="none",this.hideDiv.style.display="none",this.useIframe||(this.div.innerHTML=null)),window.focus(),DayPilot.ModalStatic.remove(this),"function"==typeof this.onClosed?this.onClosed(a):this.closed&&this.closed(),delete this.result,this.disposeOnClose&&(t.s(),t.B(t.div),t.B(t.hideDiv),t.div=null,t.hideDiv=null,t.iframe=null))},this.B=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},this.C=function(){if(e)for(var t in e)this[t]=e[t]},this.C()},DayPilot.Modal.alert=function(e,t){t=t||{},t.height=t.height||40,t.useIframe=!1;var a=t.okText||"OK";t.cancelText||"Cancel";return DayPilot.getPromise(function(i,n){t.onClosed=function(e){i(e)};var o=new DayPilot.Modal(t),l=document.createElement("div");l.className=o.theme+"_inner";var d=document.createElement("div");d.className=o.theme+"_content",d.innerHTML=e;var r=document.createElement("div");r.className=o.theme+"_buttons";var s=document.createElement("button");s.innerText=a,s.className=o.theme+"_ok",s.onclick=function(e){DayPilot.ModalStatic.close("OK")},r.appendChild(s),l.appendChild(d),l.appendChild(r),o.showHtml(l),o.autoFocus&&s.focus()})},DayPilot.Modal.confirm=function(e,t){t=t||{},t.height=t.height||40,t.useIframe=!1;var a=t.okText||"OK",i=t.cancelText||"Cancel";return DayPilot.getPromise(function(n,o){t.onClosed=function(e){n(e)};var l=new DayPilot.Modal(t),d=document.createElement("div");d.className=l.theme+"_inner";var r=document.createElement("div");r.className=l.theme+"_content",r.innerHTML=e;var s=document.createElement("div");s.className=l.theme+"_buttons";var c=document.createElement("button");c.innerText=a,c.className=l.theme+"_ok",c.onclick=function(e){DayPilot.ModalStatic.close("OK")};var u=document.createTextNode(" "),m=document.createElement("button");m.innerText=i,m.className=l.theme+"_cancel",m.onclick=function(e){DayPilot.ModalStatic.close()},s.appendChild(c),s.appendChild(u),s.appendChild(m),d.appendChild(r),d.appendChild(s),l.showHtml(d),l.autoFocus&&c.focus()})},DayPilot.Modal.prompt=function(e,t,a){"object"==typeof t&&(a=t,t=""),a=a||{},a.height=a.height||40,a.useIframe=!1;var i=a.okText||"OK",n=a.cancelText||"Cancel",o=t||"";return DayPilot.getPromise(function(t,l){a.onClosed=function(e){t(e)};var d=new DayPilot.Modal(a),r=document.createElement("div");r.className=d.theme+"_inner";var s=document.createElement("div");s.className=d.theme+"_content",s.innerHTML=e;var c=document.createElement("div");c.className=d.theme+"_input";var u=document.createElement("input");u.value=o,u.style.width="100%",u.onkeydown=function(e){var t=!1;switch(e.keyCode){case 13:d.close(this.value);break;case 27:d.close();break;default:t=!0}t||(e.preventDefault(),e.stopPropagation())},c.appendChild(u);var m=document.createElement("div");m.className=d.theme+"_buttons";var h=document.createElement("button");h.innerText=i,h.className=d.theme+"_ok",h.onclick=function(e){d.close(u.value)};var p=document.createTextNode(" "),f=document.createElement("button");f.innerText=n,f.className=d.theme+"_cancel",f.onclick=function(e){d.close()},m.appendChild(h),m.appendChild(p),m.appendChild(f),r.appendChild(s),r.appendChild(c),r.appendChild(m),d.showHtml(r),d.autoFocus&&u.focus()})};var o=function(e){return"[object Array]"===Object.prototype.toString.call(e)};DayPilot.Modal.form=function(t,a,i){if(1===arguments.length){var n=t;if(o(n))a={};else{if("object"!=typeof n)throw"Invalid DayPilot.Modal.form() parameter";a=t,t=[];for(var d in a){var r={};r.name=d,r.id=d,t.push(r)}}}var s={};for(var d in i)s[d]=i[d];s.height=s.height||40,s.useIframe=!1;var c=s.okText||"OK",u=s.cancelText||"Cancel";return DayPilot.getPromise(function(i,n){s.onClosed=function(t){if(t.result){var n=JSON.parse(JSON.stringify(a));for(var o in t.result)e(n,o,t.result[o]);t.result=n}i(t)};var o=new DayPilot.Modal(s),d=document.createElement("div");d.className=o.theme+"_inner";var r=document.createElement("div");r.className=o.theme+"_input";var m=new l({theme:o.theme,form:t,data:a,zIndex:o.zIndex,locale:o.locale,plugins:o.plugins,onKey:function(e){switch(e.key){case"Enter":m.validate()&&o.close(m.serialize());break;case"Escape":o.close()}},onChange:function(e){"function"==typeof o.onChange&&o.onChange(e)}}),h=m.create();r.append(h);var p=document.createElement("div");p.className=o.theme+"_buttons";var f=document.createElement("button");f.innerText=c,f.className=o.theme+"_ok",s.okDisabled&&(f.disabled=!0),f.onclick=function(e){m.validate()&&o.close(m.serialize())};var v=document.createTextNode(" "),g=document.createElement("button");if(g.innerText=u,g.className=o.theme+"_cancel",g.onclick=function(e){o.close()},g.onmousedown=function(e){m.canceling=!0},p.appendChild(f),p.appendChild(v),p.appendChild(g),d.appendChild(r),d.appendChild(p),o.showHtml(d),o.div.setAttribute("tabindex","-1"),o.div.addEventListener("keydown",function(e){switch(e.keyCode){case 27:o.close();break;case 13:m.validate()&&o.close(m.serialize())}}),o.focus){var b=null;if("object"==typeof o.focus){var y=o.focus.id,_=o.focus.value;b=m.findViewById(y,_)}else"string"==typeof o.focus&&(b=m.findViewById(o.focus));b&&b.focus()}else{var I=m.firstFocusable();o.autoFocus&&I?I.focus():o.div.focus()}})},DayPilot.Modal.close=function(e){var t=DayPilot.Modal.opener();t&&t.close(e)},DayPilot.Modal.stretch=function(e){var t=DayPilot.Modal.opener();if(!t)throw"Unable to find the opener DayPilot.Modal instance.";t.stretch()},DayPilot.Modal.closeSerialized=function(){var e=DayPilot.Modal.opener()||DayPilot.ModalStatic.last();e&&e.closeSerialized()},DayPilot.Modal.opener=function(){return"undefined"!=typeof DayPilot&&"undefined"!=typeof DayPilot.ModalStatic&&DayPilot.ModalStatic.list.length>0?DayPilot.ModalStatic.list[DayPilot.ModalStatic.list.length-1]:parent&&parent.DayPilot&&parent.DayPilot.ModalStatic&&parent.DayPilot.ModalStatic.list[parent.DayPilot.ModalStatic.list.length-1]},DayPilot.Modal.Experimental={},DayPilot.Modal.Experimental.Form=l,"undefined"==typeof DayPilot.getPromise&&(DayPilot.getPromise=function(e){return"undefined"!=typeof Promise?new Promise(e):(DayPilot.Promise=function(e){
var t=this;this.then=function(t,a){return t=t||function(){},a=a||function(){},e(t,a),DayPilot.getPromise(e)},this["catch"]=function(a){return t.then(null,a),DayPilot.getPromise(e)}},new DayPilot.Promise(e))});var l=function(e){this.form=[],this.data={},this.theme="form_default",this.zIndex=99999,this.locale="en-us",this.plugins={},this.onKey=null,this.D=[],this.E=null,this.canceling=!1,this.F=[],this.G=[],this.H=null,e=e||{};for(var t in e)this[t]=e[t]};l.prototype.create=function(){return this.load(),this.render(),this.H},l.prototype.render=function(){var e=this;this.H=document.createElement("div"),this.D.forEach(function(t){e.createView(t)}),this.applyState()},l.prototype.createView=function(e){var t=this.theme,a=this,i=document.createElement("div");if(i.className=t+"_form_item "+t+"_form_item_level"+e.level,e.interactive||"title"!==e.type?i.className+=" "+t+"_form_item_"+e.type:i.className+=" "+t+"_form_title",e.data.cssClass&&(i.className+=" "+e.data.cssClass),!e.isValue){var n=document.createElement("div");n.className=t+"_form_item_label",n.innerText=e.text,i.appendChild(n)}var o=this.createInteractive(e);o.onInput=function(e){if(e=e||{},a.I(o,{"debounce":!e.immediate}),"function"==typeof a.onChange){var t={};t.result=a.serialize(),a.onChange(t)}},o.onBlur=function(){a.canceling||a.I(o)},o.apply(e),o.H=i,o.row=e,o.element&&i.appendChild(o.element),this.G.push(o),this.H.appendChild(i)},l.prototype.validate=function(){var e=this,t=!0;return this.G.forEach(function(a){var i=e.I(a);t=t&&i}),t},l.prototype.I=function(e,t){function a(){e.J&&(e.J.remove(),e.J=null),e.H.classList.add(u);var t=document.createElement("div");t.classList.add(m),t.innerText=c.message,e.J=t,e.H.appendChild(t)}t=t||{};var i=t.debounce,n=t.silent,o=e.row,l=!0,d="function"==typeof o.data.onValidate?o.data.onValidate:null,r="function"==typeof o.data.validate?o.data.validate:null,s=d||r;if(s){var c={};c.valid=!0,c.value=e.save()[o.field],c.message="Error",c.values=this.serialize(),c.result=this.serialize(),s(c);var u=this.theme+"_form_item_invalid",m=this.theme+"_form_item_invalid_message";if(c.valid)clearTimeout(this.F[o.field]),e.J&&(e.J.remove(),e.J=null),e.H.classList.remove(u);else if(!n)if(i){var h=1e3;clearTimeout(this.F[o.field]),this.F[o.field]=setTimeout(function(){a()},h)}else a();l=c.valid}return l},l.prototype.load=function(){var e=this;this.form.forEach(function(t){e.processFormItem(t,0)});var a;try{var i=JSON.stringify(this.data);a=t(JSON.parse(i))}catch(e){throw new Error("The 'data' object is not serializable (it may contain circular dependencies): "+e)}for(var n in a)this.setValue(n,a[n])},l.prototype.setValue=function(e,t){this.D.forEach(function(a){a.applyValue(e,t)})},l.prototype.updateDependentState=function(){var e=this,t=[!0];(this.E?this.E:this.D).forEach(function(a){var i=e.updateState(a,{enabled:t[a.level]&&!a.data.disabled});i.isValue&&(t[i.level+1]=i.enabled&&i.checked)})},l.prototype.processFormItem=function(e,t){var a=this,i=this.getFieldType(e),n=[];if("radio"===i){if(e.name){var o=new d;o.field=e.id,o.data=e,o.level=t,o.type="label",o.interactive=!1,o.text=e.name,a.D.push(o),n.push(o)}e.options.forEach(function(o){var l=new d;l.field=e.id,l.data=o,l.level=t,l.type=i,l.isValue=!0,l.text=o.name,l.resolved=o.id,a.D.push(l),n.push(l),o.children&&o.children.forEach(function(e){var i=a.processFormItem(e,t+1);n=n.concat(i)})})}else if("title"===i){var o=new d;o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=e.name,a.D.push(o),n.push(o)}else if("image"===i){var o=new d;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,a.D.push(o),n.push(o)}else if("html"===i){var o=new d;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,a.D.push(o),n.push(o)}else if("scrollable"===i){var o=new d;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,a.D.push(o),n.push(o)}else{var o=new d;o.field=e.id,o.data=e,o.level=t,o.type=i,o.text=e.name,o.children=[],a.D.push(o),n.push(o)}return"checkbox"===i&&(o.isValue=!0,o.resolved=!0,e.children&&e.children.forEach(function(e){var i=a.processFormItem(e,t+1);n=n.concat(i)})),n},l.prototype.doOnKey=function(e){if("function"==typeof this.onKey){var t={key:e};this.onKey(t)}},l.prototype.createInteractive=function(e){var t=this,a={"label":function(){return new r},"title":function(){return new r},"image":function(){var t=new r,a=document.createElement("img");return a.src=e.data.image,t.element=a,t},"html":function(){var t=new r,a=document.createElement("div");return"string"==typeof e.data.text?a.innerText=e.data.text:"string"==typeof e.data.html&&(a.innerHTML=e.data.html),t.element=a,t},"scrollable":function(){var a=new r,i=document.createElement("div");i.className=t.theme+"_form_item_scrollable_scroll",e.data.height&&(i.style.height=e.data.height+"px");var n=document.createElement("div");return n.className=t.theme+"_form_item_scrollable_scroll_content","string"==typeof e.data.text?n.innerText=e.data.text:"string"==typeof e.data.html&&(n.innerHTML=e.data.html),i.appendChild(n),a.element=i,a},"text":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.element;t.value=e.value,t.disabled=!e.enabled};var i=document.createElement("input");return i.name=e.field,i.type="text",i.autocomplete="off",i.onkeydown=function(e){var a=!1;switch(e.keyCode){case 13:t.doOnKey("Enter");break;case 27:t.doOnKey("Escape");break;default:a=!0}a||(e.preventDefault(),e.stopPropagation())},i.oninput=function(e){a.onInput()},i.onblur=function(e){a.onBlur()},a.element=i,a.canFocus=function(){return!a.element.disabled},a.focus=function(){a.element.focus(),a.element.setSelectionRange(0,a.element.value.length)},a.save=function(){var t={};return t[e.field]=i.value,t},a},"textarea":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.element;t.value=e.value,t.disabled=!e.enabled};var i=document.createElement("textarea");return i.name=e.field,e.data.height&&(i.style.height=e.data.height+"px"),i.onkeydown=function(e){var a=!1;switch(e.keyCode){case 13:(e.ctrlKey||e.metaKey)&&t.doOnKey("Enter"),a=!1;break;case 27:t.doOnKey("Escape");break;default:a=!0}a||e.stopPropagation()},i.oninput=function(e){a.onInput()},i.onblur=function(e){a.onBlur()},a.element=i,a.canFocus=function(){return!a.element.disabled},a.focus=function(){a.element.focus(),a.element.setSelectionRange(0,0)},a.save=function(){var t={};return t[e.field]=i.value,t},a},"date":function(){var a=new r;a.apply=function(e){a.row=e;var i=a.element,n=a.picker;e.data.dateFormat&&(n.pattern=e.data.dateFormat);var o=e.data.locale||t.locale;o&&(n.locale=o),i.disabled=!e.enabled,n.date=new DayPilot.Date(e.value);var l=new DayPilot.Date(e.value).toString(e.data.dateFormat||n.pattern,n.locale);i.value=l};var i=document.createElement("input");i.name=e.field;var n=new DayPilot.DatePicker({target:i,theme:"navigator_modal",zIndex:t.zIndex+1,resetTarget:!1,targetAlignment:"left",onTimeRangeSelect:function(e){a.onInput({"immediate":!0})}});return i.picker=n,i.className=t.theme+"_input_date",i.type="text",i.onkeydown=function(e){var a=!1;switch(e.keyCode){case 13:n.visible?n.close():t.doOnKey("Enter");break;case 27:n.visible?n.close():t.doOnKey("Escape");break;case 9:n.close(),a=!0;break;default:a=!0}a||(e.preventDefault(),e.stopPropagation())},i.onfocus=function(){n.show()},i.onclick=function(){n.show()},i.oninput=function(e){a.onInput()},i.onblur=function(e){a.onBlur()},a.element=i,a.picker=n,a.canFocus=function(){return!a.element.disabled},a.focus=function(){a.element.focus()},a.save=function(){var t=n.date?n.date.toString():null,a={};return a[e.field]=t,a},a},"time":function(){return t.K(e)},"datetime":function(){return t.L(e)},"select":function(){var t=new r;t.apply=function(e){t.row=e;var a=t.element;a.value=e.value,a.disabled=!e.enabled};var a=document.createElement("select");return a.name=e.field,e.data.options&&e.data.options.forEach&&e.data.options.forEach(function(e){var t=document.createElement("option");t.innerText=e.name||e.id,t.value=e.id,t.M=e.id,a.appendChild(t)}),a.onchange=function(e){t.onInput({"immediate":!0})},a.onblur=function(e){t.onBlur()},t.element=a,t.canFocus=function(){return!t.element.disabled},t.focus=function(){t.element.focus()},t.save=function(){var t=null,i=a.options[a.selectedIndex];i&&"undefined"!=typeof i.M&&(t=i.M);var n={};return n[e.field]=t,n},t},"searchable":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.searchable;t.disabled=!e.enabled,t.select(e.value)};var i=new s({data:e.data.options||[],name:e.field,theme:t.theme+"_form_item_searchable",listZIndex:t.zIndex+1,onSelect:function(e){e.ui&&a.onInput({"immediate":!0})}}),n=i.create();return a.element=n,a.searchable=i,a.canFocus=function(){return!a.searchable.disabled},a.focus=function(){a.searchable.focus()},a.save=function(){var t=i.selected&&i.selected.id,a={};return a[e.field]=t,a},a},"radio":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.radio;t.checked=e.checked,t.disabled=!e.enabled};var i=document.createElement("label"),n=document.createElement("input");n.type="radio",n.name=e.field,n.M=e.resolved,n.onchange=function(e){var i=a.row;t.findRowsByField(i.field).forEach(function(e){t.updateState(e,{checked:!1})}),t.updateState(i,{checked:!0}),t.applyState(),a.onInput({"immediate":!0})},n.onblur=function(e){a.onBlur()},i.appendChild(n);var o=document.createTextNode(e.text);return i.append(o),a.element=i,a.radio=n,a.canFocus=function(){return!1},a.focus=function(){a.radio.focus()},a.save=function(){if(!n.checked)return{};var t=n.M,a={};return a[e.field]=t,a},a},"checkbox":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.checkbox;t.checked=e.checked,t.disabled=!e.enabled};var i=document.createElement("label"),n=document.createElement("input");n.type="checkbox",n.name=e.field,n.M=e.resolved,n.onchange=function(e){var i=a.row;t.updateState(i,{checked:this.checked}),t.applyState(),a.onInput({"immediate":!0})},n.onblur=function(e){a.onBlur()},i.appendChild(n);var o=document.createTextNode(e.text);return i.append(o),a.element=i,a.checkbox=n,a.canFocus=function(){return!1},a.focus=function(){a.checkbox.focus()},a.save=function(){var t=n.checked,a={};return a[e.field]=t,a},a},"table":function(){var a=new r;a.apply=function(e){a.row=e;var t=a.table;t.disabled=!e.enabled,t.load(e.value||[])};var i=new c({name:e.field,form:t,theme:t.theme+"_form_item_tabular",item:e.data,onInput:function(e){a.onInput()}}),n=i.create();return a.element=n,a.table=i,a.canFocus=function(){return!1},a.focus=function(){a.table.focus()},a.save=function(){var t=i.save(),a={};return a[e.field]=t,a},a}};return t.plugins&&t.plugins[e.type]?t.plugins[e.type](e):a[e.type]()},l.prototype.K=function(e){var t=this,a=new r;a.apply=function(e){a.row=e;var t=a.searchable;t.disabled=!e.enabled,t.select(e.value)};var i=[],n=e.data.timeInterval||15;[1,5,10,15,20,30,60].includes(n)||(n=15);for(var o=60/n,l=e.data.locale||t.locale,d=DayPilot.Locale.find(l)||DayPilot.Locale.US,c=DayPilot.Date.today(),u=0;u<24*o;u++){var m=c.addMinutes(n*u),h={};h.name=m.toString(e.data.timeFormat||d.timePattern,d),h.id=m.toString("HH:mm"),i.push(h)}var p=new s({data:i,name:e.field,theme:t.theme+"_form_item_time",listZIndex:t.zIndex+1,strategy:"startsWith",onSelect:function(e){e.ui&&a.onInput({"immediate":!0})}}),f=p.create();return a.element=f,a.searchable=p,a.canFocus=function(){return!a.searchable.disabled},a.focus=function(){a.searchable.focus()},a.save=function(){var t=p.selected&&p.selected.id,a={};return a[e.field]=t,a},a},l.prototype.L=function(e){var t=this,a=new r;a.apply=function(e){a.row=e;var i=a.searchable;i.disabled=!e.enabled;var n=new DayPilot.Date(e.value).toString("HH:mm");i.select(n);var o=a.dateInput,l=a.picker;e.data.dateFormat&&(l.pattern=e.data.dateFormat);var d=e.data.locale||t.locale;if(d){var r=DayPilot.Locale.find(d)||DayPilot.Locale.US;l.locale=d,l.pattern=r.datePattern}o.disabled=!e.enabled,l.date=new DayPilot.Date(e.value);var s=new DayPilot.Date(e.value).toString(e.data.dateFormat||l.pattern,l.locale);o.value=s};var i=function(){var i=document.createElement("input");i.name=e.field;var n=new DayPilot.DatePicker({target:i,theme:"navigator_modal",zIndex:t.zIndex+1,resetTarget:!1,targetAlignment:"left",onTimeRangeSelect:function(e){a.onInput({"immediate":!0})}});return i.picker=n,i.className=t.theme+"_input_date",i.type="text",i.onkeydown=function(e){var a=!1;switch(e.keyCode){case 13:n.visible?n.close():t.doOnKey("Enter");break;case 27:n.visible?n.close():t.doOnKey("Escape");break;case 9:n.close(),a=!0;break;default:a=!0}a||(e.preventDefault(),e.stopPropagation())},i.onfocus=function(){n.show()},i.onclick=function(){n.show()},i.oninput=function(e){a.onInput()},i.onblur=function(e){a.onBlur()},a.dateInput=i,a.picker=n,i}(),n=function(){var i=[],n=e.data.timeInterval||15;[1,5,10,15,20,30,60].includes(n)||(n=15);for(var o=60/n,l=e.data.locale||t.locale,d=DayPilot.Locale.find(l)||DayPilot.Locale.US,r=DayPilot.Date.today(),c=0;c<24*o;c++){var u=r.addMinutes(n*c),m={};m.name=u.toString(e.data.timeFormat||d.timePattern,d),m.id=u.toString("HH:mm"),i.push(m)}var h=new s({data:i,name:e.field,theme:t.theme+"_form_item_time",listZIndex:t.zIndex+1,strategy:"startsWith",onSelect:function(e){e.ui&&a.onInput({"immediate":!0})}});return a.searchable=h,h.create()}(),o=document.createElement("div");return o.className=t.theme+"_form_item_datetime_parent",o.appendChild(i),o.appendChild(n),a.element=o,a.canFocus=function(){return!a.searchable.disabled},a.focus=function(){a.dateInput.focus()},a.save=function(){var t=a.searchable.selected&&a.searchable.selected.id,i=a.picker.date?a.picker.date.toString():null,n=new DayPilot.Date(i).getDatePart(),o=DayPilot.Date.parse(n.toString("yyyy-dd-MM ")+t,"yyyy-dd-MM HH:mm"),l={};return l[e.field]=o,l},a},l.prototype.findRowsByField=function(e){return this.D.filter(function(t){return t.field===e})},l.prototype.findViewById=function(e,t){return this.G.find(function(a){return a.row.field===e&&("radio"!==a.row.type||a.row.resolved===t)})},l.prototype.firstFocusable=function(){return this.G.find(function(e){return e.canFocus&&e.canFocus()})},l.prototype.updateState=function(e,t){var a=this.E?this.E:this.D,i=a.indexOf(e);return this.E=a.map(function(a){if(a!==e)return a;if(e.propsEqual(t))return e;var i=e.clone();for(var n in t)i[n]=t[n];return i}),this.E[i]},l.prototype.updateInteractive=function(e){var t=this.E.indexOf(e);this.G[t].apply(e)},l.prototype.applyState=function(){var e=this;if(this.updateDependentState(),this.E){this.E.filter(function(t,a){return e.D[a]!==t}).forEach(function(t){e.updateInteractive(t)}),this.D=this.E,this.E=null}},l.prototype.getFieldType=function(e){return["text","date","select","searchable","radio","checkbox","table","title","image","html","textarea","scrollable","time","datetime"].indexOf(e.type)!==-1?e.type:e.type&&this.plugins&&this.plugins[e.type]?e.type:e.image?"image":e.html||e.text?"html":e.id?e.options?"searchable":e.dateFormat?"date":e.columns?"table":"text":"title"},l.prototype.serialize=function(){var e={};return this.G.forEach(function(t){var a=t.save();for(var i in a)e[i]=a[i]}),e};var d=function(){this.id=this.guid(),this.field=null,this.data=null,this.type=null,this.level=0,this.enabled=!0,this.value=null,this.text=null,this.interactive=!0,this.isValue=!1,this.checked=!1,this.resolved=null};d.prototype.clone=function(){var e=new d;for(var t in this)"id"!==t&&(e[t]=this[t]);return e},d.prototype.propsEqual=function(e){for(var t in e)if(this[t]!==e[t])return!1;return!0},d.prototype.guid=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return""+e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},d.prototype.applyValue=function(e,t){this.field===e&&(this.value=t,this.isValue&&t===this.resolved&&(this.checked=!0))};var r=function(){this.element=null,this.canFocus=function(){return!1},this.apply=function(e){},this.focus=function(){},this.save=function(){return{}}},s=function(e){this.data=[],this.name=null,this.theme="searchable_default",this.N=!1,this.listZIndex=1e5,this.onSelect=null,this.O=null,this.P=null,this.Q=!1,this.R=null,this.S=null,this.T=[],this.U=null,e=e||{};var t=this,a={"selected":{post:function(e){"object"==typeof e&&e.id?t.O=e:"string"!=typeof e&&"number"!=typeof e||t.select(e)}}};Object.defineProperty(this,"selected",{get:function(){return this.O}}),Object.defineProperty(this,"disabled",{get:function(){return this.N},set:function(e){this.N=e,this.R&&(this.R.disabled=e,e&&this.V())}});for(var i in e)a[i]||(this[i]=e[i]);for(var i in e)a[i]&&a[i].post(e[i])};s.prototype.select=function(e){return this.O=this.data.find(function(t){return t.id===e}),this.W(!1),this},s.prototype.create=function(){function e(e){var a=r.strategy;"includes"!==r.strategy&&"startsWith"!==r.strategy&&(a="includes"),e=e||a||"includes",m.style.display="",m.style.top=p.offsetHeight+"px",m.style.left="0px",m.style.width=p.offsetWidth+"px",m.innerHTML="",m.addEventListener("mousedown",function(e){e.preventDefault()}),r.P=null,r.T=[];var n=null;r.data.forEach(function(a){var o=a.name||a.id;if("includes"===e){if(o.toLowerCase().indexOf(p.value.toLowerCase())===-1)return}else if("startsWith"===e&&0!==o.toLowerCase().indexOf(p.value.toLowerCase()))return;var l=document.createElement("div");l.className=r.theme+"_list_item",l.innerText=o,l.item=a,a===r.O&&(r.P=l),n||(n=l),l.addEventListener("mousedown",function(e){i(l),e.preventDefault()}),l.addEventListener("mousemove",function(e){r.P!==l&&(r.P=l,t({dontScroll:!0}))}),m.appendChild(l),r.T.push(l)}),r.P||(r.P=n),t()}function t(e){e=e||{};var t=!e.dontScroll;document.querySelectorAll("."+r.theme+"_list_item_highlight").forEach(function(e){e.className=e.className.replace(r.theme+"_list_item_highlight","")}),r.P&&(r.P.className+=" "+r.theme+"_list_item_highlight",t&&!a(r.P,m)&&r.P.scrollIntoView())}function a(e,t){var a=e.getBoundingClientRect(),i=t.getBoundingClientRect();return a.top>=i.top&&a.bottom<=i.bottom}function i(e){var t=e.item;r.O=t,r.W(!0),o(),l()}function n(){r.V()}function o(){r.X()}function l(){r.Q=!0,p.setAttribute("readonly","readonly"),p.focus()}function d(){r.Q=!1,p.removeAttribute("readonly"),p.select(),e("all")}var r=this,s=this,c=document.createElement("div");c.className=this.theme+"_main",c.style.position="relative";var u=document.createElement("div");u.className=this.theme+"_icon",u.style.position="absolute",u.style.right="0",u.style.top="0",u.style.bottom="0",u.style.width="20px",u.addEventListener("mousedown",function(e){e.preventDefault(),r.Q?(r.focus(),d()):(n(),l())});var m=document.createElement("div");m.className=this.theme+"_list",m.style.display="none",m.style.position="absolute",m.style.zIndex=this.listZIndex;var h=document.createElement("input");h.type="hidden",h.name=this.name,h.searchable=s,this.U=h;var p=document.createElement("input");return p.type="text",p.className=this.theme+"_input",p.disabled=this.N,p.addEventListener("click",function(e){d()}),p.addEventListener("focus",function(t){e("all")}),p.addEventListener("input",function(t){e()}),p.addEventListener("blur",function(e){p.removeAttribute("readonly"),n()}),p.addEventListener("keydown",function(e){if(r.Q){if("Enter"===e.key)return;if("Esc"===e.key||"Escape"===e.key)return;d()}if("ArrowDown"===e.key){var a=s.T.indexOf(s.P);a+1<s.T.length&&(s.P=s.T[a+1]),t()}else if("ArrowUp"===e.key){var a=s.T.indexOf(s.P);a-1>=0&&(s.P=s.T[a-1]),t()}else"Enter"===e.key?r.P?(e.stopPropagation(),i(r.P)):(e.stopPropagation(),n(),l()):"Esc"!==e.key&&"Escape"!==e.key||(e.stopPropagation(),n(),l())}),this.R=p,this.S=m,this.O||(this.O=this.data[0],this.O&&(p.value=this.O.name)),c.appendChild(p),c.appendChild(u),c.appendChild(h),c.appendChild(m),c},s.prototype.V=function(){this.X(),this.O?this.R.value=this.O.name:(this.R.value="",this.W(!0))},s.prototype.focus=function(){this.Q=!0,this.R.setAttribute("readonly","readonly"),this.R.focus(),this.V()},s.prototype.X=function(){this.S.style.display="none"},s.prototype.W=function(e){if(this.U.value=this.selected?this.selected.id:null,this.O?this.R.value=this.O.name:this.R.value="","function"==typeof this.onSelect){var t={control:this,ui:e};this.onSelect(t)}};var c=function(e){this.form=null,this.item=null,this.data=null,this.name=null,this.theme="edit_table_default",this.onInput=null,this.nav={},this.Y=null,this.D=[],e=e||{};for(var t in e)this[t]=e[t]};c.prototype.create=function(){var e=this,t=document.createElement("div");t.className=this.theme+"_main",t.style.position="relative";var a=document.createElement("input");a.type="hidden",a.name=e.name,a.table=this,t.appendChild(a);var i=document.createElement("div");i.className=this.theme+"_table";var n=this.Z();i.appendChild(n);var o=e.$({});o.spacer=!0;var l=this._(o);l.classList.add(e.theme+"_spacer"),i.appendChild(l);var d=document.createElement("div");d.className=e.theme+"_tbody",i.appendChild(d),t.appendChild(i);var r=document.createElement("div");t.appendChild(r),this.nav.body=d,this.nav.table=i,this.nav.main=t,this.nav.after=r;var s=document.createElement("div"),c=document.createElement("span");return c.className=this.theme+"_plus",c.addEventListener("click",function(t){if(!e.disabled){var a=e.item.onNewRow,i={};if("function"==typeof a){var n={};n.result=e.form.serialize(),n.value={},a(n),i=n.value}var o=e.$(i);e.D.push(o),e.aa(),e.ba()}}),this.nav.plus=c,s.appendChild(c),t.appendChild(s),t},c.prototype.Z=function(){var e=this,t=document.createElement("div");return t.classList.add(this.theme+"_row"),t.classList.add(this.theme+"_header"),this.item.columns.forEach(function(a){var i=document.createElement("div");i.classList.add(e.theme+"_cell"),i.innerText=a.name,t.appendChild(i)}),t},c.prototype.ca=function(){var e=this.item.max||0;return!!(e&&this.D.length>=e)},c.prototype.save=function(){var e=this,t=[];return e.D.forEach(function(e){var a={};e.cells.forEach(function(e){a[e.id]=e.value}),t.push(a)}),t},c.prototype.load=function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new Error("Array expected");this.data=e,this.da(),this.aa()},c.prototype.ea=function(){this.disabled?this.nav.main.classList.add(this.theme+"_disabled"):this.nav.main.classList.remove(this.theme+"_disabled"),this.ca()?this.nav.plus.classList.add(this.theme+"_plus_max"):this.nav.plus.classList.remove(this.theme+"_plus_max")},c.prototype.da=function(){var e=this;this.D=[],this.data.forEach(function(t){var a=e.$(t);e.D.push(a)})},c.prototype.fa=function(e){var t=this,a=t.D.indexOf(e);t.D.splice(a,1)},c.prototype.$=function(e){var t=this,a={};return a.data=e,a.cells=[],t.item.columns.forEach(function(i){var n=i.id,o=e[n],l=t.ga(i);if("undefined"==typeof o)if("text"===l)o="";else if("number"===l)o=0;else if("select"===l){var d=i.options;o=d&&d[0].id}var r={};r.id=n,r.value=o,r.type=l,r.data=i,a.cells.push(r)}),a},c.prototype.ga=function(e){var t=e.type;return t||(t=e.options?"select":"text"),t},c.prototype.aa=function(){var e=this;if(this.nav.body.innerHTML="",this.nav.after.innerHTML="",this.D.forEach(function(t){var a=e._(t);e.nav.body.appendChild(a)}),0===this.D.length){var t=e.ha();e.nav.after.appendChild(t)}this.ea()},c.prototype.ha=function(){var e=document.createElement("div");return e.className=this.theme+"_empty",e},c.prototype._=function(e){var t=this,a=document.createElement("div");a.className=t.theme+"_row",e.cells.forEach(function(i){var n=document.createElement("div");n.className=t.theme+"_cell";var o=t.ia(i);if(e.spacer){var l=document.createElement("div");l.style.height="0px",l.style.overflow="hidden",l.appendChild(o),n.appendChild(l)}else n.appendChild(o);a.appendChild(n)});var i=document.createElement("div");i.classList.add(t.theme+"_cell"),i.classList.add(t.theme+"_rowaction");var n=document.createElement("span");return n.className=this.theme+"_delete",n.addEventListener("click",function(a){t.disabled||(t.fa(e),t.aa(),t.ba())}),e.spacer||i.appendChild(n),a.appendChild(i),a},c.prototype.ba=function(){var e=this;if("function"==typeof e.onInput){var t={};e.onInput(t)}},c.prototype.ia=function(e){var t=this,a=e.type;if("text"===a||"number"===a){var i=document.createElement("input");return i.type=a,t.disabled&&(i.disabled=!0),e.value&&(i.value=e.value),i.addEventListener("keyup",function(i){"number"===a?e.value=Number(this.value):e.value=this.value,t.ba()}),i}if("select"===a){var n=document.createElement("select");return t.disabled&&(n.disabled=!0),e.data.options.forEach(function(t){var a=document.createElement("option");a.innerText=t.name,a.value=t.id,a.M=t.id,n.appendChild(a),e.value===t.id&&a.setAttribute("selected",!0)}),n.addEventListener("change",function(a){var i=n.options[n.selectedIndex];i&&"undefined"!=typeof i.M&&(e.value=i.M),t.ba()}),n}throw new Error("Unsupported item type: "+a)},c.prototype.focus=function(){}}}(DayPilot);