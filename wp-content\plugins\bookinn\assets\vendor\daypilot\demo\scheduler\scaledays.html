﻿<!DOCTYPE html>
<html>
<head>
    <title>Time Scale: Days (JavaScript Scheduler)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

    <!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

    <!-- /top -->

    <div class="note">This example shows one day per cell in the Scheduler grid. Read more about the <a href="https://doc.daypilot.org/scheduler/scale/">scheduler
        scale</a> [doc.daypilot.org].
    </div>

    <div id="dp"></div>

    <script type="text/javascript">
        const dp = new DayPilot.Scheduler("dp", {
            startDate: "2025-03-01",
            days: 300,
            timeHeaders: [
                {groupBy: "Month"},
                {groupBy: "Week"},
                {groupBy: "Cell", format: "d"}
            ],
            scale: "Day",
            resources: [
                {name: "Room A", id: "A"},
                {name: "Room B", id: "B"},
                {name: "Room C", id: "C"},
                {name: "Room D", id: "D"},
                {name: "Room E", id: "E"},
                {name: "Room F", id: "F"},
                {name: "Room G", id: "G"},
                {name: "Room H", id: "H"},
                {name: "Room I", id: "I"},
                {name: "Room J", id: "J"},
                {name: "Room K", id: "K"}
            ],
            onTimeRangeSelected: async args => {
                const modal = await DayPilot.Modal.prompt("New event name:", "Event");
                dp.clearSelection();
                if (modal.canceled) return;
                dp.events.add({
                    start: args.start,
                    end: args.end,
                    id: DayPilot.guid(),
                    resource: args.resource,
                    text: modal.result
                });
            },
            height: 350
        });

        dp.init();
        dp.scrollTo("2025-03-25");

    </script>

    <!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>
<!-- /bottom -->

</body>
</html>

