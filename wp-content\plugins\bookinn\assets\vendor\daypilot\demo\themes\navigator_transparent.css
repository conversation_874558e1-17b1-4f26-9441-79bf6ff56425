﻿/* navigator styles */

.navigator_transparent_main 
{
	border-left: 1px solid #A0A0A0;
	border-right: 1px solid #A0A0A0;
	border-bottom: 1px solid #A0A0A0;
    background-color: white;
    color: #000000;
}

.navigator_transparent_month {
    font-family: Tahoma;
    font-size: 8pt;
    /*border: 1px solid black;*/
}
.navigator_transparent_day {
    color: black;
    text-align: center;
    /*background-color: white;*/
}
.navigator_transparent_weekend {
    background-color: #f0f0f0;
}
.navigator_transparent_dayheader {
    color: black;
    text-align: center;
}

.navigator_transparent_weeknumber {
	text-align: center;
}

.navigator_transparent_line 
{
	border-bottom: 1px solid #A0A0A0;
}

.navigator_transparent_dayother {
    color: gray;
}
.navigator_transparent_todaybox
{
	border: 1px solid red;
}

.navigator_transparent_select 
{
    background-color: #ddd;
}
.navigator_transparent_title, .navigator_transparent_titleleft, .navigator_transparent_titleright {
	text-align: center;
    border-top: 1px solid #A0A0A0;

	color: #666;
	background: #eeeeee;
	
	background: -moz-linear-gradient(
		top,
		#f3f3f3 0%,
		#e9e9e9);
	background: -webkit-gradient(
		linear, left top, left bottom, 
		from(#f3f3f3),
		to(#e9e9e9));
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr="#f3f3f3", endColorStr="#e9e9e9");

}
.navigator_transparent_busy {
	font-weight: bold;
}
