﻿<!DOCTYPE html>
<html>
<head>
    <title>Navigator (Open-Source JavaScript Monthly Event Calendar)</title>

    <!-- head -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../helpers/v2/main.css?v=2025.3.696" type="text/css" rel="stylesheet"/>
    <script src="../js/daypilot-all.min.js?v=2025.3.696"></script>

	<!-- /head -->
</head>
<body>

<!-- top -->
<template id="content" data-version="2025.3.696">

<!-- /top -->

<div class="note"><b>Note:</b> Read more about integrating the <a href="https://doc.daypilot.org/month/navigator/">date navigator</a> [doc.daypilot.org].</div>

    <div style="display: flex;">
        <div style="margin-right: 10px;">
            <div id="nav"></div>
        </div>
        <div style="flex-grow: 1;">
            <div id="dp"></div>
        </div>
    </div>


<script type="text/javascript">

    var nav = new DayPilot.Navigator("nav");
    nav.startDate = "2022-03-01";
    nav.selectionDay = "2022-03-01";
    nav.showMonths = 3;
    nav.selectMode = "Month";
    nav.onTimeRangeSelected = function(args) {
        dp.startDate = args.start;
        dp.update();
    };
    nav.init();

    var dp = new DayPilot.Month("dp");

    // view
    dp.startDate = "2022-03-01";

    // generate and load events
    for (var i = 0; i < 10; i++) {
        var duration = Math.floor(Math.random() * 1.2);
        var start = Math.floor(Math.random() * 6) - 3; // -3 to 3

        var e = new DayPilot.Event({
            start: new DayPilot.Date("2022-03-04T00:00:00").addDays(start),
            end: new DayPilot.Date("2022-03-04T12:00:00").addDays(start).addDays(duration),
            id: DayPilot.guid(),
            text: "Event " + i
        });
        dp.events.add(e);
    }

    // event creating
    dp.onTimeRangeSelected = function (args) {
        var name = prompt("New event name:", "Event");
        dp.clearSelection();
        if (!name) return;
        var e = new DayPilot.Event({
            start: args.start,
            end: args.end,
            id: DayPilot.guid(),
            text: name
        });
        dp.events.add(e);
    };

    dp.init();


</script>

<!-- bottom -->
</template>

<script src="../helpers/v2/app.js?v=2025.3.696"></script>

<!-- /bottom -->

</body>
</html>

