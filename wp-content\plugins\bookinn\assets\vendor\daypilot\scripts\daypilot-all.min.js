﻿/*
DayPilot Lite
Copyright (c) 2005 - 2025 Annpoint s.r.o.
https://www.daypilot.org/
Licensed under Apache Software License 2.0
Version: 2025.3.696-lite
*/
var DayPilot={};if("undefined"==typeof DayPilot)var DayPilot={};if(function(){function e(e){var t=DayPilot.Date.Cache.Ticks;if(t[e])return DayPilot.Stats.cacheHitsTicks+=1,t[e];var n,i=new Date(e),a=i.getUTCMilliseconds();n=0===a?"":a<10?".00"+a:a<100?".0"+a:"."+a;var o=i.getUTCSeconds();o<10&&(o="0"+o);var r=i.getUTCMinutes();r<10&&(r="0"+r);var s=i.getUTCHours();s<10&&(s="0"+s);var l=i.getUTCDate();l<10&&(l="0"+l);var d=i.getUTCMonth()+1;d<10&&(d="0"+d);var c=i.getUTCFullYear();if(c<=0)throw"The minimum year supported is 1.";c<10?c="000"+c:c<100?c="00"+c:c<1e3&&(c="0"+c);var u=c+"-"+d+"-"+l+"T"+s+":"+r+":"+o+n;return t[e]=u,u}function t(e,t){return!DayPilot.Util.isNullOrUndefined(e)&&(!DayPilot.Util.isNullOrUndefined(t)&&e.toLocaleLowerCase()===t.toLocaleLowerCase())}function n(e){e=Math.min(e,255),e=Math.max(e,0);var t=e.toString(16);return e<16?"0"+t:t}if("undefined"==typeof DayPilot.$){"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),DayPilot.$=function(e){return document.getElementById(e)},Object.defineProperty(DayPilot,"isKhtml",{get:function(){return"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("KHTML")!==-1}}),DayPilot.touch={},DayPilot.touch.start="touchstart",DayPilot.touch.move="touchmove",DayPilot.touch.end="touchend",DayPilot.mo2=function(e,t){if("undefined"!=typeof t.offsetX){var n={x:t.offsetX+1,y:t.offsetY+1};if(!e)return n;for(var i=t.srcElement;i&&i!==e;)"SPAN"!==i.tagName&&(n.x+=i.offsetLeft,i.offsetTop>0&&(n.y+=i.offsetTop-i.scrollTop)),i=i.offsetParent;return i?n:null}if("undefined"!=typeof t.layerX){var n={x:t.layerX,y:t.layerY,src:t.target};if(!e)return n;for(var i=t.target;i&&"absolute"!==i.style.position&&"relative"!==i.style.position;)i=i.parentNode,DayPilot.isKhtml&&(n.y+=i.scrollTop);for(;i&&i!==e;)n.x+=i.offsetLeft,n.y+=i.offsetTop-i.scrollTop,i=i.offsetParent;return i?n:null}return null},DayPilot.mo3=function(e,t){var n,i=DayPilot.page(t);if(i)if(e){var a=DayPilot.abs(e);if(!a)throw new Error("no abs");n={x:i.x-a.x,y:i.y-a.y}}else n={x:i.x,y:i.y};else if(n=DayPilot.mo2(e,t),!n)return null;return n.shift=t.shiftKey,n.meta=t.metaKey,n.ctrl=t.ctrlKey,n.alt=t.altKey,n},DayPilot.browser={},Object.defineProperty(DayPilot.browser,"hover",{get:function(){return!window.matchMedia("(any-hover: none)").matches}}),DayPilot.touch={},DayPilot.debounce=function(e,t){var n;return function(){var i=this,a=arguments,o=function(){n=null,e.apply(i,a)};clearTimeout(n),n=setTimeout(o,t)}},DayPilot.page=function(e){var t=e.changedTouches&&e.changedTouches[0]?e.changedTouches[0]:e;return"undefined"!=typeof t.pageX?{x:t.pageX,y:t.pageY}:"undefined"!=typeof e.clientX&&document.body&&document.documentElement?{x:e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y:e.clientY+document.body.scrollTop+document.documentElement.scrollTop}:null},DayPilot.abs=function(e,t){if(!e)return null;if(e.getBoundingClientRect){var n=DayPilot.absBoundingClientBased(e);if(t){var i=DayPilot.absOffsetBased(e,!1),t=DayPilot.absOffsetBased(e,!0);n.x+=t.x-i.x,n.y+=t.y-i.y,n.w=t.w,n.h=t.h}return n}return DayPilot.absOffsetBased(e,t)},DayPilot.absBoundingClientBased=function(e){var t=e.getBoundingClientRect();return{x:t.left+window.pageXOffset,y:t.top+window.pageYOffset,w:e.clientWidth,h:e.clientHeight,toString:function(){return"x:"+this.x+" y:"+this.y+" w:"+this.w+" h:"+this.h}}},DayPilot.absOffsetBased=function(e,t){for(var n={x:e.offsetLeft,y:e.offsetTop,w:e.clientWidth,h:e.clientHeight,toString:function(){return"x:"+this.x+" y:"+this.y+" w:"+this.w+" h:"+this.h}};e.offsetParent;)e=e.offsetParent,n.x-=e.scrollLeft,n.y-=e.scrollTop,t&&(n.x<0&&(n.w+=n.x,n.x=0),n.y<0&&(n.h+=n.y,n.y=0),e.scrollLeft>0&&n.x+n.w>e.clientWidth&&(n.w-=n.x+n.w-e.clientWidth),e.scrollTop&&n.y+n.h>e.clientHeight&&(n.h-=n.y+n.h-e.clientHeight)),n.x+=e.offsetLeft,n.y+=e.offsetTop;var i=DayPilot.pageOffset();return n.x+=i.x,n.y+=i.y,n},DayPilot.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},DayPilot.distance=function(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},DayPilot.sheet=function(){function e(){for(var e=document.querySelectorAll("style[nonce]"),t=0;t<e.length;t++){var n=e[t];if(n.nonce)return n.nonce}if(document.currentScript&&document.currentScript.nonce)return document.currentScript.nonce;for(var i=document.querySelectorAll("script[nonce]"),a=0;a<i.length;a++){var o=i[a];if(o.nonce)return o.nonce}return""}if("undefined"==typeof window){var t={};return t.add=function(){},t.commit=function(){},t}var n=document.createElement("style");n.nonce=e(),n.styleSheet||n.appendChild(document.createTextNode("")),(document.head||document.getElementsByTagName("head")[0]).appendChild(n);var i=!!n.styleSheet,t={};return t.rules=[],t.commit=function(){i&&(n.styleSheet.cssText=this.rules.join("\n"))},t.add=function(e,t,a){return i?void this.rules.push(e+"{"+t+"}"):void(n.sheet.insertRule?("undefined"==typeof a&&(a=n.sheet.cssRules.length),n.sheet.insertRule(e+"{"+t+"}",a)):n.sheet.addRule&&n.sheet.addRule(e,t,a))},t},DayPilot.gs=function(e,t){return window.getComputedStyle(e,null).getPropertyValue(t)||""},DayPilot.StyleReader=function(e){this.get=function(t){return e?DayPilot.gs(e,t):null},this.getPx=function(e){var t=this.get(e);return t.indexOf("px")===-1?void 0:parseInt(t,10)}},function(){if(!DayPilot.Global.defaultCss){var e=DayPilot.sheet();e.add(".menu_default_main","user-select:none; font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;font-size: 13px;border: 1px solid #dddddd;background-color: white;padding: 0px;cursor: default;background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAABCAIAAABG0om7AAAAKXRFWHRDcmVhdGlvbiBUaW1lAHBvIDEwIDUgMjAxMCAyMjozMzo1OSArMDEwMGzy7+IAAAAHdElNRQfaBQoUJAesj4VUAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAABGdBTUEAALGPC/xhBQAAABVJREFUeNpj/P//PwO1weMnT2RlZAAYuwX/4oA3BgAAAABJRU5ErkJggg==);background-repeat: repeat-y;xborder-radius: 5px;-moz-box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);-webkit-box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);box-shadow:0px 2px 3px rgba(000,000,000,0.3),inset 0px 0px 2px rgba(255,255,255,0.8);"),e.add(".menu_default_main, .menu_default_main *, .menu_default_main *:before, .menu_default_main *:after","box-sizing: content-box;"),e.add(".menu_default_title","background-color: #f2f2f2;border-bottom: 1px solid gray;padding: 4px 4px 4px 37px;"),e.add(".menu_default_main a","padding: 2px 2px 2px 35px;color: black;text-decoration: none;cursor: default;"),e.add(".menu_default_main.menu_default_withchildren a","padding: 2px 35px 2px 35px;"),e.add(".menu_default_main a img","margin-left: 6px;margin-top: 2px;"),e.add(".menu_default_item_text","display: block;height: 20px;line-height: 20px; overflow:hidden;padding-left: 2px;padding-right: 20px; white-space: nowrap;"),e.add(".menu_default_main a:hover","background-color: #f3f3f3;"),e.add(".menu_default_main div div","border-top: 1px solid #dddddd;margin-top: 2px;margin-bottom: 2px;margin-left: 28px;"),e.add(".menu_default_main a.menu_default_item_disabled","color: #ccc"),e.add(".menu_default_item_haschildren.menu_default_item_haschildren_active","background-color: #f3f3f3;"),e.add(".menu_default_item_haschildren a:before","content: ''; border-width: 5px; border-color: transparent transparent transparent #666; border-style: solid; width: 0px; height:0px; position: absolute; right: 5px; margin-top: 5px;"),e.add(".menu_default_item_icon","position: absolute; top:0px; left: 0px; padding: 2px 2px 2px 8px;"),e.add(".menu_default_item a i","height: 20px;line-height: 20px;"),e.add(".menu_default_item .menu_default_item_symbol","width: 18px; height: 18px; color: #999; margin-left: 6px;margin-top: 2px;"),e.add(".menubar_default_main","border-bottom: 1px solid #ccc; font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif; font-size: 13px; user-select:none;"),e.add(".menubar_default_item","display: inline-block;  padding: 6px 10px; cursor: default;"),e.add(".menubar_default_item:hover","background-color: #f2f2f2;"),e.add(".menubar_default_item_active","background-color: #f2f2f2;"),e.add(".calendar_default_main","  --dp-calendar-border-color: #c0c0c0;  --dp-calendar-font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;  --dp-calendar-font-size: 13px;  --dp-calendar-header-bg-color: #f3f3f3;  --dp-calendar-header-color: #333;  --dp-calendar-colheader-padding: 0px;  --dp-calendar-rowheader-font-size: 16pt;  --dp-calendar-rowheader-padding: 3px;  --dp-calendar-cell-bg-color: #f9f9f9;  --dp-calendar-cell-business-bg-color: #ffffff;  --dp-calendar-cell-border-color: #ddd;  --dp-calendar-colheader-horizontal-align: center;  --dp-calendar-colheader-vertical-align: center;  --dp-calendar-allday-event-color: #333;  --dp-calendar-allday-event-border-color: #999;  --dp-calendar-allday-event-border: 1px solid var(--dp-calendar-allday-event-border-color);  --dp-calendar-allday-event-border-radius: 0px;  --dp-calendar-allday-event-bg-top-color: #ffffff;  --dp-calendar-allday-event-bg-bottom-color: #eeeeee;  --dp-calendar-allday-event-background: linear-gradient(to bottom, var(--dp-calendar-allday-event-bg-top-color) 0%, var(--dp-calendar-allday-event-bg-bottom-color) 100%);  --dp-calendar-allday-event-box-shadow: none;  --dp-calendar-allday-event-padding: 4px;  --dp-calendar-allday-event-horizontal-align: flex-start;  --dp-calendar-event-color: #333;  --dp-calendar-event-border-color: #999;  --dp-calendar-event-border: 1px solid var(--dp-calendar-event-border-color);  --dp-calendar-event-border-radius: 0px;  --dp-calendar-event-box-shadow: none;  --dp-calendar-event-bg-top-color: #ffffff;  --dp-calendar-event-bg-bottom-color: #eeeeee;  --dp-calendar-event-background: linear-gradient(to bottom, var(--dp-calendar-event-bg-top-color) 0%, var(--dp-calendar-event-bg-bottom-color) 100%);  --dp-calendar-event-bar-bg-color: #9dc8e8;  --dp-calendar-event-bar-color: #1066a8;  --dp-calendar-event-bar-width: 6px;  --dp-calendar-event-bar-left: 0px;  --dp-calendar-event-bar-bottom: 0px;  --dp-calendar-event-bar-top: 0px;  --dp-calendar-event-bar-display: block;  --dp-calendar-event-padding: 2px;  --dp-calendar-event-padding-left: 8px;  --dp-calendar-message-bg-color: #ffa216;  --dp-calendar-message-color: #ffffff;  --dp-calendar-message-padding: 10px;  --dp-calendar-message-opacity: 0.9;  --dp-calendar-selected-event-bg-color: #ddd;  --dp-calendar-shadow-color: #bbbbbb;  --dp-calendar-shadow-border-color: #888888;  --dp-calendar-forbidden-shadow-border-color: #cc0000;  --dp-calendar-forbidden-shadow-bg-color: #cc4125;  --dp-calendar-now-indicator-color: red;  --dp-calendar-scroll-bg-color: #f3f3f3;"),e.add(".calendar_default_main *, .calendar_default_main *:before, .calendar_default_main *:after","box-sizing: content-box;"),e.add(".calendar_default_main","border:1px solid var(--dp-calendar-border-color); font-family:var(--dp-calendar-font-family); font-size:var(--dp-calendar-font-size);"),e.add(".calendar_default_rowheader_inner, .calendar_default_cornerright_inner, .calendar_default_corner_inner, .calendar_default_colheader_inner, .calendar_default_alldayheader_inner","color: var(--dp-calendar-header-color); background: var(--dp-calendar-header-bg-color);"),e.add(".calendar_default_colheader_back","background: var(--dp-calendar-header-bg-color); border-bottom: 1px solid red;"),e.add(".calendar_default_colheader_back_inner","position: absolute; inset: 0; border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_cornerright_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_direction_rtl .calendar_default_cornerright_inner","border-right: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_rowheader_inner","font-size: var(--dp-calendar-rowheader-font-size); text-align: right; position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color); padding: var(--dp-calendar-rowheader-padding);"),e.add(".calendar_default_rowheader_simple .calendar_default_rowheader_inner","font-size: inherit; display: flex; align-items: center; justify-content: center; white-space: nowrap;"),e.add(".calendar_default_direction_rtl .calendar_default_rowheader_inner","border-right: none;"),e.add(".calendar_default_corner_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_direction_rtl .calendar_default_corner_inner","border-right: none;"),e.add(".calendar_default_rowheader_minutes","font-size: 10px; vertical-align: super; padding-left: 2px; padding-right: 2px;"),e.add(".calendar_default_colheader_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; padding: var(--dp-calendar-colheader-padding);border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color); display: flex; align-items: var(--dp-calendar-colheader-vertical-align); justify-content: var(--dp-calendar-colheader-horizontal-align);"),e.add(".calendar_default_cell_inner","position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-cell-border-color); border-bottom: 1px solid var(--dp-calendar-cell-border-color); background: var(--dp-calendar-cell-bg-color);"),e.add(".calendar_default_cell_business .calendar_default_cell_inner","background: var(--dp-calendar-cell-business-bg-color);"),e.add(".calendar_default_alldayheader_inner","text-align: center; position: absolute; top: 0px; left: 0px; bottom: 0px; right: 0px; border-right: 1px solid var(--dp-calendar-border-color); border-bottom: 1px solid var(--dp-calendar-border-color);"),e.add(".calendar_default_message","opacity: var(--dp-calendar-message-opacity); padding: var(--dp-calendar-message-padding); color: var(--dp-calendar-message-color); background: var(--dp-calendar-message-bg-color);"),e.add(".calendar_default_event_inner","color: var(--dp-calendar-event-color); border: var(--dp-calendar-event-border); border-radius: var(--dp-calendar-event-border-radius); background: var(--dp-calendar-event-background);"),e.add(".calendar_default_alldayevent","box-shadow: var(--dp-calendar-allday-event-box-shadow); border-radius: var(--dp-calendar-allday-event-border-radius);"),e.add(".calendar_default_alldayevent_inner","color: var(--dp-calendar-allday-event-color); border: var(--dp-calendar-allday-event-border); border-radius: var(--dp-calendar-allday-event-border-radius); background: var(--dp-calendar-allday-event-background);"),e.add(".calendar_default_event_bar","display: var(--dp-calendar-event-bar-display); top: var(--dp-calendar-event-bar-top); bottom: var(--dp-calendar-event-bar-bottom); left: var(--dp-calendar-event-bar-left); width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-bg-color);"),e.add(".calendar_default_direction_rtl .calendar_default_event_bar","top: 0px; bottom: 0px; right: 0px; width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-bg-color);"),e.add(".calendar_default_event_bar_inner","position: absolute; width: var(--dp-calendar-event-bar-width); background-color: var(--dp-calendar-event-bar-color);"),e.add(".calendar_default_selected .calendar_default_event_inner","background: var(--dp-calendar-selected-event-bg-color);"),e.add(".calendar_default_alldayevent_inner","position: absolute; top: 0px; bottom: 0px; left: 0px; right: 0px; overflow: hidden; padding: var(--dp-calendar-allday-event-padding); margin-right: 0px; display: flex; align-items: center; justify-content: var(--dp-calendar-allday-event-horizontal-align);"),e.add(".calendar_default_event_withheader .calendar_default_event_inner","padding-top: 15px;"),e.add(".calendar_default_event","box-shadow: var(--dp-calendar-event-box-shadow); border-radius: var(--dp-calendar-event-border-radius); cursor: default;"),e.add(".calendar_default_event_inner","position: absolute; overflow: hidden; top: 0px; bottom: 0px; left: 0px; right: 0px; padding: var(--dp-calendar-event-padding) var(--dp-calendar-event-padding) var(--dp-calendar-event-padding) var(--dp-calendar-event-padding-left);"),e.add(".calendar_default_direction_rtl .calendar_default_event_inner","padding: 2px 8px 2px 2px;"),e.add(".calendar_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-calendar-shadow-color); border: 1px solid var(--dp-calendar-shadow-border-color); opacity: 0.5; height: 100%;"),e.add(".calendar_default_shadow","box-shadow: 0 2px 5px rgba(0,0,0,0.2);"),e.add(".calendar_default_shadow_forbidden:after","content: ''; position: absolute; top: 5px; left: calc(50% - 10px); border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; background-image: url('data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 viewBox=%270 0 20 20%27%3E%3Ccircle cx=%2710%27 cy=%2710%27 r=%279%27 fill=%27%23cc0000aa%27 /%3E%3Cline x1=%275%27 y1=%275%27 x2=%2715%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3Cline x1=%2715%27 y1=%275%27 x2=%275%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: center; background-size: contain;"),e.add(".calendar_default_shadow_forbidden .calendar_default_shadow_inner","border: 1px solid var(--dp-calendar-forbidden-shadow-border-color); background: var(--dp-calendar-forbidden-shadow-bg-color);"),e.add(".calendar_default_event_delete","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAI5JREFUKFNtkLERgCAMRbmzdK8s4gAUlhYOYEHJEJYOYOEwDmGBPxC4kOPfvePy84MGR0RJ2N1A8H3N6DATwSQ57m2ql8NBG+AEM7D+UW+wjdfUPgerYNgB5gOLRHqhcasg84C2QxPMtrUhSqQIhg7ypy9VM2EUZPI/4rQ7rGxqo9sadTegw+UdjeDLAKUfhbaQUVPIfJYAAAAASUVORK5CYII=) center center no-repeat; opacity: 0.6; cursor: pointer;"),e.add(".calendar_default_event_delete:hover","opacity: 1; -ms-filter: none;"),e.add(".calendar_default_scroll_up","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAB3RJTUUH2wESDiYcrhwCiQAAAAlwSFlzAAAuIwAALiMBeKU/dgAAAARnQU1BAACxjwv8YQUAAACcSURBVHjaY2AgF9wWsTW6yGMlhi7OhC7AyMDQzMnBXIpFHAFuCtuaMTP+P8nA8P/b1x//FfW/HHuF1UQmxv+NUP1c3OxMVVhNvCVi683E8H8LXOY/w9+fTH81tF8fv4NiIpBRj+YoZtZ/LDUoJmKYhsVUpv0MDiyMDP96sIYV0FS2/8z9ICaLlOhvS4b/jC//MzC8xBG0vJeF7GQBlK0xdiUzCtsAAAAASUVORK5CYII=);"),e.add(".calendar_default_scroll_down","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAACqSURBVChTY7wpam3L9J+xmQEP+PGPKZZxP4MDi4zI78uMDIwa2NT+Z2DYovrmiC+TI8OBP/8ZmEqwGvif4e8vxr+FIDkmEKH25vBWBgbG0+iK/zEwLtF+ffwOXCGI8Y+BoRFFIdC030x/WmBiYBNhpgLdswNJ8RSYaSgmgk39z1gPUfj/29ef/9rwhQTDHRHbrbdEbLvRFcGthkkAra/9/uMvhkK8piNLAgCRpTnNn4AEmAAAAABJRU5ErkJggg==);"),e.add(".calendar_default_now","background-color: var(--dp-calendar-now-indicator-color);"),e.add(".calendar_default_now:before","content: ''; top: -5px; border-width: 5px; border-color: transparent transparent transparent var(--dp-calendar-now-indicator-color); border-style: solid; width: 0px; height: 0px; position: absolute; -moz-transform: scale(.9999);"),e.add(".calendar_default_shadow_top","box-sizing: border-box; padding: 2px; border: 1px solid var(--dp-calendar-border-color); background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); pointer-events: none;"),e.add(".calendar_default_shadow_bottom","box-sizing: border-box; padding: 2px; border: 1px solid var(--dp-calendar-border-color); background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); pointer-events: none;"),e.add(".calendar_default_crosshair_vertical, .calendar_default_crosshair_horizontal, .calendar_default_crosshair_left, .calendar_default_crosshair_top","background-color: gray; opacity: 0.2;"),e.add(".calendar_default_loading","background-color: orange; color: white; padding: 2px;"),e.add(".calendar_default_scroll","background-color: var(--dp-calendar-header-bg-color);"),e.add(".calendar_default_event_moving_source","opacity: 0.5;"),e.add(".calendar_default_colmove_handle","background-repeat: no-repeat; background-position: center center; background-color: #ccc; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAKCAYAAACT+/8OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAClJREFUGFdj+P//P4O9vX2Bg4NDP4gNFgBytgPxebgAMsYuQGMz/jMAAFsTZDPYJlDHAAAAAElFTkSuQmCC); cursor: move;"),e.add(".calendar_default_colheader:hover .calendar_default_colheader_splitter","background-color: #c0c0c0;"),e.add(".calendar_default_colmove_source","background-color: black; opacity: 0.5;"),e.add(".calendar_default_colmove_position_before","box-sizing: border-box; border-left: 2px solid #999999;"),e.add(".calendar_default_colmove_position_before:before","content: ''; border-width: 6px; border-color: transparent #999999 transparent transparent; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_after","box-sizing: border-box; border-right: 2px solid #999999;"),e.add(".calendar_default_colmove_position_after:before","content: ''; border-width: 6px; border-color: transparent transparent transparent #999999; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_child","box-sizing: border-box; border-bottom: 2px solid #999999;"),e.add(".calendar_default_colmove_position_child:before","content: ''; border-width: 6px; border-color: #999999 transparent transparent transparent; border-style: solid; width: 0px; height: 0px; position: absolute;"),e.add(".calendar_default_colmove_position_forbidden","border-top: 2px solid red;"),e.add(".calendar_default_colheader .calendar_default_colheader_splitter:hover","background-color: #999999;"),e.add(".calendar_default_block","background-color: #808080; opacity: 0.5;"),e.add(".month_default_main","  --dp-month-border-color: #c0c0c0;  --dp-month-font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;  --dp-month-font-size: 13px;  --dp-month-cell-border-color: #ddd;  --dp-month-cell-bg-color: #f9f9f9;  --dp-month-cell-business-bg-color: #ffffff;  --dp-month-event-color: #333;  --dp-month-event-border-color: #999;  --dp-month-event-border: 1px solid var(--dp-month-event-border-color);  --dp-month-event-bg-top-color: #ffffff;  --dp-month-event-bg-bottom-color: #eeeeee;  --dp-month-event-background: linear-gradient(to bottom, var(--dp-month-event-bg-top-color) 0%, var(--dp-month-event-bg-bottom-color) 100%);  --dp-month-event-horizontal-align: flex-start;  --dp-month-event-vertical-align: center;  --dp-month-event-padding: 2px;  --dp-month-event-padding-left: 10px;  --dp-month-event-padding-rtl: 2px 10px 2px 1px;  --dp-month-event-border-radius: 0px;  --dp-month-event-box-shadow: none;  --dp-month-event-bar-top: 1px;  --dp-month-event-bar-left: 2px;  --dp-month-event-bar-bottom: 1px;  --dp-month-event-bar-width: 6px;  --dp-month-event-bar-color: #1066a8;  --dp-month-event-bar-display: block;  --dp-month-header-bg-color: #f3f3f3;  --dp-month-header-color: #333;  --dp-month-header-horizontal-align: center;  --dp-month-header-vertical-align: center;  --dp-month-header-padding: 0px;  --dp-month-message-bg-color: #ffa216;  --dp-month-message-color: #ffffff;  --dp-month-message-padding: 10px;  --dp-month-selected-event-bg-color: #ddd;  --dp-month-shadow-color: #bbbbbb;  --dp-month-shadow-border-color: #888888;"),e.add(".month_default_main *, .month_default_main *:before, .month_default_main *:after","box-sizing: content-box; "),e.add(".month_default_main","border: 1px solid var(--dp-month-border-color); font-family: var(--dp-month-font-family); font-size: var(--dp-month-font-size); color: #333; "),e.add(".month_default_cell_inner","border-right: 1px solid var(--dp-month-cell-border-color); border-bottom: 1px solid var(--dp-month-cell-border-color); position: absolute; top: 0; left: 0; bottom: 0; right: 0; background-color: var(--dp-month-cell-bg-color); "),e.add(".month_default_cell_business .month_default_cell_inner","background-color: var(--dp-month-cell-business-bg-color); "),e.add(".month_default_cell_header","text-align: right; padding: 4px; box-sizing: border-box; "),e.add(".month_default_header_inner","position: absolute; inset: 0; border-right: 1px solid var(--dp-month-border-color); border-bottom: 1px solid var(--dp-month-border-color); cursor: default; color: var(--dp-month-header-color); background: var(--dp-month-header-bg-color); overflow: hidden; display: flex; align-items: var(--dp-month-header-vertical-align); justify-content: var(--dp-month-header-horizontal-align); padding: var(--dp-month-header-padding);"),e.add(".month_default_message","opacity: 0.9; color: var(--dp-month-message-color); background: var(--dp-month-message-bg-color); padding: var(--dp-month-message-padding); "),e.add(".month_default_event","border-radius: var(--dp-month-event-border-radius); box-shadow: var(--dp-month-event-box-shadow); "),e.add(".month_default_event_inner","position: absolute; top: 0; bottom: 0; left: 1px; right: 1px; overflow: hidden; padding: var(--dp-month-event-padding) var(--dp-month-event-padding) var(--dp-month-event-padding) var(--dp-month-event-padding-left); color: var(--dp-month-event-color); background: var(--dp-month-event-background); border: var(--dp-month-event-border); border-radius: var(--dp-month-event-border-radius); display: flex; align-items: var(--dp-month-event-vertical-align); justify-content: var(--dp-month-event-horizontal-align); "),e.add(".month_default_direction_rtl .month_default_event_inner","right: 2px; padding: var(--dp-month-event-padding-rtl); "),e.add(".month_default_event_continueright .month_default_event_inner","border-top-right-radius: 0; border-bottom-right-radius: 0; border-right-style: dotted; "),e.add(".month_default_event_continueleft .month_default_event_inner","border-top-left-radius: 0; border-bottom-left-radius: 0; border-left-style: dotted; "),e.add(".month_default_event_bar","display: var(--dp-month-event-bar-display); top: var(--dp-month-event-bar-top); bottom: var(--dp-month-event-bar-bottom); left: var(--dp-month-event-bar-left); width: var(--dp-month-event-bar-width); "),e.add(".month_default_direction_rtl .month_default_event_bar","top: 1px; bottom: 1px; right: 3px; width: var(--dp-month-event-bar-width); "),e.add(".month_default_event_bar_inner","position: absolute; width: var(--dp-month-event-bar-width); background-color: var(--dp-month-event-bar-color); "),e.add(".month_default_event_continueleft .month_default_event_bar","display: none; "),e.add(".month_default_selected .month_default_event_inner","background: var(--dp-month-selected-event-bg-color); "),e.add(".month_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-month-shadow-color); border: 1px solid var(--dp-month-shadow-border-color); opacity: 0.5; height: 100%; "),e.add(".month_default_shadow","box-shadow: 0 2px 5px rgba(0, 0, 0, .2); "),e.add(".month_default_event_delete","background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAI5JREFUKFNtkLERgCAMRbmzdK8s4gAUlhYOYEHJEJYOYOEwDmGBPxC4kOPfvePy84MGR0RJ2N1A8H3N6DATwSQ57m2ql8NBG+AEM7D+UW+wjdfUPgerYNgB5gOLRHqhcasg84C2QxPMtrUhSqQIhg7ypy9VM2EUZPI/4rQ7rGxqo9sadTegw+UdjeDLAKUfhbaQUVPIfJYAAAAASUVORK5CYII=) center center no-repeat; opacity: 0.6; cursor: pointer; "),e.add(".month_default_event_delete:hover","opacity: 1; "),e.add(".month_default_event_timeleft","color: #ccc; font-size: 11px; display: flex; align-items: center; "),e.add(".month_default_event_timeright","color: #ccc; font-size: 11px; display: flex; align-items: center; justify-content: end; "),e.add(".month_default_loading","background-color: orange; color: white; padding: 2px; "),e.add(".month_default_shadow_forbidden:after","content: ''; position: absolute; top: calc(50% - 10px); left: 10px; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; background-image: url('data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 viewBox=%270 0 20 20%27%3E%3Ccircle cx=%2710%27 cy=%2710%27 r=%279%27 fill=%27%23cc0000aa%27 /%3E%3Cline x1=%275%27 y1=%275%27 x2=%2715%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3Cline x1=%2715%27 y1=%275%27 x2=%275%27 y2=%2715%27 stroke=%27white%27 stroke-width=%271.5%27/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: center; background-size: contain; "),e.add(".month_default_shadow_forbidden .month_default_shadow_inner","border: 1px solid #cc0000; background: #cc4125; "),e.add(".navigator_default_main","  --dp-nav-border-color: #c0c0c0;  --dp-nav-font-family: -apple-system,system-ui,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;  --dp-nav-font-size: 12px;  --dp-nav-title-color: #333;  --dp-nav-title-bg-color: #f3f3f3;  --dp-nav-dayheader-color: #333;  --dp-nav-dayheader-bg-color: #ffffff;  --dp-nav-weeknumber-color: #999;  --dp-nav-weeknumber-bg-color: #ffffff;  --dp-nav-day-color: #000;  --dp-nav-day-bg-color: #ffffff;  --dp-nav-dayother-color: gray;  --dp-nav-dayother-bg-color: #ffffff;  --dp-nav-weekend-bg-color: #f0f0f0;  --dp-nav-select-bg-color: #FFE794;  --dp-nav-text-align: center;"),e.add(".navigator_default_main *, .navigator_default_main *:before, .navigator_default_main *:after","box-sizing: content-box;"),e.add(".navigator_default_main","border-left: 1px solid var(--dp-nav-border-color);border-right: 1px solid var(--dp-nav-border-color);border-bottom: 1px solid var(--dp-nav-border-color);background-color: white;color: var(--dp-nav-day-color);box-sizing: content-box;"),e.add(".navigator_default_month","font-family: var(--dp-nav-font-family);font-size: var(--dp-nav-font-size);"),e.add(".navigator_default_day","color: var(--dp-nav-day-color); background-color: var(--dp-nav-day-bg-color);"),e.add(".navigator_default_weekend","background-color: var(--dp-nav-weekend-bg-color);"),e.add(".navigator_default_dayheader","color: var(--dp-nav-dayheader-color);background-color: var(--dp-nav-dayheader-bg-color);text-align: var(--dp-nav-text-align);padding: 0px;"),e.add(".navigator_default_line","border-bottom: 1px solid var(--dp-nav-border-color);"),e.add(".navigator_default_dayother","color: var(--dp-nav-dayother-color); background-color: var(--dp-nav-dayother-bg-color);"),e.add(".navigator_default_todaybox","border: 1px solid red;"),e.add(".navigator_default_title, .navigator_default_titleleft, .navigator_default_titleright","box-sizing: border-box; border-top: 1px solid var(--dp-nav-border-color);border-bottom: 1px solid var(--dp-nav-border-color);color: var(--dp-nav-title-color);background: var(--dp-nav-title-bg-color);text-align: var(--dp-nav-text-align);"),e.add(".navigator_default_busy","font-weight: bold;"),e.add(".navigator_default_cell","text-align: var(--dp-nav-text-align);"),e.add(".navigator_default_select .navigator_default_cell_box","background-color: var(--dp-nav-select-bg-color);opacity: 0.5;"),e.add(".navigator_default_weeknumber","text-align: var(--dp-nav-text-align);color: var(--dp-nav-weeknumber-color);background: var(--dp-nav-weeknumber-bg-color);"),
e.add(".navigator_default_cell_text","cursor: pointer;"),e.add(".navigator_default_todaysection","box-sizing: border-box; display: flex; align-items: center; justify-content: center; border-top: 1px solid var(--dp-nav-border-color);"),e.add(".navigator_default_todaysection_button","cursor: pointer; color: #333; background-color: #f0f0f0; border: 1px solid var(--dp-nav-border-color); padding: 5px 10px; border-radius: 0px; "),e.add(".scheduler_default_main",'  --dp-scheduler-border-color: #c0c0c0;  --dp-scheduler-border-inner-color: #e0e0e0;  --dp-scheduler-cell-bg-color: #f9f9f9;  --dp-scheduler-cell-business-bg-color: #ffffff;  --dp-scheduler-event-background: linear-gradient(to bottom, var(--dp-scheduler-event-bg-top-color) 0%, var(--dp-scheduler-event-bg-bottom-color) 100%);  --dp-scheduler-event-bg-bottom-color: #eeeeee;  --dp-scheduler-event-bg-top-color: #ffffff;  --dp-scheduler-event-bar-bg-color: #9dc8e8;  --dp-scheduler-event-bar-color: #1066a8;  --dp-scheduler-event-bar-display: block;  --dp-scheduler-event-bar-height: 4px;  --dp-scheduler-event-bar-left: 0px;  --dp-scheduler-event-bar-right: 0px;  --dp-scheduler-event-bar-top: 0px;  --dp-scheduler-event-border: 1px solid var(--dp-scheduler-event-border-color);  --dp-scheduler-event-border-color: #ccc;  --dp-scheduler-event-border-radius: 0px;  --dp-scheduler-event-box-shadow: none;  --dp-scheduler-event-color: #333;  --dp-scheduler-event-horizontal-align: flex-start;  --dp-scheduler-event-milestone-color: #38761d;  --dp-scheduler-event-padding: 2px;  --dp-scheduler-event-selected-bg-color: #ddd;  --dp-scheduler-event-vertical-align: center;  --dp-scheduler-focus-outline-color: red;  --dp-scheduler-font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;  --dp-scheduler-font-size: 13px;  --dp-scheduler-grid-line-break-color: #999;  --dp-scheduler-grid-line-color: #eee;  --dp-scheduler-header-bg-color: #f3f3f3;  --dp-scheduler-header-color: #333;  --dp-scheduler-link-color: #cc0000;  --dp-scheduler-message-bg-color: #ffa216;  --dp-scheduler-message-color: #ffffff;  --dp-scheduler-message-padding: 10px;  --dp-scheduler-rowheader-padding: 7px;  --dp-scheduler-rowheader-vertical-align: center;  --dp-scheduler-selectionrectangle-color: #1066a8;  --dp-scheduler-shadow-border-color: #888888;  --dp-scheduler-shadow-color: #bbbbbb;  --dp-scheduler-timeheader-horizontal-align: center;  --dp-scheduler-timeheader-padding: 0px;  --dp-scheduler-timeheader-vertical-align: center;'),e.add(".scheduler_default_main *, .scheduler_default_main *:before, .scheduler_default_main *:after","box-sizing: content-box;"),e.add(".scheduler_default_main, .scheduler_default_main svg text","box-sizing: content-box; border: 1px solid var(--dp-scheduler-border-color); font-family: var(--dp-scheduler-font-family); font-size: var(--dp-scheduler-font-size);"),e.add(".scheduler_default_selected .scheduler_default_event_inner","background: var(--dp-scheduler-event-selected-bg-color);"),e.add(".scheduler_default_timeheader_scroll","background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_message","opacity: 0.9; padding: var(--dp-scheduler-message-padding); color: var(--dp-scheduler-message-color); background: var(--dp-scheduler-message-bg-color);"),e.add(".scheduler_default_timeheadergroup,.scheduler_default_timeheadercol","color: var(--dp-scheduler-header-color); background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_rowheader,.scheduler_default_corner","color: var(--dp-scheduler-header-color); background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_rowheader.scheduler_default_rowheader_selected","background-color: #aaa; background-image: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_rowheader_inner","position: absolute; left: 0px; right: 0px; top: 0px; bottom: 0px; border-right: 1px solid var(--dp-scheduler-border-inner-color); padding: var(--dp-scheduler-rowheader-padding); display: flex; align-items: var(--dp-scheduler-rowheader-vertical-align);"),e.add(".scheduler_default_timeheadergroup_inner, .scheduler_default_timeheadercol_inner","position: absolute; left: 0; right: 0; top: 0; bottom: 0;  border-right: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_timeheadergroup_inner","border-bottom: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_timeheadergroup_inner, .scheduler_default_timeheadercol_inner, .scheduler_default_timeheader_float","display: flex; align-items: var(--dp-scheduler-timeheader-vertical-align); justify-content: var(--dp-scheduler-timeheader-horizontal-align); padding: var(--dp-scheduler-timeheader-padding);"),e.add(".scheduler_default_divider, .scheduler_default_splitter","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_divider_horizontal","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_matrix_vertical_line","background-color: var(--dp-scheduler-grid-line-color);"),e.add(".scheduler_default_matrix_vertical_break","background-color: var(--dp-scheduler-grid-line-break-color);"),e.add(".scheduler_default_matrix_horizontal_line","background-color: var(--dp-scheduler-grid-line-color);"),e.add(".scheduler_default_resourcedivider","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_shadow_inner","box-sizing: border-box; background-color: var(--dp-scheduler-shadow-color); border: 1px solid var(--dp-scheduler-shadow-border-color); border-radius: var(--dp-scheduler-event-border-radius); opacity: 0.5; height: 100%;"),e.add(".scheduler_default_shadow","box-shadow: 0 2px 5px rgba(0,0,0,.2); border-radius: var(--dp-scheduler-event-border-radius);"),e.add(".scheduler_default_event","font-size: var(--dp-scheduler-font-size); color: var(--dp-scheduler-event-color); border-radius: var(--dp-scheduler-event-border-radius); box-shadow: var(--dp-scheduler-event-box-shadow);"),e.add(".scheduler_default_event_inner","position: absolute; top: 0px; left: 0px; right: 0px; bottom: 0px; border-radius: var(--dp-scheduler-event-border-radius); padding: var(--dp-scheduler-event-padding); overflow: hidden; border: var(--dp-scheduler-event-border); display: flex; align-items: var(--dp-scheduler-event-vertical-align); justify-content: var(--dp-scheduler-event-horizontal-align); background: var(--dp-scheduler-event-background);"),e.add(".scheduler_default_event_bar","display: var(--dp-scheduler-event-bar-display);top: var(--dp-scheduler-event-bar-top); left: var(--dp-scheduler-event-bar-left); right: var(--dp-scheduler-event-bar-right); height: var(--dp-scheduler-event-bar-height); background-color: var(--dp-scheduler-event-bar-bg-color);"),e.add(".scheduler_default_event_bar_inner","position:absolute; height: var(--dp-scheduler-event-bar-height); background-color: var(--dp-scheduler-event-bar-color);"),e.add(".scheduler_default_event_float","display: flex; align-items: center;"),e.add(".scheduler_default_event_float_inner","padding: var(--dp-scheduler-event-padding) var(--dp-scheduler-event-padding) var(--dp-scheduler-event-padding) 8px; position: relative;"),e.add(".scheduler_default_event_float_inner:after",'content:""; border-color: transparent #666 transparent transparent; border-style:solid; border-width:5px; width:0; height:0; position:absolute; top: calc(50% - 5px); left:-4px;'),e.add(".scheduler_default_event_focus","outline: var(--dp-scheduler-focus-outline-color) 2px solid; z-index: 100; opacity: 0.5;"),e.add(".scheduler_default_columnheader_inner","font-weight: bold;"),e.add(".scheduler_default_columnheader_splitter","box-sizing: border-box; border-right: 1px solid var(--dp-scheduler-border-color);"),e.add(".scheduler_default_columnheader_splitter:hover","background-color: var(--dp-scheduler-border-color);"),e.add(".scheduler_default_columnheader_cell_inner","position: absolute; left: 0px; right: 0px; top: 0px; bottom: 0px; padding: 2px; display: flex; align-items: center;"),e.add(".scheduler_default_cell","background-color: var(--dp-scheduler-cell-bg-color);"),e.add(".scheduler_default_cell.scheduler_default_cell_business","background-color: var(--dp-scheduler-cell-business-bg-color);"),e.add(".scheduler_default_cell.scheduler_default_cell_business.scheduler_default_cell_selected, .scheduler_default_cell.scheduler_default_cell_selected","background-color: #ccc; background-image: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_tree_image_no_children",""),e.add(".scheduler_default_tree_image_expand","background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnPjxwYXRoIGQ9J00gMS41IDAuNSBMIDYuNSA1IEwgMS41IDkuNScgc3R5bGU9J2ZpbGw6bm9uZTtzdHJva2U6Izk5OTk5OTtzdHJva2Utd2lkdGg6MjtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLWxpbmVjYXA6YnV0dCcgLz48L3N2Zz4=);"),e.add(".scheduler_default_tree_image_collapse","background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMS41IEwgNSA2LjUgTCA5LjUgMS41JyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojOTk5OTk5O3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==);"),e.add(".scheduler_default_event_delete","background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTInIGhlaWdodD0nMTInIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMC41IEwgMTEuNSAxMS41IE0gMC41IDExLjUgTCAxMS41IDAuNScgc3R5bGU9J2ZpbGw6bm9uZTtzdHJva2U6IzQ2NDY0NjtzdHJva2Utd2lkdGg6MztzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLWxpbmVjYXA6YnV0dCcgLz48L3N2Zz4=) no-repeat center center; opacity: 0.6; cursor: pointer;"),e.add(".scheduler_default_event_delete:hover","opacity: 1;"),e.add(".scheduler_default_rowmove_handle","background-repeat: no-repeat; background-position: center center; background-color: #ccc; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAKCAYAAACT+/8OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAClJREFUGFdj+P//P4O9vX2Bg4NDP4gNFgBytgPxebgAMsYuQGMz/jMAAFsTZDPYJlDHAAAAAElFTkSuQmCC); cursor: move;"),e.add(".scheduler_default_rowmove_source","background-color: black; opacity: 0.2;"),e.add(".scheduler_default_rowmove_position_before, .scheduler_default_rowmove_position_after","background-color: #999; height: 2px;"),e.add(".scheduler_default_rowmove_position_child","margin-left: 20px; background-color: #999; height: 2px;"),e.add(".scheduler_default_rowmove_position_forbidden","background-color: #cc0000; height: 2px; margin-left: 20px;"),e.add(".scheduler_default_link_horizontal","border-bottom-style: solid; border-bottom-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_vertical","border-right-style: solid; border-right-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_arrow_right:before","content: ''; border-width: 6px; border-color: transparent transparent transparent var(--dp-scheduler-link-color); border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_left:before","content: ''; border-width: 6px; border-color: transparent var(--dp-scheduler-link-color) transparent transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_down:before","content: ''; border-width: 6px; border-color: var(--dp-scheduler-link-color) transparent transparent transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_arrow_up:before","content: ''; border-width: 6px; border-color: transparent transparent var(--dp-scheduler-link-color) transparent; border-style: solid; width: 0; height:0; position: absolute;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_horizontal","border-bottom-color: #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_vertical","border-right-color: #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_right:before","border-color: transparent transparent transparent #aaaaaa;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_left:before","border-color: transparent #aaaaaa transparent transparent;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_down:before","border-color: #aaaaaa transparent transparent transparent;"),e.add(".scheduler_default_link_mshadow.scheduler_default_link_arrow_up:before","border-color: transparent transparent #aaaaaa transparent;"),e.add(".scheduler_default_block","background-color: #808080; opacity: 0.5;"),e.add(".scheduler_default_main .scheduler_default_event_group","box-sizing: border-box; font-size: 13px; color: #666; padding: 2px; overflow:hidden; border:1px solid var(--dp-scheduler-event-border-color); background-color: #fff; display: flex; align-items: center; white-space: nowrap;"),e.add(".scheduler_default_main .scheduler_default_header_icon","box-sizing: border-box; border: 1px solid var(--dp-scheduler-border-color); background-color: var(--dp-scheduler-header-bg-color); color: var(--dp-scheduler-header-color);"),e.add(".scheduler_default_header_icon:hover","background-color: #ccc;"),e.add(".scheduler_default_header_icon_hide:before","content: '\\00AB';"),e.add(".scheduler_default_header_icon_show:before","content: '\\00BB';"),e.add(".scheduler_default_row_new .scheduler_default_rowheader_inner","padding-left: 10px; color: #666; cursor: text; background-position: 0px 50%; background-repeat: no-repeat; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABUSURBVChTY0ACslAaK2CC0iCQDMSlECYmQFYIAl1AjFUxukIQwKoYm0IQwFCMSyEIaEJpMMClcD4Qp0CYEIBNIUzRPzAPCtAVYlWEDgyAGIdTGBgAbqEJYyjqa3oAAAAASUVORK5CYII=);"),e.add(".scheduler_default_row_new .scheduler_default_rowheader_inner:hover","background: white; color: white;"),e.add(".scheduler_default_rowheader textarea","padding: 3px;"),e.add(".scheduler_default_rowheader_scroll","cursor: default; background: var(--dp-scheduler-header-bg-color);"),e.add(".scheduler_default_shadow_forbidden .scheduler_default_shadow_inner, .scheduler_default_shadow_overlap .scheduler_default_shadow_inner","border: 1px solid #cc0000; background: #cc4125;"),e.add(".scheduler_default_event_moving_source","opacity: 0.5;"),e.add(".scheduler_default_linkpoint","background-color: white; border: 1px solid gray; border-radius: 5px;"),e.add(".scheduler_default_linkpoint.scheduler_default_linkpoint_hover","background-color: black;"),e.add(".scheduler_default_event.scheduler_default_event_version .scheduler_default_event_inner","overflow:hidden; background-color: #cfdde8; background-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent); background-size: 20px 20px;"),e.add(".scheduler_default_crosshair_vertical, .scheduler_default_crosshair_horizontal, .scheduler_default_crosshair_left, .scheduler_default_crosshair_top","background-color: gray; opacity: 0.2;"),e.add(".scheduler_default_link_dot","border-radius: 10px; background-color: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_task_milestone .scheduler_default_event_inner","position:absolute; top:16%; left:16%; right:16%; bottom:16%; background: var(--dp-scheduler-event-milestone-color); border: 0px none; transform: rotate(45deg); filter: none;"),e.add(".scheduler_default_event_left, .scheduler_default_event_right","white-space: nowrap; color: #666; cursor: default; display: flex; align-items: center;");e.add(".scheduler_default_main:focus","outline: none;");e.add(".scheduler_default_cell_focus","outline: var(--dp-scheduler-focus-outline-color) 2px solid; outline-offset: -2px; z-index: 100; opacity: 0.5;"),e.add(".scheduler_default_cell_focus.scheduler_default_cell_focus_top","border-top: 4px solid var(--dp-scheduler-focus-outline-color);"),e.add(".scheduler_default_cell_focus.scheduler_default_cell_focus_bottom","border-bottom: 4px solid var(--dp-scheduler-focus-outline-color);"),e.add(".scheduler_default_selectionrectangle","background-color: var(--dp-scheduler-selectionrectangle-color); border: 1px solid #000033; opacity: 0.4;"),e.add(".scheduler_default_link_shadow","border:1px solid black;"),e.add(".scheduler_default_link_shadow_circle","background-color:black;"),e.add(".scheduler_default_event_move_left","box-sizing: border-box; padding: 2px; border: 1px solid #ccc; background: #fff; background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); display: flex; align-items: center;"),e.add(".scheduler_default_event_move_right","box-sizing: border-box; padding: 2px; border: 1px solid #ccc; background: #fff; background: linear-gradient(to bottom, #ffffff 0%, #eeeeee); display: flex; align-items: center;"),e.add(".scheduler_default_link_hover","box-shadow: 0px 0px 2px 2px rgba(255, 0, 0, 0.3)"),e.add(".scheduler_default_sorticon","opacity: 0.2;background-position: center center; background-repeat: no-repeat; cursor: pointer;"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_asc","background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBvbHlnb24gcG9pbnRzPSI1IDEuNSwgMTAgMTAsIDAgMTAiLz48L3N2Zz4=');"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_desc","background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBvbHlnb24gcG9pbnRzPSI1IDguNSwgMCAwLCAxMCAwIi8+PC9zdmc+');"),e.add(".scheduler_default_sorticon.scheduler_default_sorticon_active","opacity: 1;"),e.add(".scheduler_default_loading","background-color: orange; color: white; padding: 2px;"),e.add(".scheduler_default_link_curve","stroke: var(--dp-scheduler-link-color); fill: none; stroke-width: 2;"),e.add(".scheduler_default_link_curve:hover","stroke-opacity: 0.5;"),e.add(".scheduler_default_link_curve_dot","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_marker","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_text","fill: var(--dp-scheduler-link-color);"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve","stroke: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_dot","fill: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_marker","fill: #aaaaaa;"),e.add(".scheduler_default_link_curve_mshadow.scheduler_default_link_curve_text","fill: #aaaaaa;"),e.commit(),DayPilot.Global.defaultCss=!0}}(),DayPilot.doc=function(){var e=document.documentElement;return e&&e.clientHeight?e:document.body},DayPilot.sh=function(e){return e?e.offsetHeight-e.clientHeight:0},DayPilot.guid=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return""+e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},DayPilot.ua=function(e){if(!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.ua() - array required");var t=[];return e.forEach(function(e){DayPilot.contains(t,e)||t.push(e)}),t},DayPilot.pageOffset=function(){if("undefined"!=typeof pageXOffset)return{x:pageXOffset,y:pageYOffset};var e=DayPilot.doc();return{x:e.scrollLeft,y:e.scrollTop}},DayPilot.indexOf=function(e,t){if(!e||!e.length)return-1;for(var n=0;n<e.length;n++)if(e[n]===t)return n;return-1},DayPilot.contains=function(e,t){if(2!==arguments.length)throw new DayPilot.Exception("DayPilot.contains() requires two arguments.");return!!e&&(e===t&&!DayPilot.isArray(e)||DayPilot.indexOf(e,t)!==-1)},DayPilot.ac=function(e,t){if(!t)var t=[];for(var n=0;e.children&&n<e.children.length;n++)t.push(e.children[n]),DayPilot.ac(e.children[n],t);return t},DayPilot.rfa=function(e,t){var n=DayPilot.indexOf(e,t);n!==-1&&e.splice(n,1)},DayPilot.mc=function(e){return e.pageX||e.pageY?{x:e.pageX,y:e.pageY}:{x:e.clientX+document.documentElement.scrollLeft,y:e.clientY+document.documentElement.scrollTop}},DayPilot.Stats={},DayPilot.Stats.eventObjects=0,DayPilot.Stats.dateObjects=0,DayPilot.Stats.cacheHitsCtor=0,DayPilot.Stats.cacheHitsParsing=0,DayPilot.Stats.cacheHitsTicks=0,DayPilot.re=function(e,t,n){n&&t&&e&&e.addEventListener(t,n,!1)},DayPilot.rePassive=function(e,t,n){n&&t&&e&&e.addEventListener(t,n,{"passive":!0})},DayPilot.reNonPassive=function(e,t,n){n&&t&&e&&e.addEventListener(t,n,{"passive":!1})},DayPilot.ue=function(e,t,n){e.removeEventListener(t,n,!1)},DayPilot.pu=function(e){var t,n,i,a=e.attributes;if(a)for(n=a.length,t=0;t<n;t+=1)a[t]&&(i=a[t].name,"function"==typeof e[i]&&(e[i]=null));if(a=e.childNodes)for(n=a.length,t=0;t<n;t+=1){DayPilot.pu(e.childNodes[t])}},DayPilot.de=function(e){if(e)if(DayPilot.isArray(e))for(var t=0;t<e.length;t++)DayPilot.de(e[t]);else e.parentNode&&e.parentNode.removeChild(e)},DayPilot.sw=function(e){return e?e.offsetWidth-e.clientWidth:0},DayPilot.am=function(){return"undefined"==typeof angular?null:(DayPilot.am.cached||(DayPilot.am.cached=angular.module("daypilot",[])),DayPilot.am.cached)},DayPilot.Selection=function(e,t,n,i){this.type="selection",this.start=e.isDayPilotDate?e:new DayPilot.Date(e),this.end=t.isDayPilotDate?t:new DayPilot.Date(t),this.resource=n,this.root=i,this.toJSON=function(e){var t={};return t.start=this.start,t.end=this.end,t.resource=this.resource,t}},DayPilot.request=function(e,t,n,i){var a=DayPilot.createXmlHttp();a&&(a.open("POST",e,!0),a.setRequestHeader("Content-type","text/plain"),a.onreadystatechange=function(){if(4===a.readyState)return 200!==a.status&&304!==a.status?void(i?i(a):window.console&&console.log("HTTP error "+a.status)):void t(a)},4!==a.readyState&&("object"==typeof n&&(n=JSON.stringify(n)),a.send(n)))},DayPilot.ajax=function(e){if(!e)throw new DayPilot.Exception("Parameter object required.");if("string"!=typeof e.url)throw new DayPilot.Exception("The parameter object must have 'url' property.");var t=DayPilot.createXmlHttp();if(!t)throw new DayPilot.Exception("Unable to create XMLHttpRequest object");var n="object"==typeof e.data,i=e.data,a=e.method||(e.data?"POST":"GET"),o=e.success||function(){},r=e.error||function(){},s=e.url,l=e.contentType||(n?"application/json":"text/plain"),d=e.headers||{};t.open(a,s,!0),t.setRequestHeader("Content-type",l),DayPilot.Util.ownPropsAsArray(d).forEach(function(e){t.setRequestHeader(e.key,e.val)}),t.onreadystatechange=function(){if(4===t.readyState)if(200===t.status||201===t.status||204===t.status||304===t.status){var e={};e.request=t,t.responseText&&(e.data=JSON.parse(t.responseText)),o(e)}else if(r){var e={};e.request=t,r(e)}else window.console&&console.log("HTTP error "+t.status)},4!==t.readyState&&(n&&(i=JSON.stringify(i)),t.send(i))},DayPilot.createXmlHttp=function(){return new XMLHttpRequest},DayPilot.Http={},DayPilot.Http.ajax=function(e){DayPilot.ajax(e)},DayPilot.Http.get=function(e,t){return t=t||{},new Promise(function(n,i){var a={};a.url=e,a.method="GET",a.success=function(e){n(e)},a.error=function(e){i(e)},a.contentType=t.contentType,a.headers=t.headers,DayPilot.ajax(a)})},DayPilot.Http.post=function(e,t,n){return n=n||{},new Promise(function(i,a){var o={};o.url=e,o.method="POST",o.data=t,o.success=function(e){i(e)},o.error=function(e){a(e)},o.contentType=n.contentType,o.headers=n.headers,DayPilot.ajax(o)})},DayPilot.Http.put=function(e,t,n){return n=n||{},new Promise(function(i,a){var o={};o.url=e,o.method="PUT",o.data=t,o.success=function(e){i(e)},o.error=function(e){a(e)},o.contentType=n.contentType,o.headers=n.headers,DayPilot.ajax(o)})},DayPilot.Http.delete=function(e,t){return t=t||{},new Promise(function(n,i){var a={};a.url=e,a.method="DELETE",a.success=function(e){n(e)},a.error=function(e){i(e)},a.contentType=t.contentType,a.headers=t.headers,DayPilot.ajax(a)})},DayPilot.Util={},DayPilot.Util.addClass=function(e,t){if(e){if(!e.className)return void(e.className=t);new RegExp("(^|\\s)"+t+"($|\\s)").test(e.className)||(e.className=e.className+" "+t)}},DayPilot.Util.removeClass=function(e,t){if(e){var n=new RegExp("(^|\\s)"+t+"($|\\s)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}},DayPilot.Util.copyProps=function(e,t,n){if(t||(t={}),!e)return t;if("undefined"==typeof n)for(var i in e)e.hasOwnProperty(i)&&"undefined"!=typeof e[i]&&(t[i]=e[i]);else for(var a=0;a<n.length;a++){var i=n[a];"undefined"!=typeof e[i]&&(t[i]=e[i])}return t},DayPilot.Util.ownPropsAsArray=function(e){var t=[];if(!e)return t;for(var n in e)if(e.hasOwnProperty(n)){var i={};i.key=n,i.val=e[n],t.push(i)}return t},DayPilot.Util.atLeast=function(e,t){return Math.max(e,t)},DayPilot.Util.mouseButton=function(e){var t={};switch(e.button){case 0:t.left=!0;break;case 1:t.middle=!0;break;case 2:t.right=!0}return t},DayPilot.Util.replaceCharAt=function(e,t,n){return e.substr(0,t)+n+e.substr(t+n.length)},DayPilot.Util.isNullOrUndefined=function(e){return null===e||"undefined"==typeof e},DayPilot.Util.escapeHtml=function(e){var t=document.createElement("div");return t.innerText=e,t.innerHTML},DayPilot.Util.escapeTextHtml=function(e,t){return DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":DayPilot.Util.escapeHtml(e):t},DayPilot.Util.isSameEvent=function(e,t){return!(!e||!t)&&(e=e instanceof DayPilot.Event?e.data:e,t=t instanceof DayPilot.Event?t.data:t,e===t||!DayPilot.Util.isNullOrUndefined(e.id)&&e.id===t.id)},DayPilot.Util.overlaps=function(e,t,n,i){return!(t<=n||e>=i)},DayPilot.Util.isVueVNode=function(e){return!!e&&(DayPilot.isArray(e)?DayPilot.Util.isVueVNode(e[0]):e["__v_isVNode"])},DayPilot.Util.isMouseEvent=function(e){return!!e&&("mouse"===e.pointerType||e instanceof MouseEvent)},DayPilot.Areas={},DayPilot.Areas.attach=function(e,t,n){var n=n||{},a=n.areas,o=n.allowed||function(){return!0},r=n.offsetX||0;a=i(t,a),a&&DayPilot.isArray(a)&&0!==a.length&&(DayPilot.re(e,"mousemove",function(i){e.active||e.areasDisabled||!o()||DayPilot.Areas.showAreas(e,t,i,a,{"offsetX":r,"eventDiv":n.eventDiv})}),DayPilot.re(e,"mouseleave",function(t){DayPilot.Areas.hideAreas(e,t)}),a.forEach(function(i){if(DayPilot.Areas.isVisible(i)){var a=DayPilot.Areas.createArea(e,t,i,{"offsetX":r,"eventDiv":n.eventDiv});e.appendChild(a)}}))},DayPilot.Areas.disable=function(e){e.areasDisabled=!0,Array.from(e.childNodes).filter(function(e){return e.isActiveArea&&!e.area.start}).forEach(function(e){e.c=e.style.display,e.style.display="none"})},DayPilot.Areas.enable=function(e){e.areasDisabled=!1,Array.from(e.childNodes).filter(function(e){return e.isActiveArea&&!e.area.start}).forEach(function(e){e.c?e.style.display=e.c:e.style.display=""})},DayPilot.Areas.remove=function(e){var t=Array.from(e.childNodes).filter(function(e){return e.isActiveArea});DayPilot.de(t)},DayPilot.Areas.isVisible=function(e){var t=e.visibility||e.v||"Visible";return"Visible"===t||"TouchVisible"===t&&!DayPilot.browser.hover},DayPilot.Areas.copy=function(e){return DayPilot.isArray(e)?e.map(function(e){return DayPilot.Util.copyProps(e,{})}):[]};var i=function(e,t){return DayPilot.isArray(t)||(t=e.areas,t||(e.cache?t=e.cache.areas:e.data&&(t=e.data.areas))),t};DayPilot.Areas.showAreas=function(e,t,n,i,a){if(!DayPilot.Global.resizing&&!DayPilot.Global.moving&&!DayPilot.Global.selecting&&!e.active&&DayPilot.browser.hover){if(DayPilot.Areas.all&&DayPilot.Areas.all.length>0)for(var o=0;o<DayPilot.Areas.all.length;o++){var r=DayPilot.Areas.all[o];r!==e&&DayPilot.Areas.hideAreas(r,n)}if(e.active={},DayPilot.isArray(i)||(i=t.areas,i||(t.cache?i=t.cache.areas:t.data&&(i=t.data.areas))),i&&0!==i.length&&!(e.areas&&e.areas.length>0)){e.areas=[];for(var o=0;o<i.length;o++){var s=i[o];if(!DayPilot.Areas.isVisible(s)){var l=DayPilot.Areas.createArea(e,t,s,a);e.areas.push(l),e.appendChild(l),DayPilot.Areas.all.push(e)}}e.active.children=DayPilot.ac(e)}}},DayPilot.Areas.createArea=function(e,t,n,i){function a(e,t,n){var i={};return i.area=e,i.source=t,i.originalEvent=n,i.preventDefault=function(){i.preventDefault.value=!0},"function"==typeof e.onClick&&e.onClick(i),i}function o(e,t,n){DayPilot.Bubble&&DayPilot.Bubble.touchPosition(n),e.calendar.bubble&&e.calendar.bubble.showEvent(e,!0)}function r(e,t,n,i){DayPilot.Menu&&DayPilot.Menu.touchPosition(i);var a=n.contextMenu||n.menu;if(a instanceof DayPilot.Menu||(t.isEvent&&t.client.contextMenu()?a=t.client.contextMenu():t.isEvent&&t.calendar.contextMenu&&(a=t.calendar.contextMenu)),a&&a.show){var o={"type":"area","div":e,"e":t,"area":n,"a":d};a.show(t,{"initiator":o})}}function s(e){return"string"==typeof e&&isNaN(e)?e:"undefined"!=typeof e?e+"px":void 0}var i=i||{},l=(i.offsetX||0,i.eventDiv||e),d=document.createElement("div");d.isActiveArea=!0,d.area=n,d.setAttribute("unselectable","on");var c=n.w||n.width,u=n.h||n.height,h=n.cssClass||n.css||n.className;if("undefined"!=typeof n.style&&d.setAttribute("style",n.style),d.style.position="absolute",d.style.width=s(c),d.style.height=s(u),d.style.right=s(n.right),d.style.top=s(n.top),d.style.left=s(n.left),d.style.bottom=s(n.bottom),d.style.borderRadius=s(n.borderRadius),"undefined"!=typeof n.html||"undefined"!=typeof n.text)d.innerHTML=DayPilot.Util.escapeTextHtml(n.text,n.html);else if(n.icon){var f=document.createElement("i");f.className=n.icon,d.appendChild(f)}else if(n.image){var v=document.createElement("img");v.src=n.image,d.appendChild(v)}else if(n.symbol){var p="http://www.w3.org/2000/svg",m=document.createElementNS(p,"svg");m.setAttribute("width","100%"),m.setAttribute("height","100%");var y=document.createElementNS(p,"use");y.setAttribute("href",n.symbol),m.appendChild(y),d.appendChild(m)}if(h&&(d.className=h),n.toolTip&&d.setAttribute("title",n.toolTip),n.backColor&&(d.style.background=n.backColor),n.background&&(d.style.background=n.background),n.fontColor&&(d.style.color=n.fontColor),n.padding&&(d.style.padding=n.padding+"px",d.style.boxSizing="border-box"),n.verticalAlignment)switch(d.style.display="flex",n.verticalAlignment){case"center":d.style.alignItems="center";break;case"top":d.style.alignItems="flex-start";break;case"bottom":d.style.alignItems="flex-end"}if(n.horizontalAlignment)switch(d.style.display="flex",n.horizontalAlignment){case"right":d.style.justifyContent="flex-end";break;case"left":d.style.justifyContent="flex-start";break;case"center":d.style.justifyContent="center"}if("ResizeEnd"===n.action||"ResizeStart"===n.action||"Move"===n.action){if(t.calendar.isCalendar)switch(n.action){case"ResizeEnd":n.cursor="s-resize",n.dpBorder="bottom";break;case"ResizeStart":n.cursor="n-resize",n.dpBorder="top";break;case"Move":n.cursor="move"}if(t.calendar.isScheduler||t.calendar.isMonth)switch(n.action){case"ResizeEnd":n.cursor="e-resize",n.dpBorder="right";break;case"ResizeStart":n.cursor="w-resize",n.dpBorder="left";break;case"Move":n.cursor="move"}d.onmousemove=function(e,t,n){return function(i){t.calendar.internal&&t.calendar.internal.dragInProgress&&t.calendar.internal.dragInProgress()||(i.cancelBubble=!0,e.style.cursor=n.cursor,n.dpBorder&&(e.dpBorder=n.dpBorder))}}(l,t,n),d.onmouseout=function(e,t,n){return function(t){e.style.cursor=""}}(l,t,n)}if(("ResizeEnd"===n.action||"ResizeStart"===n.action)&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,n){return function(i){i.cancelBubble=!0;var a=t.calendar.internal.touch,o=i.touches?i.touches[0]:i,r={x:o.pageX,y:o.pageY};t.calendar.coords=a.relativeCoords(i),a.preventEventTap=!0,t.calendar.isScheduler?a.startResizing(e,"ResizeEnd"===n.action?"right":"left"):t.calendar.isCalendar&&a.startResizing(e,"ResizeEnd"===n.action?"bottom":"top",r)}}(l,t,n);DayPilot.rePassive(d,DayPilot.touch.start,g)}if("ContextMenu"===n.action&&t.isEvent&&t.calendar.internal.touch){
var g=function(e,t,n){return function(i){i.cancelBubble=!0,i.preventDefault(),r(e,t,n,i),t.calendar.internal.touch.preventEventTap=!0}}(l,t,n),b=function(e,t,n){return function(e){e.cancelBubble=!0,e.preventDefault()}}(l,t,n);DayPilot.reNonPassive(d,DayPilot.touch.start,g),DayPilot.reNonPassive(d,DayPilot.touch.end,b)}if("Bubble"===n.action&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,n){return function(e){e.cancelBubble=!0,e.preventDefault();var i=a(n,t,e);if(!i.preventDefault.value){o(t,n,e);t.calendar.internal.touch.preventEventTap=!0,"function"==typeof n.onClicked&&n.onClicked(i)}}}(l,t,n),b=function(e,t,n){return function(e){e.cancelBubble=!0,e.preventDefault()}}(l,t,n);DayPilot.reNonPassive(d,DayPilot.touch.start,g),DayPilot.reNonPassive(d,DayPilot.touch.end,b)}if("Move"===n.action&&t.isEvent&&t.calendar.internal.touch){var g=function(e,t,n){return function(n){n.cancelBubble=!0;var i=t.calendar.internal.touch,a=n.touches?n.touches[0]:n,o={x:a.pageX,y:a.pageY};t.calendar.coords=i.relativeCoords(n),DayPilot.Global&&DayPilot.Global.touch&&(DayPilot.Global.touch.active=!0),i.preventEventTap=!0,i.startMoving(e,o)}}(l,t,n);DayPilot.rePassive(d,DayPilot.touch.start,g)}if("Bubble"===n.action&&t.isEvent?(d.onmousemove=function(e,t,n){return function(e){n.bubble?n.bubble.showEvent(t,!0):t.calendar.bubble&&t.calendar.bubble.showEvent(t,!0)}}(e,t,n),d.onmouseout=function(e,t,n){return function(e){"undefined"!=typeof DayPilot.Bubble&&(n.bubble?n.bubble.hideOnMouseOut():t.calendar.bubble&&t.calendar.bubble.hideOnMouseOut())}}(e,t,n)):"Bubble"===n.action&&t.isRow?(d.onmousemove=function(e,t,n){return function(e){n.bubble?n.bubble.showResource(t,!0):t.calendar.resourceBubble&&t.calendar.resourceBubble.showResource(t,!0)}}(e,t,n),d.onmouseout=function(e,t,n){return function(e){"undefined"!=typeof DayPilot.Bubble&&(n.bubble?n.bubble.hideOnMouseOut():t.calendar.resourceBubble&&t.calendar.resourceBubble.hideOnMouseOut())}}(e,t,n)):"Bubble"===n.action&&"undefined"!=typeof DayPilot.Bubble&&n.bubble instanceof DayPilot.Bubble&&(d.onmousemove=function(e,t,n){return function(e){n.bubble.showHtml(null,null)}}(e,t,n),d.onmouseout=function(e,t,n){return function(e){"undefined"!=typeof DayPilot.Bubble&&n.bubble&&n.bubble.hideOnMouseOut()}}(e,t,n)),"HoverMenu"===n.action&&(d.onmousemove=function(e,t,n){return function(e){var i=n.menu;i&&i.show&&(i.visible?i.source&&"undefined"!=typeof i.source.id&&i.source.id!==t.id&&i.show(t):i.show(t),i.cancelHideTimeout())}}(e,t,n),d.onmouseout=function(e,t,n){return function(e){var t=n.menu;t&&t.hideOnMouseOver&&t.delayedHide()}}(e,t,n)),"None"===n.action){var g=function(e,t,n){return function(e){var i=a(n,t,e);"function"==typeof n.onClicked&&n.onClicked(i),e.preventDefault(),e.stopPropagation()}}(l,t,n);DayPilot.reNonPassive(d,DayPilot.touch.start,g)}return d.onmousedown=function(e,t,n){return function(i){if("function"==typeof n.onmousedown&&n.onmousedown(i),"function"==typeof n.mousedown){var a={};a.area=n,a.div=e,a.originalEvent=i,a.source=t,n.mousedown(a)}if("Move"===n.action&&t.isRow){var o=t.$.row;(0,t.calendar.internal.rowStartMoving)(o)}"undefined"!=typeof DayPilot.Bubble&&DayPilot.Bubble.hideActive(),"Move"===n.action&&(DayPilot.Global.movingAreaData=n.data),"Move"===n.action&&t.isEvent&&t.calendar.internal&&t.calendar.internal.startMoving&&t.calendar.internal.startMoving(e,i);"Move"!==n.action&&"ResizeEnd"!==n.action&&"ResizeStart"!==n.action&&n.action&&"Default"!==n.action&&(i.preventDefault(),i.cancelBubble=!0)}}(e,t,n),d.onclick=function(e,t,n){return function(i){var o=a(n,t,i);if(!o.preventDefault.value){switch(n.action){case"ContextMenu":r(e,t,n,i),i.cancelBubble=!0;break;case"None":i.cancelBubble=!0}"function"==typeof n.onClicked&&n.onClicked(o)}}}(e,t,n),"function"==typeof n.onMouseEnter&&d.addEventListener("mouseenter",function(e,t,n){return function(e){var i={};i.area=n,i.source=t,i.originalEvent=e,n.onMouseEnter(i)}}(e,t,n)),"function"==typeof n.onMouseLeave&&d.addEventListener("mouseleave",function(e,t,n){return function(e){var i={};i.area=n,i.source=t,i.originalEvent=e,n.onMouseLeave(i)}}(e,t,n)),d},DayPilot.Areas.all=[],DayPilot.Areas.hideAreas=function(e,t){if(e&&e&&e.active){var n=e.active,i=e.areas;if(n&&n.children&&t){var a=t.toElement||t.relatedTarget;if(~DayPilot.indexOf(n.children,a))return}if(!i||0===i.length)return void(e.active=null);DayPilot.de(i),e.active=null,e.areas=[],DayPilot.rfa(DayPilot.Areas.all,e),n.children=null}},DayPilot.Areas.hideAll=function(e){if(DayPilot.Areas.all&&0!==DayPilot.Areas.all.length)for(var t=0;t<DayPilot.Areas.all.length;t++)DayPilot.Areas.hideAreas(DayPilot.Areas.all[t],e)},DayPilot.Exception=function(e){return new Error(e)},DayPilot.Locale=function(e,t){if(this.id=e,this.dayNames=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],this.dayNamesShort=["Su","Mo","Tu","We","Th","Fr","Sa"],this.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"],this.datePattern="M/d/yyyy",this.timePattern="H:mm",this.dateTimePattern="M/d/yyyy H:mm",this.timeFormat="Clock12Hours",this.weekStarts=0,t)for(var n in t)this[n]=t[n]},DayPilot.Locale.all={},DayPilot.Locale.find=function(e){if(!e)return null;var t=e.toLowerCase();return t.length>2&&(t=DayPilot.Util.replaceCharAt(t,2,"-")),DayPilot.Locale.all[t]},DayPilot.Locale.register=function(e){DayPilot.Locale.all[e.id]=e},DayPilot.Locale.register(new DayPilot.Locale("ca-es",{"dayNames":["diumenge","dilluns","dimarts","dimecres","dijous","divendres","dissabte"],"dayNamesShort":["dg","dl","dt","dc","dj","dv","ds"],"monthNames":["gener","febrer","març","abril","maig","juny","juliol","agost","setembre","octubre","novembre","desembre",""],"monthNamesShort":["gen.","febr.","març","abr.","maig","juny","jul.","ag.","set.","oct.","nov.","des.",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("cs-cz",{"dayNames":["neděle","pondělí","úterý","středa","čtvrtek","pátek","sobota"],"dayNamesShort":["ne","po","út","st","čt","pá","so"],"monthNames":["leden","únor","březen","duben","květen","červen","červenec","srpen","září","říjen","listopad","prosinec",""],"monthNamesShort":["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII",""],"timePattern":"H:mm","datePattern":"d. M. yyyy","dateTimePattern":"d. M. yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("da-dk",{"dayNames":["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],"dayNamesShort":["sø","ma","ti","on","to","fr","lø"],"monthNames":["januar","februar","marts","april","maj","juni","juli","august","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"dd-MM-yyyy","dateTimePattern":"dd-MM-yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-at",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Jänner","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jän","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-ch",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-de",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("de-lu",{"dayNames":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"dayNamesShort":["So","Mo","Di","Mi","Do","Fr","Sa"],"monthNames":["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember",""],"monthNamesShort":["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-au",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"d/MM/yyyy","dateTimePattern":"d/MM/yyyy h:mm tt","timeFormat":"Clock12Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-ca",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd h:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("en-gb",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("en-us",{"dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Su","Mo","Tu","We","Th","Fr","Sa"],"monthNames":["January","February","March","April","May","June","July","August","September","October","November","December",""],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],"timePattern":"h:mm tt","datePattern":"M/d/yyyy","dateTimePattern":"M/d/yyyy h:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("es-es",{"dayNames":["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],"dayNamesShort":["D","L","M","X","J","V","S"],"monthNames":["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre",""],"monthNamesShort":["ene.","feb.","mar.","abr.","may.","jun.","jul.","ago.","sep.","oct.","nov.","dic.",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("es-mx",{"dayNames":["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],"dayNamesShort":["do.","lu.","ma.","mi.","ju.","vi.","sá."],"monthNames":["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre",""],"monthNamesShort":["ene.","feb.","mar.","abr.","may.","jun.","jul.","ago.","sep.","oct.","nov.","dic.",""],"timePattern":"hh:mm tt","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy hh:mm tt","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("eu-es",{"dayNames":["igandea","astelehena","asteartea","asteazkena","osteguna","ostirala","larunbata"],"dayNamesShort":["ig","al","as","az","og","or","lr"],"monthNames":["urtarrila","otsaila","martxoa","apirila","maiatza","ekaina","uztaila","abuztua","iraila","urria","azaroa","abendua",""],"monthNamesShort":["urt.","ots.","mar.","api.","mai.","eka.","uzt.","abu.","ira.","urr.","aza.","abe.",""],"timePattern":"H:mm","datePattern":"yyyy/MM/dd","dateTimePattern":"yyyy/MM/dd H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fi-fi",{"dayNames":["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"],"dayNamesShort":["su","ma","ti","ke","to","pe","la"],"monthNames":["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kesäkuu","heinäkuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu",""],"monthNamesShort":["tammi","helmi","maalis","huhti","touko","kesä","heinä","elo","syys","loka","marras","joulu",""],"timePattern":"H:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-be",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd-MM-yy","dateTimePattern":"dd-MM-yy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-ca",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("fr-ch",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-fr",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("fr-lu",{"dayNames":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"dayNamesShort":["di","lu","ma","me","je","ve","sa"],"monthNames":["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre",""],"monthNamesShort":["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc.",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("gl-es",{"dayNames":["domingo","luns","martes","mércores","xoves","venres","sábado"],"dayNamesShort":["do","lu","ma","mé","xo","ve","sá"],"monthNames":["xaneiro","febreiro","marzo","abril","maio","xuño","xullo","agosto","setembro","outubro","novembro","decembro",""],"monthNamesShort":["xan","feb","mar","abr","maio","xuño","xul","ago","set","out","nov","dec",""],"timePattern":"H:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("it-it",{"dayNames":["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],"dayNamesShort":["do","lu","ma","me","gi","ve","sa"],"monthNames":["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre",""],"monthNamesShort":["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("it-ch",{"dayNames":["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],"dayNamesShort":["do","lu","ma","me","gi","ve","sa"],"monthNames":["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre",""],"monthNamesShort":["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("ja-jp",{"dayNames":["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"],"dayNamesShort":["日","月","火","水","木","金","土"],"monthNames":["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"H:mm","datePattern":"yyyy/MM/dd","dateTimePattern":"yyyy/MM/dd H:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("ko-kr",{"dayNames":["일요일","월요일","화요일","수요일","목요일","금요일","토요일"],"dayNamesShort":["일","월","화","수","목","금","토"],"monthNames":["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"tt h:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd tt h:mm","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("nb-no",{"dayNames":["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],"dayNamesShort":["sø","ma","ti","on","to","fr","lø"],"monthNames":["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember",""],"monthNamesShort":["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nl-nl",{"dayNames":["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],"dayNamesShort":["zo","ma","di","wo","do","vr","za"],"monthNames":["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"d-M-yyyy","dateTimePattern":"d-M-yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nl-be",{"dayNames":["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],"dayNamesShort":["zo","ma","di","wo","do","vr","za"],"monthNames":["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"H:mm","datePattern":"d/MM/yyyy","dateTimePattern":"d/MM/yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("nn-no",{"dayNames":["søndag","måndag","tysdag","onsdag","torsdag","fredag","laurdag"],"dayNamesShort":["sø","må","ty","on","to","fr","la"],"monthNames":["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember",""],"monthNamesShort":["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des",""],"timePattern":"HH:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("pt-br",{"dayNames":["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],"dayNamesShort":["D","S","T","Q","Q","S","S"],"monthNames":["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro",""],"monthNamesShort":["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("pl-pl",{"dayNames":["niedziela","poniedziałek","wtorek","środa","czwartek","piątek","sobota"],"dayNamesShort":["N","Pn","Wt","Śr","Cz","Pt","So"],"monthNames":["styczeń","luty","marzec","kwiecień","maj","czerwiec","lipiec","sierpień","wrzesień","październik","listopad","grudzień",""],"monthNamesShort":["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","paź","lis","gru",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("pt-pt",{"dayNames":["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],"dayNamesShort":["D","S","T","Q","Q","S","S"],"monthNames":["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro",""],"monthNamesShort":["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez",""],"timePattern":"HH:mm","datePattern":"dd/MM/yyyy","dateTimePattern":"dd/MM/yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":0})),DayPilot.Locale.register(new DayPilot.Locale("ro-ro",{"dayNames":["duminică","luni","marți","miercuri","joi","vineri","sâmbătă"],"dayNamesShort":["D","L","Ma","Mi","J","V","S"],"monthNames":["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie",""],"monthNamesShort":["ian.","feb.","mar.","apr.","mai.","iun.","iul.","aug.","sep.","oct.","nov.","dec.",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("ru-ru",{"dayNames":["воскресенье","понедельник","вторник","среда","четверг","пятница","суббота"],"dayNamesShort":["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],"monthNames":["Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь",""],"monthNamesShort":["янв","фев","мар","апр","май","июн","июл","авг","сен","окт","ноя","дек",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("sk-sk",{"dayNames":["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"],"dayNamesShort":["ne","po","ut","st","št","pi","so"],"monthNames":["január","február","marec","apríl","máj","jún","júl","august","september","október","november","december",""],"monthNamesShort":["1","2","3","4","5","6","7","8","9","10","11","12",""],"timePattern":"H:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("sv-se",{"dayNames":["söndag","måndag","tisdag","onsdag","torsdag","fredag","lördag"],"dayNamesShort":["sö","må","ti","on","to","fr","lö"],"monthNames":["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december",""],"monthNamesShort":["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec",""],"timePattern":"HH:mm","datePattern":"yyyy-MM-dd","dateTimePattern":"yyyy-MM-dd HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("tr-tr",{"dayNames":["Pazar","Pazartesi","Salı","Çarşamba","Perşembe","Cuma","Cumartesi"],"dayNamesShort":["Pz","Pt","Sa","Ça","Pe","Cu","Ct"],"monthNames":["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık",""],"monthNamesShort":["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara",""],"timePattern":"HH:mm","datePattern":"d.M.yyyy","dateTimePattern":"d.M.yyyy HH:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("uk-ua",{"dayNames":["неділя","понеділок","вівторок","середа","четвер","п'ятниця","субота"],"dayNamesShort":["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],"monthNames":["січень","лютий","березень","квітень","травень","червень","липень","серпень","вересень","жовтень","листопад","грудень",""],"monthNamesShort":["Січ","Лют","Бер","Кві","Тра","Чер","Лип","Сер","Вер","Жов","Лис","Гру",""],"timePattern":"H:mm","datePattern":"dd.MM.yyyy","dateTimePattern":"dd.MM.yyyy H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("zh-cn",{"dayNames":["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],"dayNamesShort":["日","一","二","三","四","五","六"],"monthNames":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"monthNamesShort":["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月",""],"timePattern":"H:mm","datePattern":"yyyy/M/d","dateTimePattern":"yyyy/M/d H:mm","timeFormat":"Clock24Hours","weekStarts":1})),DayPilot.Locale.register(new DayPilot.Locale("zh-tw",{"dayNames":["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],"dayNamesShort":["日","一","二","三","四","五","六"],"monthNames":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"monthNamesShort":["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月",""],"timePattern":"tt hh:mm","datePattern":"yyyy/M/d","dateTimePattern":"yyyy/M/d tt hh:mm","timeFormat":"Clock12Hours","weekStarts":0})),DayPilot.Locale.US=DayPilot.Locale.find("en-us"),DayPilot.Switcher=function(e){function t(e,t,i){var a={};a.start=e,a.end=t,a.day=i,a.target=n.l.control,a.preventDefault=function(){this.preventDefault.value=!0};var o=n.I;o&&o.start===a.start&&o.end===a.end&&o.day===a.day&&o.target===a.target||(n.I=a,"function"==typeof n.onChange&&(n.onChange(a),a.preventDefault.value)||"function"==typeof n.onTimeRangeSelect&&(n.onTimeRangeSelect(a),a.preventDefault.value)||(n.l.t(n.m),"function"==typeof n.onChanged&&n.onChanged(a),"function"==typeof n.onTimeRangeSelected&&n.onTimeRangeSelected(a)))}var n=this;this.f=[],this.j=[],this.k={},this.selectedClass=null,this.syncScrollbar=!0,this.l=null,this.m=DayPilot.Date.today(),this.n=null,this.onChange=null,this.onChanged=null,this.onSelect=null,this.k.updateMode=function(e){var t=n.k.control;t&&(t.selectMode=e,t.select(n.m))},this.addView=function(e,t){var i;if("string"==typeof e){if(i=document.getElementById(e),!i)throw"Element not found: "+e}else i=e;var a=i,o={};return o.o=!0,o.p=a.id,o.control=a,o.q=t||{},o.s=function(){a.hide?a.hide():a.nav&&a.nav.top?a.nav.top.style.display="none":a.style.display="none"},o.t=function(e){(function(){return!!a.backendUrl||!("function"!=typeof WebForm_DoCallback||!a.uniqueID)})()?a.commandCallBack&&a.commandCallBack("navigate",{"day":e}):(a.startDate=e,a.update())},o.u=function(){n.z(),a.show?a.show():a.nav&&a.nav.top?a.nav.top.style.display="":a.style.display=""},o.A=function(){if(o.q.navigatorSelectMode)return o.q.navigatorSelectMode;if(a.isCalendar)switch(a.viewType){case"Day":return"day";case"Week":return"week";case"WorkWeek":return"week";default:return"day"}else if(a.isMonth)switch(a.viewType){case"Month":return"month";case"Weeks":return"week";default:return"day"}return"day"},this.f.push(o),o},this.addTrigger=function(e,t){var i;if("string"==typeof e){if(i=document.getElementById(e),!i)throw"Element not found: "+e}else i=e;var a=this.B(t);a||(a=this.addView(t));var o={};return o.C=!0,o.D=i,o.p=i.id,o.E=a,o.F=function(e){n.show(o),n.G(o),e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},DayPilot.re(i,"click",o.F),this.j.push(o),o},this.addButton=this.addTrigger,this.select=function(e){var t=this.H(e);t?t.F():this.j.length>0&&this.j[0].F()},this.H=function(e){for(var t=0;t<this.j.length;t++){var n=this.j[t];if(n.p===e)return n}return null},this.G=function(e){if(this.selectedClass){for(var t=0;t<this.j.length;t++){var n=this.j[t];DayPilot.Util.removeClass(n.D,this.selectedClass)}DayPilot.Util.addClass(e.D,this.selectedClass)}},this.addNavigator=function(e){n.k.control=e,e.timeRangeSelectedHandling="JavaScript",e.onTimeRangeSelected=function(){var i,a,o;if(1===e.api)i=arguments[0],a=arguments[1],o=arguments[2];else{var r=arguments[0];i=r.start,a=r.end,o=r.day}n.m=o,t(i,a,o)}},this.show=function(e){var i,a;if(e.C)a=e,i=a.E;else if(i=e.o?e:this.B(e),this.l===i)return;if(n.onSelect){var o={};o.source=a?a.D:null,o.target=i.control,n.onSelect(o)}if(n.syncScrollbar){var r=this.l&&this.l.control;r&&r.isCalendar&&(n.n=r.getScrollY())}this.l=i,i.u(),null!==n.n&&i.control.isCalendar&&i.control.setScrollY(n.n);var s=i.A();n.k.updateMode(s),t(n.k.control.selectionStart,n.k.control.selectionEnd.addDays(1),n.k.control.selectionDay)},this.B=function(e){for(var t=0;t<this.f.length;t++)if(this.f[t].control===e)return this.f[t];return null},this.z=function(){for(var e=0;e<this.f.length;e++)this.f[e].s()},Object.defineProperty(this,"active",{get:function(){return n.l}}),this.events={},this.events.load=function(e,t,i){if(!n.l||!n.l.control)throw"DayPilot.Switcher.events.load(): Active view not found";n.l.control.events.load(e,t,i)},this.I=null,this.J=function(){if(e)for(var t in e)if("triggers"===t){var i=e.triggers||[];i.forEach(function(e){n.addTrigger(e.id,e.view)})}else"navigator"===t?n.addNavigator(e.navigator):n[t]=e[t]},this.J()},DayPilot.Duration=function(e){var t=this,n=864e5,i=36e5,a=6e4,o=1e3;if(2===arguments.length){var r=arguments[0],s=arguments[1];if(!(r instanceof DayPilot.Date)&&"string"!=typeof r)throw"DayPilot.Duration(): Invalid start argument, DayPilot.Date expected";if(!(s instanceof DayPilot.Date)&&"string"!=typeof s)throw"DayPilot.Duration(): Invalid end argument, DayPilot.Date expected";"string"==typeof r&&(r=new DayPilot.Date(r)),"string"==typeof s&&(s=new DayPilot.Date(s)),e=s.getTime()-r.getTime()}return this.ticks=e,DayPilot.Date.Cache.DurationCtor[""+e]?DayPilot.Date.Cache.DurationCtor[""+e]:(DayPilot.Date.Cache.DurationCtor[""+e]=this,this.toString=function(e){if(!e)return t.days()+"."+t.hours()+":"+t.minutes()+":"+t.seconds()+"."+t.milliseconds();var n=t.minutes();n=(n<10?"0":"")+n;var i=e;return i=i.replace("mm",n),i=i.replace("m",t.minutes()),i=i.replace("H",t.hours()),i=i.replace("h",t.hours()),i=i.replace("d",t.days()),i=i.replace("s",t.seconds())},this.totalHours=function(){return t.ticks/i},this.totalDays=function(){return t.ticks/n},this.totalMinutes=function(){return t.ticks/a},this.totalSeconds=function(){return t.ticks/o},this.days=function(){
return Math.floor(t.totalDays())},this.hours=function(){var e=t.ticks-t.days()*n;return Math.floor(e/i)},this.minutes=function(){var e=t.ticks-Math.floor(t.totalHours())*i;return Math.floor(e/a)},this.seconds=function(){var e=t.ticks-Math.floor(t.totalMinutes())*a;return Math.floor(e/o)},void(this.milliseconds=function(){return t.ticks%o}))},DayPilot.Duration.weeks=function(e){return new DayPilot.Duration(1e3*e*60*60*24*7)},DayPilot.Duration.days=function(e){return new DayPilot.Duration(1e3*e*60*60*24)},DayPilot.Duration.hours=function(e){return new DayPilot.Duration(1e3*e*60*60)},DayPilot.Duration.minutes=function(e){return new DayPilot.Duration(1e3*e*60)},DayPilot.Duration.seconds=function(e){return new DayPilot.Duration(1e3*e)},DayPilot.TimeSpan=function(){throw"Please use DayPilot.Duration class instead of DayPilot.TimeSpan."};try{DayPilot.TimeSpan.prototype=Object.create(DayPilot.Duration.prototype)}catch(e){}DayPilot.Date=function(t,n){if(t instanceof DayPilot.Date)return t;var i;DayPilot.Util.isNullOrUndefined(t)&&(i=DayPilot.DateUtil.fromLocal().getTime(),t=i);var a=DayPilot.Date.Cache.Ctor;if(a[t])return DayPilot.Stats.cacheHitsCtor+=1,a[t];var o=!1;if("string"==typeof t||t instanceof String)i=DayPilot.DateUtil.fromStringSortable(t,n).getTime(),o=!0;else if("number"==typeof t||t instanceof Number){if(isNaN(t))throw"Cannot create DayPilot.Date from NaN";i=t}else{if(!(t instanceof Date))throw"Unrecognized parameter: use Date, number or string in ISO 8601 format";i=n?DayPilot.DateUtil.fromLocal(t).getTime():t.getTime()}var r=e(i);return a[r]?a[r]:(a[r]=this,a[i]=this,o&&r!==t&&DayPilot.DateUtil.hasTzSpec(t)&&(a[t]=this),Object.defineProperty?(Object.defineProperty(this,"ticks",{get:function(){return i}}),Object.defineProperty(this,"value",{"value":r,"writable":!1,"enumerable":!0})):(this.ticks=i,this.value=r),DayPilot.Date.Config.legacyShowD&&(this.d=new Date(i)),void(DayPilot.Stats.dateObjects+=1))},DayPilot.Date.Config={},DayPilot.Date.Config.legacyShowD=!1,DayPilot.Date.Cache={},DayPilot.Date.Cache.Parsing={},DayPilot.Date.Cache.Ctor={},DayPilot.Date.Cache.Ticks={},DayPilot.Date.Cache.DurationCtor={},DayPilot.Date.Cache.clear=function(){DayPilot.Date.Cache.Parsing={},DayPilot.Date.Cache.Ctor={},DayPilot.Date.Cache.Ticks={},DayPilot.Date.Cache.DurationCtor={}},DayPilot.Date.prototype.addDays=function(e){return e?new DayPilot.Date(this.ticks+24*e*60*60*1e3):this},DayPilot.Date.prototype.addHours=function(e){return e?this.addTime(60*e*60*1e3):this},DayPilot.Date.prototype.addMilliseconds=function(e){return e?this.addTime(e):this},DayPilot.Date.prototype.addMinutes=function(e){return e?this.addTime(60*e*1e3):this},DayPilot.Date.prototype.addMonths=function(e){if(!e)return this;var t=new Date(this.ticks),n=t.getUTCFullYear(),i=t.getUTCMonth()+1;if(e>0){for(;e>=12;)e-=12,n++;e>12-i?(n++,i=e-(12-i)):i+=e}else{for(;e<=-12;)e+=12,n--;i+e<=0?(n--,i=12+i+e):i+=e}var a=new Date(t.getTime());a.setUTCDate(1),a.setUTCFullYear(n),a.setUTCMonth(i-1);var o=new DayPilot.Date(a).daysInMonth();return a.setUTCDate(Math.min(o,t.getUTCDate())),new DayPilot.Date(a)},DayPilot.Date.prototype.addSeconds=function(e){return e?this.addTime(1e3*e):this},DayPilot.Date.prototype.addTime=function(e){return e?(e instanceof DayPilot.Duration&&(e=e.ticks),new DayPilot.Date(this.ticks+e)):this},DayPilot.Date.prototype.addYears=function(e){var t=new Date(this.ticks),n=new Date(this.ticks),i=this.getYear()+e,a=this.getMonth();n.setUTCDate(1),n.setUTCFullYear(i),n.setUTCMonth(a);var o=new DayPilot.Date(n).daysInMonth();return n.setUTCDate(Math.min(o,t.getUTCDate())),new DayPilot.Date(n)},DayPilot.Date.prototype.dayOfWeek=function(){return new Date(this.ticks).getUTCDay()},DayPilot.Date.prototype.getDayOfWeek=function(){return new Date(this.ticks).getUTCDay()},DayPilot.Date.prototype.getDayOfYear=function(){var e=this.firstDayOfYear();return DayPilot.DateUtil.daysDiff(e,this)+1},DayPilot.Date.prototype.daysInMonth=function(){var e=new Date(this.ticks),t=e.getUTCMonth()+1,n=e.getUTCFullYear(),i=[31,28,31,30,31,30,31,31,30,31,30,31];return 2!==t?i[t-1]:n%4!==0?i[1]:n%100===0&&n%400!==0?i[1]:i[1]+1},DayPilot.Date.prototype.daysInYear=function(){var e=this.getYear();return e%4!==0?365:e%100===0&&e%400!==0?365:366},DayPilot.Date.prototype.dayOfYear=function(){return Math.ceil((this.getDatePart().getTime()-this.firstDayOfYear().getTime())/864e5)+1},DayPilot.Date.prototype.equals=function(e){if(null===e)return!1;if(e instanceof DayPilot.Date)return this===e;throw"The parameter must be a DayPilot.Date object (DayPilot.Date.equals())"},DayPilot.Date.prototype.firstDayOfMonth=function(){var e=new Date;return e.setUTCFullYear(this.getYear(),this.getMonth(),1),e.setUTCHours(0),e.setUTCMinutes(0),e.setUTCSeconds(0),e.setUTCMilliseconds(0),new DayPilot.Date(e)},DayPilot.Date.prototype.firstDayOfYear=function(){var e=this.getYear(),t=new Date;return t.setUTCFullYear(e,0,1),t.setUTCHours(0),t.setUTCMinutes(0),t.setUTCSeconds(0),t.setUTCMilliseconds(0),new DayPilot.Date(t)},DayPilot.Date.prototype.firstDayOfWeek=function(e){var t=this;if(e instanceof DayPilot.Locale)e=e.weekStarts;else if("string"==typeof e&&DayPilot.Locale.find(e)){var n=DayPilot.Locale.find(e);e=n.weekStarts}else e=e||0;for(var i=t.dayOfWeek();i!==e;)t=t.addDays(-1),i=t.dayOfWeek();return new DayPilot.Date(t)},DayPilot.Date.prototype.getDay=function(){return new Date(this.ticks).getUTCDate()},DayPilot.Date.prototype.getDatePart=function(){var e=new Date(this.ticks);return e.setUTCHours(0),e.setUTCMinutes(0),e.setUTCSeconds(0),e.setUTCMilliseconds(0),new DayPilot.Date(e)},DayPilot.Date.prototype.getYear=function(){return new Date(this.ticks).getUTCFullYear()},DayPilot.Date.prototype.getHours=function(){return new Date(this.ticks).getUTCHours()},DayPilot.Date.prototype.getMilliseconds=function(){return new Date(this.ticks).getUTCMilliseconds()},DayPilot.Date.prototype.getMinutes=function(){return new Date(this.ticks).getUTCMinutes()},DayPilot.Date.prototype.getMonth=function(){return new Date(this.ticks).getUTCMonth()},DayPilot.Date.prototype.getSeconds=function(){return new Date(this.ticks).getUTCSeconds()},DayPilot.Date.prototype.getTotalTicks=function(){return this.getTime()},DayPilot.Date.prototype.getTime=function(){return this.ticks},DayPilot.Date.prototype.getTimePart=function(){var e=this.getDatePart();return DayPilot.DateUtil.diff(this,e)},DayPilot.Date.prototype.lastDayOfMonth=function(){var e=new Date(this.firstDayOfMonth().getTime()),t=this.daysInMonth();return e.setUTCDate(t),new DayPilot.Date(e)},DayPilot.Date.prototype.weekNumber=function(){var e=this.firstDayOfYear(),t=(this.getTime()-e.getTime())/864e5;return Math.ceil((t+e.dayOfWeek()+1)/7)},DayPilot.Date.prototype.weekNumberISO=function(){var e=!1,t=this.dayOfYear(),n=this.firstDayOfYear().dayOfWeek(),i=this.firstDayOfYear().addYears(1).addDays(-1).dayOfWeek();0===n&&(n=7),0===i&&(i=7);var a=8-n;4!==n&&4!==i||(e=!0);var o=Math.ceil((t-a)/7),r=o;return a>=4&&(r+=1),r>52&&!e&&(r=1),0===r&&(r=this.firstDayOfYear().addDays(-1).weekNumberISO()),r},DayPilot.Date.prototype.toDateLocal=function(){var e=new Date(this.ticks),t=new Date;return t.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),t.setHours(e.getUTCHours()),t.setMinutes(e.getUTCMinutes()),t.setSeconds(e.getUTCSeconds()),t.setMilliseconds(e.getUTCMilliseconds()),t},DayPilot.Date.prototype.toDate=function(){return new Date(this.ticks)},DayPilot.Date.prototype.toJSON=function(){return this.value},DayPilot.Date.prototype.toString=function(e,t){return e?new a(e,t).print(this):this.toStringSortable()},DayPilot.Date.prototype.toStringSortable=function(){return e(this.ticks)},DayPilot.Date.parse=function(e,t,n){return new a(t,n).parse(e)};DayPilot.Date.today=function(){return new DayPilot.Date(DayPilot.DateUtil.localToday(),!0)},DayPilot.Date.now=function(){return new DayPilot.Date},DayPilot.Date.fromYearMonthDay=function(e,t,n){t=t||1,n=n||1;var i=new Date(0);return i.setUTCFullYear(e),i.setUTCMonth(t-1),i.setUTCDate(n),new DayPilot.Date(i)},DayPilot.DateUtil={},DayPilot.DateUtil.fromStringSortable=function(e,t){if(!e)throw"Can't create DayPilot.Date from an empty string";var n=e.length,i=10===n,a=19===n,o=n>19;if(!i&&!a&&!o)throw"Invalid string format (use '2010-01-01' or '2010-01-01T00:00:00'): "+e;if(DayPilot.Date.Cache.Parsing[e]&&!t)return DayPilot.Stats.cacheHitsParsing+=1,DayPilot.Date.Cache.Parsing[e];var r=e.substring(0,4),s=e.substring(5,7),l=e.substring(8,10),d=new Date(0);if(d.setUTCFullYear(r,s-1,l),i)return DayPilot.Date.Cache.Parsing[e]=d,d;var c=e.substring(11,13),u=e.substring(14,16),h=e.substring(17,19);if(d.setUTCHours(c),d.setUTCMinutes(u),d.setUTCSeconds(h),a)return DayPilot.Date.Cache.Parsing[e]=d,d;var f=e[19],v=0;if("."===f){var p=parseInt(e.substring(20,23));d.setUTCMilliseconds(p),v=DayPilot.DateUtil.getTzOffsetMinutes(e.substring(23))}else v=DayPilot.DateUtil.getTzOffsetMinutes(e.substring(19));var m=new DayPilot.Date(d);return t||(m=m.addMinutes(-v)),d=m.toDate(),DayPilot.Date.Cache.Parsing[e]=d,d},DayPilot.DateUtil.getTzOffsetMinutes=function(e){if(DayPilot.Util.isNullOrUndefined(e)||""===e)return 0;if("Z"===e)return 0;var t=e[0],n=parseInt(e.substring(1,3)),i=parseInt(e.substring(4)),a=60*n+i;if("-"===t)return-a;if("+"===t)return a;throw"Invalid timezone spec: "+e},DayPilot.DateUtil.hasTzSpec=function(e){return!!e.indexOf("+")||!!e.indexOf("-")},DayPilot.DateUtil.daysDiff=function(e,t){if(e&&t||function(){throw"two parameters required"}(),e=new DayPilot.Date(e),t=new DayPilot.Date(t),e.getTime()>t.getTime())return null;for(var n=0,i=e.getDatePart(),a=t.getDatePart();i<a;)i=i.addDays(1),n++;return n},DayPilot.DateUtil.daysSpan=function(e,t){if(e&&t||function(){throw"two parameters required"}(),e=new DayPilot.Date(e),t=new DayPilot.Date(t),e.getTime()===t.getTime())return 0;var n=DayPilot.DateUtil.daysDiff(e,t);return t.getTime()==t.getDatePart().getTime()&&n--,n},DayPilot.DateUtil.diff=function(e,t){if(!(e&&t&&e.getTime&&t.getTime))throw"Both compared objects must be Date objects (DayPilot.Date.diff).";return e.getTime()-t.getTime()},DayPilot.DateUtil.fromLocal=function(e){e||(e=new Date);var t=new Date;return t.setUTCFullYear(e.getFullYear(),e.getMonth(),e.getDate()),t.setUTCHours(e.getHours()),t.setUTCMinutes(e.getMinutes()),t.setUTCSeconds(e.getSeconds()),t.setUTCMilliseconds(e.getMilliseconds()),t},DayPilot.DateUtil.localToday=function(){var e=new Date;return e.setHours(0),e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),e},DayPilot.DateUtil.hours=function(e,t){var n=e.getUTCMinutes();n<10&&(n="0"+n);var i=e.getUTCHours();if(t){var a=i<12,i=i%12;0===i&&(i=12);return i+":"+n+" "+(a?"AM":"PM")}return i+":"+n},DayPilot.DateUtil.max=function(e,t){return e.getTime()>t.getTime()?e:t},DayPilot.DateUtil.min=function(e,t){return e.getTime()<t.getTime()?e:t};var a=function(e,n){"string"==typeof n&&(n=DayPilot.Locale.find(n));var n=n||DayPilot.Locale.US,i=[{"seq":"yyyy","expr":"[0-9]{4,4}","str":function(e){return e.getYear()}},{"seq":"yy","expr":"[0-9]{2,2}","str":function(e){return e.getYear()%100}},{"seq":"mm","expr":"[0-9]{2,2}","str":function(e){var t=e.getMinutes();return t<10?"0"+t:t}},{"seq":"m","expr":"[0-9]{1,2}","str":function(e){return e.getMinutes()}},{"seq":"HH","expr":"[0-9]{2,2}","str":function(e){var t=e.getHours();return t<10?"0"+t:t}},{"seq":"H","expr":"[0-9]{1,2}","str":function(e){return e.getHours()}},{"seq":"hh","expr":"[0-9]{2,2}","str":function(e){var t=e.getHours(),t=t%12;0===t&&(t=12);var n=t;return n<10?"0"+n:n}},{"seq":"h","expr":"[0-9]{1,2}","str":function(e){var t=e.getHours(),t=t%12;return 0===t&&(t=12),t}},{"seq":"ss","expr":"[0-9]{2,2}","str":function(e){var t=e.getSeconds();return t<10?"0"+t:t}},{"seq":"s","expr":"[0-9]{1,2}","str":function(e){return e.getSeconds()}},{"seq":"MMMM","expr":"[^\\s0-9]*","str":function(e){return n.monthNames[e.getMonth()]},"transform":function(e){var i=DayPilot.indexOf(n.monthNames,e,t);return i<0?null:i+1}},{"seq":"MMM","expr":"[^\\s0-9]*","str":function(e){return n.monthNamesShort[e.getMonth()]},"transform":function(e){var i=DayPilot.indexOf(n.monthNamesShort,e,t);return i<0?null:i+1}},{"seq":"MM","expr":"[0-9]{2,2}","str":function(e){var t=e.getMonth()+1;return t<10?"0"+t:t}},{"seq":"M","expr":"[0-9]{1,2}","str":function(e){return e.getMonth()+1}},{"seq":"dddd","expr":"[^\\s0-9]*","str":function(e){return n.dayNames[e.getDayOfWeek()]}},{"seq":"ddd","expr":"[^\\s0-9]*","str":function(e){return n.dayNamesShort[e.getDayOfWeek()]}},{"seq":"dd","expr":"[0-9]{2,2}","str":function(e){var t=e.getDay();return t<10?"0"+t:t}},{"seq":"%d","expr":"[0-9]{1,2}","str":function(e){return e.getDay()}},{"seq":"d","expr":"[0-9]{1,2}","str":function(e){return e.getDay()}},{"seq":"tt","expr":"(AM|PM|am|pm)","str":function(e){return e.getHours()<12?"AM":"PM"},"transform":function(e){return e.toUpperCase()}}],a=function(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")};this.init=function(){this.year=this.findSequence("yyyy"),this.month=this.findSequence("MMMM")||this.findSequence("MMM")||this.findSequence("MM")||this.findSequence("M"),this.day=this.findSequence("dd")||this.findSequence("d"),this.hours=this.findSequence("HH")||this.findSequence("H"),this.minutes=this.findSequence("mm")||this.findSequence("m"),this.seconds=this.findSequence("ss")||this.findSequence("s"),this.ampm=this.findSequence("tt"),this.hours12=this.findSequence("hh")||this.findSequence("h")},this.findSequence=function(t){function n(e){return parseInt(e)}return e.indexOf(t)===-1?null:{"findValue":function(o){for(var r=a(e),s=null,l=0;l<i.length;l++){var d=(i[l].length,t===i[l].seq),c=i[l].expr;d&&(c="("+c+")",s=i[l].transform),r=r.replace(i[l].seq,c)}r="^"+r+"$";try{var u=new RegExp(r),h=u.exec(o);return h?(s=s||n)(h[1]):null}catch(e){throw"unable to create regex from: "+r}}}},this.print=function(t){for(var n=function(e){for(var t=0;t<i.length;t++)if(i[t]&&i[t].seq===e)return i[t];return null},a=e.length<=0,o=0,r=[];!a;){var s=e.substring(o),l=/%?(.)\1*/.exec(s);if(l&&l.length>0){var d=l[0],c=n(d);c?r.push(c):r.push(d),o+=d.length,a=e.length<=o}else a=!0}for(var u=0;u<r.length;u++){var h=r[u];"string"!=typeof h&&(r[u]=h.str(t))}return r.join("")},this.parse=function(e){var t=this.year.findValue(e);if(!t)return null;var n=this.month.findValue(e);if(DayPilot.Util.isNullOrUndefined(n))return null;if(n>12||n<1)return null;var i=this.day.findValue(e),a=DayPilot.Date.fromYearMonthDay(t,n).daysInMonth();if(i<1||i>a)return null;var o=this.hours?this.hours.findValue(e):0,r=this.minutes?this.minutes.findValue(e):0,s=this.seconds?this.seconds.findValue(e):0,l=this.ampm?this.ampm.findValue(e):null;if(this.ampm&&this.hours12){var d=this.hours12.findValue(e);if(d<1||d>12)return null;o="PM"===l?12===d?12:d+12:12===d?0:d}if(o<0||o>23)return null;if(r<0||r>59)return null;if(s<0||s>59)return null;var c=new Date;return c.setUTCFullYear(t,n-1,i),c.setUTCHours(o),c.setUTCMinutes(r),c.setUTCSeconds(s),c.setUTCMilliseconds(0),new DayPilot.Date(c)},this.init()};DayPilot.ColorUtil={},DayPilot.ColorUtil.hexToRgb=function(e){if(!/^#[0-9a-f]{6}$/i.test(e))throw new DayPilot.Exception("Invalid color, only full hex color string accepted, eg. '#ffaaff'.");return e=e.replace("#",""),{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16)}},DayPilot.ColorUtil.rgbToHex=function(e){return"#"+n(e.r)+n(e.g)+n(e.b)},DayPilot.ColorUtil.adjustLuminance=function(e,t){return{r:e.r+t,g:e.g+t,b:e.b+t}},DayPilot.ColorUtil.darker=function(e,t){var n="";9===e.length&&(n=e.slice(7,9),e=e.slice(0,7));var i=DayPilot.ColorUtil.hexToRgb(e);"number"!=typeof t&&(t=1);var a=17,o=Math.round(t*a),r=DayPilot.ColorUtil.adjustLuminance(i,-o);return DayPilot.ColorUtil.rgbToHex(r)+n},DayPilot.ColorUtil.lighter=function(e,t){return"number"!=typeof t&&(t=1),DayPilot.ColorUtil.darker(e,-t)},DayPilot.ColorUtil.pl=function(e){var t=DayPilot.ColorUtil.hexToRgb(e),n=t.r/255,i=t.g/255,a=t.b/255;return Math.sqrt(.299*n*n+.587*i*i+.114*a*a)},DayPilot.ColorUtil.contrasting=function(e,t,n){var i=DayPilot.ColorUtil.pl(e);return t=t||"#ffffff",n=n||"#000000",i>.5?n:t},DayPilot.Event=function(e,t,n){var i=this;this.calendar=t,this.data=e?e:{},this.part=n?n:{},"undefined"==typeof this.data.id&&(this.data.id=this.data.value);var a={},o=["id","text","start","end","resource"];this.isEvent=!0,this.temp=function(){if(a.dirty)return a;for(var e=0;e<o.length;e++)a[o[e]]=i.data[o[e]];return a.dirty=!0,a},this.copy=function(){for(var e={},t=0;t<o.length;t++)e[o[t]]=i.data[o[t]];return e},this.commit=function(){if(a.dirty){for(var e=0;e<o.length;e++)i.data[o[e]]=a[o[e]];a.dirty=!1}},this.dirty=function(){return a.dirty},this.id=function(e){return"undefined"==typeof e?i.data.id:void(this.temp().id=e)},this.value=function(e){return"undefined"==typeof e?i.id():void i.id(e)},this.text=function(e){return"undefined"==typeof e?i.data.text:(this.temp().text=e,void this.client.innerHTML(e))},this.start=function(e){return"undefined"==typeof e?new DayPilot.Date(i.data.start):void(this.temp().start=new DayPilot.Date(e))},this.end=function(e){return"undefined"==typeof e?new DayPilot.Date(i.data.end):void(this.temp().end=new DayPilot.Date(e))},this.resource=function(e){return"undefined"==typeof e?i.data.resource:void(this.temp().resource=e)},this.duration=function(){return new DayPilot.Duration(this.start(),this.end())},this.rawend=function(e){if("undefined"==typeof e)return t&&t.internal.adjustEndIn?t.internal.adjustEndIn(new DayPilot.Date(i.data.end)):new DayPilot.Date(i.data.end);throw new DayPilot.Exception("DayPilot.Event.rawend() is readonly")},this.partStart=function(){return new DayPilot.Date(this.part.start)},this.partEnd=function(){return new DayPilot.Date(this.part.end)},this.tag=function(e){var t=i.data.tag;if(!t)return null;if("undefined"==typeof e)return i.data.tag;for(var n=i.calendar.tagFields,a=-1,o=0;o<n.length;o++)e===n[o]&&(a=o);if(a===-1)throw"Field name not found.";return t[a]},this.client={},this.client.innerHTML=function(e){if("undefined"==typeof e){var t=i.cache||i.data,n=i.calendar&&i.calendar.internal&&i.calendar.internal.xssTextHtml;return n?n(t.text,t.html):DayPilot.Util.escapeTextHtml(t.text,t.html)}i.data.html=e},this.client.html=this.client.innerHTML,this.client.header=function(e){return"undefined"==typeof e?i.data.header:void(i.data.header=e)},this.client.cssClass=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.cssClass?i.cache.cssClass:i.data.cssClass:void(i.data.cssClass=e)},this.client.toolTip=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.toolTip?i.cache.toolTip:"undefined"!=typeof i.data.toolTip?i.data.toolTip:i.data.text:void(i.data.toolTip=e)},this.client.barVisible=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.barHidden?!i.cache.barHidden:i.calendar.durationBarVisible&&!i.data.barHidden:void(i.data.barHidden=!e)},this.client.backColor=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.backColor?i.cache.backColor:"undefined"!=typeof i.data.backColor?i.data.backColor:i.calendar.eventBackColor:void(i.data.backColor=e)},this.client.borderColor=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.borderColor?i.cache.borderColor:"undefined"!=typeof i.data.borderColor?i.data.borderColor:i.calendar.eventBorderColor:void(i.data.borderColor=e)},this.client.contextMenu=function(e){if("undefined"==typeof e){if(i.oContextMenu)return i.oContextMenu;i.cache?i.cache.contextMenu:i.data.contextMenu}else i.oContextMenu=e},this.client.moveEnabled=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.moveDisabled?!i.cache.moveDisabled:"Disabled"!==i.calendar.eventMoveHandling&&!i.data.moveDisabled:void(i.data.moveDisabled=!e)},this.client.resizeEnabled=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.resizeDisabled?!i.cache.resizeDisabled:"Disabled"!==i.calendar.eventResizeHandling&&!i.data.resizeDisabled:void(i.data.resizeDisabled=!e)},this.client.clickEnabled=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.clickDisabled?!i.cache.clickDisabled:"Disabled"!==i.calendar.eventClickHandling&&!i.data.clickDisabled:void(i.data.clickDisabled=!e)},this.client.rightClickEnabled=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.rightClickDisabled?!i.cache.rightClickDisabled:"Disabled"!==i.calendar.eventRightClickHandling&&!i.data.rightClickDisabled:void(i.data.rightClickDisabled=!e)},this.client.deleteEnabled=function(e){return"undefined"==typeof e?i.cache&&"undefined"!=typeof i.cache.deleteDisabled?!i.cache.deleteDisabled:"Disabled"!==i.calendar.eventDeleteHandling&&!i.data.deleteDisabled:void(i.data.deleteDisabled=!e)},this.toJSON=function(e){var t={};if(t.value=this.id(),t.id=this.id(),t.text=this.text(),t.start=this.start(),t.end=this.end(),t.tag={},i.calendar&&i.calendar.tagFields)for(var n=i.calendar.tagFields,a=0;a<n.length;a++)t.tag[n[a]]=this.tag(n[a]);return t}}}}(),DayPilot.JSON={},function(){function e(e){return e<10?"0"+e:e}function t(e){return r.lastIndex=0,r.test(e)?'"'+e.replace(r,function(e){var t=s[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function n(e,r){var s,l,d,c,u,h=i,f=r[e];switch(f&&"object"==typeof f&&"function"==typeof f.toJSON2?f=f.toJSON2(e):f&&"object"==typeof f&&"function"==typeof f.toJSON&&!f.ignoreToJSON&&(f=f.toJSON(e)),"function"==typeof o&&(f=o.call(r,e,f)),typeof f){case"string":return t(f);case"number":return isFinite(f)?String(f):"null";case"boolean":case"null":return String(f);case"object":if(!f)return"null";if(i+=a,u=[],"number"==typeof f.length&&!f.propertyIsEnumerable("length")){for(c=f.length,s=0;s<c;s+=1)u[s]=n(s,f)||"null";return d=0===u.length?"[]":i?"[\n"+i+u.join(",\n"+i)+"\n"+h+"]":"["+u.join(",")+"]",i=h,d}if(o&&"object"==typeof o)for(c=o.length,s=0;s<c;s+=1)l=o[s],"string"==typeof l&&(d=n(l,f),d&&u.push(t(l)+(i?": ":":")+d));else for(l in f)Object.hasOwnProperty.call(f,l)&&(d=n(l,f),d&&u.push(t(l)+(i?": ":":")+d));return d=0===u.length?"{}":i?"{\n"+i+u.join(",\n"+i)+"\n"+h+"}":"{"+u.join(",")+"}",i=h,d}}"function"!=typeof Date.prototype.toJSON2&&(Date.prototype.toJSON2=function(t){return this.getUTCFullYear()+"-"+e(this.getUTCMonth()+1)+"-"+e(this.getUTCDate())+"T"+e(this.getUTCHours())+":"+e(this.getUTCMinutes())+":"+e(this.getUTCSeconds())},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var i,a,o,r=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,s={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};"function"!=typeof DayPilot.JSON.stringify&&(DayPilot.JSON.stringify=function(e,t,r){var s;if(i="",a="","number"==typeof r)for(s=0;s<r;s+=1)a+=" ";else"string"==typeof r&&(a=r);if(o=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("JSON.stringify");return n("",{"":e})})}(),"undefined"==typeof DayPilot)var DayPilot={};if("undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(){var e=function(){};if("undefined"==typeof DayPilot.Calendar||!DayPilot.Calendar.events){var t={};t.selectedCells=[],t.topSelectedCell=null,t.bottomSelectedCell=null,t.column=null,t.firstSelected=null,t.firstMousePos=null,t.originalMouse=null,t.originalHeight=null,t.originalTop=null,t.resizing=null,t.globalHandlers=!1,t.moving=null,t.register=function(e){t.registered||(t.registered=[]);for(var n=t.registered,i=0;i<n.length;i++)if(n[i]===e)return;n.push(e)},t.unregister=function(e){var n=t.registered;if(n){var i=DayPilot.indexOf(n,e);i!==-1&&n.splice(i,1)}},t.getCellsAbove=function(e){for(var n=[],i=t.getColumn(e),a=e.parentNode,o=null;a&&o!==t.firstSelected;)for(o=a.getElementsByTagName("td")[i],n.push(o),a=a.previousSibling;a&&"TR"!==a.tagName;)a=a.previousSibling;return n},t.getCellsBelow=function(e){for(var n=[],i=t.getColumn(e),a=e.parentNode,o=null;a&&o!==t.firstSelected;)for(o=a.getElementsByTagName("td")[i],n.push(o),a=a.nextSibling;a&&"TR"!==a.tagName;)a=a.nextSibling;return n},t.getColumn=function(e){for(var t=0;e.previousSibling;)e=e.previousSibling,"TD"===e.tagName&&t++;return t},t.gUnload=function(e){if(t.registered)for(var n=t.registered,i=0;i<n.length;i++){var a=n[i];a.dispose(),t.unregister(a)}},t.gMouseUp=function(n){if(t.resizing){if(!t.resizingShadow)return t.resizing.style.cursor="default",document.body.style.cursor="default",t.resizing=null,void(DayPilot.Global.resizing=null);var i=t.resizing.event,a=t.resizingShadow.clientHeight,o=t.resizingShadow.offsetTop,r=t.resizing.dpBorder;t.deleteShadow(t.resizingShadow),t.resizingShadow=null,t.resizing.style.cursor="default",i.calendar.nav.top.style.cursor="auto",t.resizing.onclick=null,t.resizing=null,DayPilot.Global.resizing=null,i.calendar.K(i,a,o,r)}else if(t.moving){if(!t.movingShadow)return t.moving=null,DayPilot.Global.moving=null,void(document.body.style.cursor="default");var o=t.movingShadow.offsetTop,i=t.moving.event;t.deleteShadow(t.movingShadow),DayPilot.Util.removeClass(t.moving,i.calendar.L("_event_moving_source"));var s=t.movingShadow.column;t.moving=null,DayPilot.Global.moving=null,t.movingShadow=null,i.calendar.nav.top.style.cursor="auto",i.calendar.M(i,s,o,n)}else if(DayPilot.Global.selecting){var l=DayPilot.Global.selecting.calendar;l.N=DayPilot.Global.selecting,DayPilot.Global.selecting=null;var d=l.getSelection();l.O(d.start,d.end,d.resource),"Hold"!==l.timeRangeSelectedHandling&&"HoldForever"!==l.timeRangeSelectedHandling&&e()}else t.selecting=!1},t.deleteShadow=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},t.moveShadow=function(e){var n=t.movingShadow,i=n.parentNode;i.style.display="none",n.parentNode.removeChild(n),e.firstChild.appendChild(n),n.style.left="0px",i.style.display="",n.style.width=t.movingShadow.parentNode.offsetWidth+1+"px"};var n=DayPilot.Util.isVueVNode,i=DayPilot.Util.overlaps;t.Calendar=function(e,a){var o=!1;if(this instanceof t.Calendar&&!this.P&&(o=!0,this.P=!0),!o)throw"DayPilot.Calendar() is a constructor and must be called as 'var c = new DayPilot.Calendar(id);'";var r=this;this.uniqueID=null,this.isCalendar=!0,this.v="2025.3.696-lite",this.id=e,this.clientName=e,this.cache={},this.cache.pixels={},this.elements={},this.elements.events=[],this.elements.selection=[],this.nav={},this.afterRender=function(){},this.fasterDispose=!0,this.angularAutoApply=!1,this.api=2,this.businessBeginsHour=9,this.businessEndsHour=18,this.cellDuration=30,this.cellHeight=30,this.columnMarginLeft=0,this.columnMarginRight=5,this.columnsLoadMethod="GET",this.contextMenu=null,this.days=1,this.durationBarVisible=!0,this.eventBorderRadius=null,this.eventsLoadMethod="GET",this.headerDateFormat=null,this.headerHeight=30,this.headerTextWrappingEnabled=!1,this.height=300,this.heightSpec="BusinessHours",this.hideUntilInit=!0,this.hourWidth=60,this.initScrollPos="Auto",this.loadingLabelHtml=null,this.loadingLabelText="Loading...",this.loadingLabelVisible=!0,this.locale="en-us",this.snapToGrid=!0,this.showToolTip=!0,this.startDate=(new DayPilot.Date).getDatePart(),this.cssClassPrefix="calendar_default",this.theme=null,this.timeFormat="Auto",this.useEventBoxes="Always",this.viewType="Days",this.visible=!0,this.xssProtection="Enabled",this.headerClickHandling="Enabled",this.eventClickHandling="Enabled",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.eventMoveHandling="Update",this.eventDeleteHandling="Disabled",this.timeRangeSelectedHandling="Enabled",this.onBeforeCellRender=null,this.onBeforeEventRender=null,this.onBeforeHeaderRender=null,this.onEventClick=null,this.onEventClicked=null,this.onEventDelete=null,this.onEventDeleted=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onEventRightClick=null,this.onEventRightClicked=null,this.onHeaderClick=null,this.onHeaderClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.Q=!1,this.clearSelection=function(){t.topSelectedCell=null,t.bottomSelectedCell=null,this.R()},this.R=function(){DayPilot.de(r.elements.selection),r.elements.selection=[],r.nav.activeSelection=null},this.cleanSelection=this.clearSelection,this.S=function(e,t,n){var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.T();var a="JSON"+DayPilot.JSON.stringify(i);__doPostBack(r.uniqueID,a)},this.U=function(e,t,n){this.callbackTimeout&&window.clearTimeout(this.callbackTimeout),this.callbackTimeout=window.setTimeout(function(){r.loadingStart()},100);var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.T();var a="JSON"+DayPilot.JSON.stringify(i);this.backendUrl?DayPilot.request(this.backendUrl,this.V,a,this.ajaxError):"function"==typeof WebForm_DoCallback&&WebForm_DoCallback(this.uniqueID,a,this.W,this.clientName,this.onCallbackError,!0)},this.onCallbackError=function(e,t){alert("Error!\r\nResult: "+e+"\r\nContext:"+t)},this.dispose=function(){var e=r;e.Q||(e.Q=!0,clearInterval(e.X),e.Y(),e.nav.scroll.root=null,DayPilot.pu(e.nav.loading),e.Z(),e._(),e.nav.select=null,e.nav.cornerRight=null,e.nav.scrollable=null,e.nav.zoom=null,e.nav.loading=null,e.nav.header=null,e.nav.hourTable=null,e.nav.scrolltop=null,e.nav.scroll.onscroll=null,e.nav.scroll=null,e.nav.main=null,e.nav.message=null,e.nav.messageClose=null,e.nav.top=null,t.unregister(e))},this.disposed=function(){return this.Q},this.aa=function(){this.nav.top.dispose=this.dispose},this.V=function(e){r.W(e.responseText)},this.T=function(){var e={};return e.control="dpc",e.id=this.id,e.v=this.v,e.days=r.days,e.startDate=r.startDate,e.heightSpec=r.heightSpec,e.businessBeginsHour=r.businessBeginsHour,e.businessEndsHour=r.businessEndsHour,e.hashes=r.hashes,e.timeFormat=r.timeFormat,e.viewType=r.viewType,e.locale=r.locale,e},this.ba=function(e,t){for(var n=e.parentNode;n&&"TD"!==n.tagName;)n=n.parentNode;var i=r.eventBorderRadius;"number"==typeof i&&(i+="px");var a=document.createElement("div");a.setAttribute("unselectable","on"),a.style.position="absolute",a.style.width=e.offsetWidth+"px",a.style.height=e.offsetHeight+"px",a.style.left=e.offsetLeft+"px",a.style.top=e.offsetTop+"px",a.style.boxSizing="border-box",a.style.zIndex=101,a.className=r.L("_shadow");var o=document.createElement("div");return o.className=r.L("_shadow_inner"),i&&(o.style.borderRadius=i,a.style.borderRadius=i),a.appendChild(o),n.firstChild.appendChild(a),a},this.ca={},this.ca.locale=function(){var e=DayPilot.Locale.find(r.locale);return e?e:DayPilot.Locale.US},this.ca.timeFormat=function(){return"Auto"!==r.timeFormat?r.timeFormat:this.locale().timeFormat},this.ca.da=function(){return"Disabled"!==r.xssProtection},this.ca.ea=function(){if("Auto"===r.weekStarts){var e=s.locale();return e?e.weekStarts:0}return r.weekStarts||0},this.ca.fa=function(){var e=r.cellDuration;if(e<=1)return 1;if(e>=60)return 60;var t=[1,2,3,4,5,6,10,12,15,20,30,60],n=Math.floor(e);return Math.max.apply(null,t.filter(function(e){return e<=n}))};var s=this.ca;this.W=function(e,t){if(e&&0===e.indexOf("$$$")){if(!window.console)throw"Error received from the server side: "+e;return void console.log("Error received from the server side: "+e)}var e=JSON.parse(e);if(e.CallBackRedirect)return void(document.location.href=e.CallBackRedirect);if("None"===e.UpdateType)return r.loadingStop(),void r.u();if(r.Y(),"Full"===e.UpdateType&&(r.columns=e.Columns,r.days=e.Days,r.startDate=new DayPilot.Date(e.StartDate),r.heightSpec=e.HeightSpec?e.HeightSpec:r.heightSpec,r.businessBeginsHour=e.BusinessBeginsHour?e.BusinessBeginsHour:r.businessBeginsHour,r.businessEndsHour=e.BusinessEndsHour?e.BusinessEndsHour:r.businessEndsHour,r.headerDateFormat=e.HeaderDateFormat?e.HeaderDateFormat:r.headerDateFormat,r.viewType=e.ViewType,r.backColor=e.BackColor?e.BackColor:r.backColor,r.eventHeaderVisible=e.EventHeaderVisible?e.EventHeaderVisible:r.eventHeaderVisible,
r.timeFormat=e.TimeFormat?e.TimeFormat:r.timeFormat,r.locale=e.Locale?e.Locale:r.locale,r.ga()),e.Hashes)for(var n in e.Hashes)r.hashes[n]=e.Hashes[n];r.events.list=e.Events,r.ha(),r.ia(),"Full"===e.UpdateType&&(r.ja(),r.ka(),r.la(),r.ma()),r.u(),r.na(),r.clearSelection(),r.afterRender(e.CallBackData,!0),r.loadingStop()},this.oa=function(){return this.pa()/36e5},this.qa=function(){return this.businessBeginsHour>this.businessEndsHour?24-this.businessBeginsHour+this.businessEndsHour:this.businessEndsHour-this.businessBeginsHour},this.ra=function(){return this.pa()/(60*s.fa()*1e3)},this.pa=function(){var e=0;return e="BusinessHoursNoScroll"===this.heightSpec?this.qa():24,60*e*60*1e3},this.sa=function(){return"BusinessHoursNoScroll"===this.heightSpec?this.businessBeginsHour:0},this.ta=function(){return 2===r.api},this.eventClickCallBack=function(e,t){this.U("EventClick",t,e)},this.eventClickPostBack=function(e,t){this.S("EventClick",t,e)},this.va=function(e){var t=this,n=t.event;if(n.client.clickEnabled())if(r.ta()){var i={};if(i.e=n,i.originalEvent=e,i.meta=e.metaKey,i.ctrl=e.ctrlKey,i.control=r,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onEventClick&&(r.wa.apply(function(){r.onEventClick(i)}),i.preventDefault.value))return;switch(r.eventClickHandling){case"CallBack":r.eventClickCallBack(n);break;case"PostBack":r.eventClickPostBack(n);break;case"ContextMenu":var a=n.client.contextMenu();a?a.show(n):r.contextMenu&&r.contextMenu.show(n)}"function"==typeof r.onEventClicked&&r.wa.apply(function(){r.onEventClicked(i)})}else switch(r.eventClickHandling){case"PostBack":r.eventClickPostBack(n);break;case"CallBack":r.eventClickCallBack(n);break;case"JavaScript":r.onEventClick(n)}},this.xa=function(e){var t=this.event;if(e.stopPropagation&&e.stopPropagation(),!t.client.rightClickEnabled())return!1;var n={};if(n.e=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onEventRightClick&&(r.onEventRightClick(n),n.preventDefault.value))return!1;switch(r.eventRightClickHandling){case"ContextMenu":var i=t.client.contextMenu();i?i.show(t):r.contextMenu&&r.contextMenu.show(this.event)}return"function"==typeof r.onEventRightClicked&&r.onEventRightClicked(n),e.preventDefault&&e.preventDefault(),!1},this.eventDeleteCallBack=function(e,t){this.U("EventDelete",t,e)},this.eventDeletePostBack=function(e,t){this.S("EventDelete",t,e)},this.ya=function(e){if(r.ta()){var t={};if(t.e=e,t.control=r,t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onEventDelete&&(r.wa.apply(function(){r.onEventDelete(t)}),t.preventDefault.value))return;switch(r.eventDeleteHandling){case"CallBack":r.eventDeleteCallBack(e);break;case"PostBack":r.eventDeletePostBack(e);break;case"Update":r.events.remove(e)}"function"==typeof r.onEventDeleted&&r.wa.apply(function(){r.onEventDeleted(t)})}else switch(r.eventDeleteHandling){case"PostBack":r.eventDeletePostBack(e);break;case"CallBack":r.eventDeleteCallBack(e);break;case"JavaScript":r.onEventDelete(e)}},this.eventResizeCallBack=function(e,t,n,i){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=e,a.newStart=t,a.newEnd=n,this.U("EventResize",i,a)},this.eventResizePostBack=function(e,t,n,i){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=e,a.newStart=t,a.newEnd=n,this.S("EventResize",i,a)},this.K=function(e,t,n,i){var a=0,o=new Date,s=new Date;if("top"===i?(o=r.za(e,n-a),s=e.end()):"bottom"===i&&(o=e.start(),s=r.Aa(e,n+t-a)),r.ta()){var l={};if(l.e=e,l.control=r,l.newStart=o,l.newEnd=s,l.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onEventResize&&(r.wa.apply(function(){r.onEventResize(l)}),l.preventDefault.value))return;switch(r.eventResizeHandling){case"PostBack":r.eventResizePostBack(e,o,s);break;case"CallBack":r.eventResizeCallBack(e,o,s);break;case"Update":e.start(o),e.end(s),r.events.update(e)}"function"==typeof r.onEventResized&&r.wa.apply(function(){r.onEventResized(l)})}else switch(r.eventResizeHandling){case"PostBack":r.eventResizePostBack(e,o,s);break;case"CallBack":r.eventResizeCallBack(e,o,s);break;case"JavaScript":r.onEventResize(e,o,s)}},this.za=function(e,t){var n=r.Ba[e.part.dayIndex].start,i=Math.floor(t/r.cellHeight);r.snapToGrid||(i=t/r.cellHeight);var a=Math.floor(i*s.fa()),o=60*a*1e3,l=60*r.sa()*60*1e3;return n.addTime(o+l)},this.Aa=function(e,t){var n=Math.floor(t/r.cellHeight);r.snapToGrid||(n=t/r.cellHeight);var i=Math.floor(n*s.fa()),a=60*i*1e3,o=60*r.sa()*60*1e3;return r.Ba[e.part.dayIndex].start.addTime(a+o)},this.eventMovePostBack=function(e,t,n,i,a){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var o={};o.e=e,o.newStart=t,o.newEnd=n,this.S("EventMove",a,o)},this.eventMoveCallBack=function(e,t,n,i,a){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var o={};o.e=e,o.newStart=t,o.newEnd=n,this.U("EventMove",a,o)},this.M=function(e,t,n,i){var a=0,o=Math.floor((n-a)/r.cellHeight);r.snapToGrid||(o=(n-a)/r.cellHeight);var l=s.fa(),d=o*l*60*1e3,c=e.start(),u=e.end(),h=new Date;c instanceof DayPilot.Date&&(c=c.toDate()),h.setTime(Date.UTC(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()));var f=c.getTime()-(h.getTime()+3600*c.getUTCHours()*1e3+Math.floor(c.getUTCMinutes()/l)*l*60*1e3);"Never"===r.useEventBoxes&&(f=0);var v=u.getTime()-c.getTime(),p=this.Ba[t],m=p.id;r.snapToGrid||(f=0);var y=p.start.getTime(),g=new Date;g.setTime(y+d+f);var b=new DayPilot.Date(g),w=b.addTime(v);if(r.ta()){var D={};if(D.e=e,D.newStart=b,D.newEnd=w,D.newResource=m,D.ctrl=i.ctrlKey,D.shift=i.shiftKey,D.control=r,D.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onEventMove&&(r.wa.apply(function(){r.onEventMove(D)}),D.preventDefault.value))return;switch(r.eventMoveHandling){case"PostBack":r.eventMovePostBack(e,b,w,p.id);break;case"CallBack":r.eventMoveCallBack(e,b,w,p.id);break;case"Update":e.start(b),e.end(w),e.resource(m),r.events.update(e)}"function"==typeof r.onEventMoved&&r.wa.apply(function(){r.onEventMoved(D)})}else switch(r.eventMoveHandling){case"PostBack":r.eventMovePostBack(e,b,w,p.id);break;case"CallBack":r.eventMoveCallBack(e,b,w,p.id);break;case"JavaScript":r.onEventMove(e,b,w,p.id,!1)}},this.timeRangeSelectedPostBack=function(e,t,n,i){var a={};a.start=e,a.end=t,this.S("TimeRangeSelected",i,a)},this.timeRangeSelectedCallBack=function(e,t,n,i){var a={};a.start=e,a.end=t,this.U("TimeRangeSelected",i,a)},this.O=function(e,t,n){if(e=new DayPilot.Date(e),t=new DayPilot.Date(t),this.ta()){var i={};if(i.start=e,i.end=t,i.resource=n,i.control=r,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onTimeRangeSelect&&(r.wa.apply(function(){r.onTimeRangeSelect(i)}),i.preventDefault.value))return;switch(r.timeRangeSelectedHandling){case"PostBack":r.timeRangeSelectedPostBack(e,t);break;case"CallBack":r.timeRangeSelectedCallBack(e,t)}"function"==typeof r.onTimeRangeSelected&&r.wa.apply(function(){r.onTimeRangeSelected(i)})}else switch(r.timeRangeSelectedHandling){case"PostBack":r.timeRangeSelectedPostBack(e,t);break;case"CallBack":r.timeRangeSelectedCallBack(e,t);break;case"JavaScript":r.onTimeRangeSelected(e,t)}},this.Ca=function(e){if(!DayPilot.Global.selecting&&"Disabled"!==r.timeRangeSelectedHandling){var t=e.which;if(1===t||0===t){DayPilot.Global.selecting={calendar:r,start:r.coords};var n=DayPilot.Global.selecting;return n.start.time=r.Da(r.coords.x,r.coords.y),n.start.columnIndex=r.Ea(r.coords.x),n.start.column=r.Ba[n.start.columnIndex],r.Fa(),r.Ga(),!1}}},this.Ga=function(){var e=DayPilot.Global.selecting;!function(){var t=e.start.column,n=e.start.columnIndex,i=t.start,a=r.getPixels(e.startTime,i),o=r.getPixels(e.endTime,i),s=a.boxTop,l=o.boxBottom;r.snapToGrid||(s=a.top,l=o.top);var d=l-s,c=r.eventBorderRadius;"number"==typeof c&&(c+="px");var u=function(){if(r.nav.activeSelection)return r.nav.activeSelection;var e=document.createElement("div");e.setAttribute("unselectable","on"),e.style.position="absolute",e.style.left="0px",e.style.width="100%";var t=document.createElement("div");return t.setAttribute("unselectable","on"),t.className=r.L("_shadow_inner"),c&&(t.style.borderRadius=c,e.style.borderRadius=c),e.appendChild(t),r.nav.events.rows[0].cells[n].selection.appendChild(e),r.elements.selection.push(e),r.nav.activeSelection=e,e}();u.className=r.L("_shadow"),u.firstChild.innerHTML="",u.style.top=s+"px",u.style.height=d+"px",r.nav.events.rows[0].cells[n].selection.appendChild(u)}()},this.Fa=function(){var e=DayPilot.Global.selecting;e.end=r.coords,e.end.time=r.Da(e.start.x,e.end.y),e.end.column=r.Ba[r.Ea(e.end.x)],r.Ha&&(e.end.time=r.Da(e.end.x,e.end.y)),r.Ia(e)},this.Ia=function(e){var t,n,i;r.snapToGrid?e.end.time<e.start.time?(t=r.Ja(e.end.time,e.start.column.start),n=r.Ka(e.start.time,e.start.column.start),i=e.endTime):(t=r.Ja(e.start.time,e.start.column.start),n=r.Ka(e.end.time,e.start.column.start),i=e.startTime):e.end.time<e.start.time?(t=e.end.time,n=e.start.time,i=e.endTime):(t=e.start.time,n=e.end.time,i=e.startTime),e.startTime=t,e.endTime=n,e.anchor=i},this.getSelection=function(){if(!r.N)return null;var e=r.N;return new DayPilot.Selection(e.startTime,e.endTime,e.start.column.id,r)},this.Ea=function(e){if(e-=r.hourWidth,e<0)return null;for(var t=0,n=r.nav.events.rows[0].cells,i=0;i<n.length;i++){if(t+=n[i].offsetWidth,e<t)return i}return null},this.Da=function(e,t){e=DayPilot.Util.atLeast(e,0);var n=this.Ea(e),i=t/(60/s.fa())/r.cellHeight,a=60*i*60*1e3,o=6e4*Math.floor(a/6e4),l=this.Ba[n];return l?l.start.addTime(o):null},this.Ja=function(e,t){var n=e.getTime();t&&(n-=t.getTime());var i=60*s.fa()*1e3,a=n%i;return e.addTime(-a)},this.Ka=function(e,t){var n=this.Ja(e,t);return n.getTime()===e.getTime()?n:n.addTime(60*s.fa()*1e3)},this.La={},this.La.getCellCoords=function(){var e={};if(e.x=0,e.y=0,!r.coords)return null;e.x=r.Ea(r.coords.x);var t=0,n=Math.floor((r.coords.y-t)/r.cellHeight);return e.y=n,e.x<0?null:e},this.columns={},this.columns.list=[],this.columns.load=function(e,t,n){if(!e)throw new DayPilot.Exception("columns.load(): 'url' parameter required");var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var o={};return o.exception=e,void i(o)}if(DayPilot.isArray(n)){var s={};if(s.preventDefault=function(){this.preventDefault.value=!0},s.data=n,"function"==typeof t&&t(s),s.preventDefault.value)return;r.columns.list=n,r.Ma&&r.update()}};r.columnsLoadMethod&&"POST"===r.columnsLoadMethod.toUpperCase()?DayPilot.ajax({"method":"POST","url":e,"success":a,"error":i}):DayPilot.ajax({"method":"GET","url":e,"success":a,"error":i})},this.ga=function(){var e;e="Resources"!==r.viewType?this.Na():r.columns.list,this.Ba=[];for(var t=0;t<e.length;t++){var n=this.Oa(e[t]);this.Ba.push(n)}},this.Oa=function(e){var t={};if(t.name=e.name,t.html=e.html,t.id=e.id,t.toolTip=e.toolTip,t.data=e,e.start?t.start=new DayPilot.Date(e.start):t.start=new DayPilot.Date(r.startDate),"BusinessHoursNoScroll"===this.heightSpec){var n=t.start.getDatePart();t.start=n.addHours(this.businessBeginsHour)}return t.putIntoBlock=function(e){for(var t=0;t<this.blocks.length;t++){var n=this.blocks[t];if(n.overlapsWith(e.part.top,e.part.height))return n.events.push(e),n.min=Math.min(n.min,e.part.top),n.max=Math.max(n.max,e.part.top+e.part.height),t}var n=[];return n.lines=[],n.events=[],n.overlapsWith=function(e,t){return!(e+t-1<this.min||e>this.max-1)},n.putIntoLine=function(e){for(var t=0;t<this.lines.length;t++){var n=this.lines[t];if(n.isFree(e.part.top,e.part.height))return n.push(e),t}var n=[];return n.isFree=function(e,t){for(var n=e+t-1,i=this.length,a=0;a<i;a++){var o=this[a];if(!(n<o.part.top||e>o.part.top+o.part.height-1))return!1}return!0},n.push(e),this.lines.push(n),this.lines.length-1},n.events.push(e),n.min=e.part.top,n.max=e.part.top+e.part.height,this.blocks.push(n),this.blocks.length-1},t.putIntoLine=function(e){for(var t=0;t<this.lines.length;t++){var n=this.lines[t];if(n.isFree(e.part.top,e.part.height))return n.push(e),t}var n=[];return n.isFree=function(e,t){for(var n=e+t-1,i=this.length,a=0;a<i;a++){var o=this[a];if(!(n<o.part.top||e>o.part.top+o.part.height-1))return!1}return!0},n.push(e),this.lines.push(n),this.lines.length-1},t},this.Na=function(){var e=[],t=this.startDate.getDatePart(),n=this.days;switch(this.viewType){case"Day":n=1;break;case"Week":n=7;var i=s.ea();t=t.firstDayOfWeek(i);break;case"WorkWeek":n=5,t=t.firstDayOfWeek(1)}for(var a=0;a<n;a++){var o=r.headerDateFormat?r.headerDateFormat:s.locale().datePattern,l={};l.start=t.addDays(a),l.name=l.start.toString(o,s.locale()),e.push(l)}return e},this.visibleStart=function(){if("Resources"===r.viewType){if(0===r.Ba.length)return DayPilot.Date.today();var e=r.Ba.map(function(e){return e.start.getTime()}),t=Math.min.apply(null,e);return new DayPilot.Date(t)}return this.Ba[0].start},this.visibleEnd=function(){if("Resources"===r.viewType){if(0===r.Ba.length)return DayPilot.Date.today().addDays(1);var e=r.Ba.map(function(e){return e.start.getTime()}),t=Math.max.apply(null,e);return new DayPilot.Date(t).addDays(1)}var t=this.Ba.length-1;return this.Ba[t].start.addDays(1)},this.L=function(e){var t=this.theme||this.cssClassPrefix;return t?t+e:""},this.Y=function(){if(this.elements.events)for(var e=0;e<this.elements.events.length;e++){var t=this.elements.events[e];r.Pa(t)}this.elements.events=[]},this.Pa=function(e){!function(){var t=e.domArgs;if(e.domArgs=null,t&&"function"==typeof r.onBeforeEventDomRemove&&r.onBeforeEventDomRemove(t),t&&"function"==typeof r.onBeforeEventDomAdd){var i=t&&t.Qa;if(i){r.Sa.Ra&&n(t.element)&&(r.Sa.Ta=!0,r.Sa.Ua(i),r.Sa.Ta=!1)}}}();var t=e.event;if(t&&(t.calendar=null),e.onclick=null,e.onclickSave=null,e.onmouseover=null,e.onmouseout=null,e.onmousemove=null,e.onmousedown=null,e.firstChild&&e.firstChild.firstChild&&e.firstChild.firstChild.tagName&&"IMG"===e.firstChild.firstChild.tagName.toUpperCase()){var i=e.firstChild.firstChild;i.onmousedown=null,i.onmousemove=null,i.onclick=null}e.helper=null,e.data=null,e.event=null,DayPilot.de(e)},this.Va=function(e){var i=e.cache||e.data,a=this.nav.events,o=i.borderRadius||r.eventBorderRadius;"number"==typeof o&&(o+="px");var s=document.createElement("div");s.style.position="absolute",s.style.left=e.part.left+"%",s.style.top=e.part.top+"px",s.style.width=e.part.width+"%",s.style.height=Math.max(e.part.height,2)+"px",s.style.overflow="hidden",s.data=e,s.event=e,s.unselectable="on",s.style.MozUserSelect="none",s.style.KhtmlUserSelect="none",s.className=this.L("_event"),i.cssClass&&DayPilot.Util.addClass(s,i.cssClass),r.showToolTip&&e.client.toolTip()&&(s.title=e.client.toolTip()),s.isFirst=e.part.start.getTime()===e.start().getTime(),s.isLast=e.part.end.getTime()===e.end().getTime(),s.onclick=this.va,DayPilot.re(s,"contextmenu",this.xa),s.onmouseout=function(e){s.deleteIcon&&(s.deleteIcon.style.display="none")},s.onmousemove=function(n){var i=5;if("undefined"!=typeof t){var a=DayPilot.mo3(s,n);if(a&&!t.resizing&&!t.moving){s.deleteIcon&&(s.deleteIcon.style.display="");var o=this.isLast;a.y<=i&&e.client.resizeEnabled()?(this.style.cursor="n-resize",this.dpBorder="top"):this.offsetHeight-a.y<=i&&e.client.resizeEnabled()?o?(this.style.cursor="s-resize",this.dpBorder="bottom"):this.style.cursor="not-allowed":t.resizing||t.moving||("Disabled"!==r.eventClickHandling?this.style.cursor="pointer":this.style.cursor="default")}}},s.onmousedown=function(n){var i=n.which||n.button;if("n-resize"!==this.style.cursor&&"s-resize"!==this.style.cursor||1!==i){if(1===i&&e.client.moveEnabled()){t.moving=this,DayPilot.Global.moving=this,t.moving.event=this.event;var a=t.moving.helper={};a.oldColumn=r.Ba[this.data.part.dayIndex].id,t.originalMouse=DayPilot.mc(n),t.originalTop=this.offsetTop;var o=DayPilot.mo3(this,n);o?t.moveOffsetY=o.y:t.moveOffsetY=0,r.nav.top.style.cursor="move"}}else t.resizing=this,DayPilot.Global.resizing=this,t.originalMouse=DayPilot.mc(n),t.originalHeight=this.offsetHeight,t.originalTop=this.offsetTop,r.nav.top.style.cursor=this.style.cursor;return!1};var l=document.createElement("div");if(l.setAttribute("unselectable","on"),l.className=r.L("_event_inner"),"darker"===i.borderColor&&i.backColor?l.style.borderColor=DayPilot.ColorUtil.darker(i.backColor,2):l.style.borderColor=i.borderColor,i.backColor&&(l.style.background=i.backColor),i.fontColor&&(l.style.color=i.fontColor),o&&(s.style.borderRadius=o,l.style.borderRadius=o),s.appendChild(l),e.client.barVisible()){var d=e.part.height-2,c=100*e.part.barTop/d,u=Math.ceil(100*e.part.barHeight/d),h=document.createElement("div");h.setAttribute("unselectable","on"),h.className=this.L("_event_bar"),h.style.position="absolute",i.barBackColor&&(h.style.backgroundColor=i.barBackColor);var f=document.createElement("div");f.setAttribute("unselectable","on"),f.className=this.L("_event_bar_inner"),f.style.top=c+"%",0<u&&u<=1?f.style.height="1px":f.style.height=u+"%",i.barColor&&(f.style.backgroundColor=i.barColor),h.appendChild(f),s.appendChild(h)}if(e.client.deleteEnabled()){var v=document.createElement("div");v.style.position="absolute",v.style.right="2px",v.style.top="2px",v.style.width="17px",v.style.height="17px",v.className=r.L("_event_delete"),v.onmousedown=function(e){e.stopPropagation()},v.onclick=function(e){e.stopPropagation();var t=this.parentNode.event;t&&r.ya(t)},v.style.display="none",s.deleteIcon=v,s.appendChild(v)}var p=i.areas?DayPilot.Areas.copy(i.areas):[];if(DayPilot.Areas.attach(s,e,{"areas":p}),"function"==typeof r.onAfterEventRender){var m={};m.e=s.event,m.div=s,r.onAfterEventRender(m)}if(function(){var t={};if(t.control=r,t.e=e,t.element=null,s.domArgs=t,"function"==typeof r.onBeforeEventDomAdd&&r.onBeforeEventDomAdd(t),t.element){var i=l;if(i){t.Qa=i;if(n(t.element)){if(!r.Sa.Ra)throw new DayPilot.Exception("Can't reach Vue");r.Sa.Ta=!0,r.Sa.Wa(t.element,i),r.Sa.Ta=!1}else i.appendChild(t.element)}}else l.innerHTML=e.client.innerHTML()}(),a.rows[0].cells[e.part.dayIndex]){a.rows[0].cells[e.part.dayIndex].firstChild.appendChild(s),r.Xa(s)}r.elements.events.push(s)},this.Xa=function(e){for(var t=e&&e.childNodes?e.childNodes.length:0,n=0;n<t;n++)try{var i=e.childNodes[n];1===i.nodeType&&(i.unselectable="on",this.Xa(i))}catch(e){}},this.na=function(){for(var e=0;e<this.Ba.length;e++){var t=this.Ba[e];if(t.blocks)for(var n=0;n<t.blocks.length;n++)for(var i=t.blocks[n],a=0;a<i.lines.length;a++)for(var o=i.lines[a],r=0;r<o.length;r++){var s=o[r];s.part.width=100/i.lines.length,s.part.left=s.part.width*a;var l=a===i.lines.length-1;l||(s.part.width=1.5*s.part.width),this.Va(s)}}},this.Ya=function(){this.nav.top.innerHTML="",DayPilot.Util.addClass(this.nav.top,this.L("_main")),this.nav.top.style.MozUserSelect="none",this.nav.top.style.KhtmlUserSelect="none",this.nav.top.style.position="relative",this.nav.top.style.width=this.width?this.width:"100%",this.hideUntilInit&&(this.nav.top.style.visibility="hidden"),this.visible||(this.nav.top.style.display="none"),this.nav.scroll=document.createElement("div"),this.nav.scroll.style.height=this.Za()+"px","BusinessHours"===this.heightSpec?this.nav.scroll.style.overflow="auto":this.nav.scroll.style.overflow="hidden",this.nav.scroll.style.position="relative";var e=this.$a();this.nav.top.appendChild(e),this.nav.scroll.style.zoom=1;var t=this._a();this.nav.scrollable=t.firstChild,this.nav.scroll.appendChild(t),this.nav.top.appendChild(this.nav.scroll),this.nav.scrollLayer=document.createElement("div"),this.nav.scrollLayer.style.position="absolute",this.nav.scrollLayer.style.top="0px",this.nav.scrollLayer.style.left="0px",this.nav.top.appendChild(this.nav.scrollLayer),this.nav.loading=document.createElement("div"),this.nav.loading.style.position="absolute",this.nav.loading.style.top="0px",this.nav.loading.style.left=this.hourWidth+5+"px",this.nav.loading.innerHTML=r.ab(r.loadingLabelText,r.loadingLabelHtml),this.nav.loading.style.display="none",this.nav.top.appendChild(this.nav.loading)},this.la=function(){this.fasterDispose||DayPilot.pu(this.nav.hourTable),this.nav.scrollable.rows[0].cells[0].innerHTML="",this.nav.hourTable=this.bb(),this.nav.scrollable.rows[0].cells[0].appendChild(this.nav.hourTable)},this._a=function(){var e=document.createElement("div");e.style.zoom=1,e.style.position="relative";var t=document.createElement("table");t.cellSpacing="0",t.cellPadding="0",t.border="0",t.style.border="0px none",t.style.width="100%",t.style.position="absolute";var n,i=t.insertRow(-1);n=i.insertCell(-1),n.valign="top",n.style.padding="0px",n.style.border="0px none",this.nav.hourTable=this.bb(),n.appendChild(this.nav.hourTable),n=i.insertCell(-1),n.valign="top",n.width="100%",n.style.padding="0px",n.style.border="0px none";var a=document.createElement("div");return a.style.position="relative",n.appendChild(a),a.appendChild(this.cb()),a.appendChild(this.db()),e.appendChild(t),this.nav.zoom=e,e},this.cb=function(){var e=document.createElement("table");return e.cellPadding="0",e.cellSpacing="0",e.border="0",e.style.width="100%",e.style.border="0px none",e.style.tableLayout="fixed",this.nav.main=e,this.nav.events=e,e},this.db=function(){var e=document.createElement("table");e.style.top="0px",e.cellPadding="0",e.cellSpacing="0",e.border="0",e.style.position="absolute",e.style.width="100%",e.style.border="0px none",e.style.tableLayout="fixed",this.nav.events=e;for(var t=this.Ba,n=t.length,i=e.insertRow(-1),a=0;a<n;a++){var o=i.insertCell(-1);o.style.padding="0px",o.style.border="0px none",o.style.height="0px",o.style.overflow="visible",r.rtl||(o.style.textAlign="left");var s=document.createElement("div");s.style.marginRight=r.columnMarginRight+"px",s.style.marginLeft=r.columnMarginLeft+"px",s.style.position="relative",s.style.height="1px",s.style.marginTop="-1px";var l=document.createElement("div");o.selection=l,o.appendChild(s),o.appendChild(l)}return e},this.bb=function(){var e=document.createElement("table");e.cellSpacing="0",e.cellPadding="0",e.border="0",e.style.border="0px none",e.style.width=this.hourWidth+"px",e.oncontextmenu=function(){return!1};for(var t=r.oa(),n=0;n<t;n++)this.eb(e,n);return e},this.eb=function(e,t){var n=60*r.cellHeight/s.fa(),i=e.insertRow(-1);i.style.height=n+"px";var a=i.insertCell(-1);a.valign="bottom",a.unselectable="on",a.style.cursor="default",a.style.padding="0px",a.style.border="0px none";var o=document.createElement("div");o.style.position="relative",o.className=this.L("_rowheader"),o.style.width=this.hourWidth+"px",o.style.height=n+"px",o.style.overflow="hidden",o.unselectable="on";var l=document.createElement("div");l.className=this.L("_rowheader_inner"),l.unselectable="on";var d=document.createElement("div");d.unselectable="on";var c=this.startDate.addHours(t).addHours(r.sa()),u=c.getHours(),h=u<12,f=s.timeFormat();"Clock12Hours"===f&&(u%=12,0===u&&(u=12)),d.innerHTML=u;var v=document.createElement("span");v.unselectable="on",v.className=this.L("_rowheader_minutes");var p;p="Clock12Hours"===f?h?"AM":"PM":"00",v.innerHTML=p,d.appendChild(v),l.appendChild(d),o.appendChild(l),a.appendChild(o)},this.Za=function(){var e=s.fa(),t=60/e;switch(this.heightSpec){case"Full":return 24*t*this.cellHeight;case"BusinessHours":var n=this.qa();return n*this.cellHeight*t;case"BusinessHoursNoScroll":var n=this.qa();return n*this.cellHeight*t;default:throw"DayPilot.Calendar: Unexpected 'heightSpec' value."}},this.fb=function(){var e=r.nav.corner?r.nav.corner.parentNode:null;if(e){e.innerHTML="";var t=this.gb();e.appendChild(t),r.nav.corner=t}},this.$a=function(){var e=document.createElement("div");e.style.overflow="auto";var t=document.createElement("table");t.cellPadding="0",t.cellSpacing="0",t.border="0",t.style.width="100%",t.style.borderCollapse="separate",t.style.border="0px none";var n=t.insertRow(-1),i=n.insertCell(-1);i.style.padding="0px",i.style.border="0px none";var a=this.gb();i.appendChild(a),this.nav.corner=a,i=n.insertCell(-1),i.style.width="100%",i.valign="top",i.style.position="relative",i.style.padding="0px",i.style.border="0px none",this.nav.header=document.createElement("table"),this.nav.header.cellPadding="0",this.nav.header.cellSpacing="0",this.nav.header.border="0",this.nav.header.width="100%",this.nav.header.style.tableLayout="fixed",this.nav.header.oncontextmenu=function(){return!1};var o="hidden"!==this.nav.scroll.style.overflow;if(i.appendChild(this.nav.header),o){i=n.insertCell(-1),i.unselectable="on";var r=document.createElement("div");r.unselectable="on",r.style.position="relative",r.style.width="16px",r.style.height=this.headerHeight+"px",r.className=this.L("_cornerright");var s=document.createElement("div");s.className=this.L("_cornerright_inner"),r.appendChild(s),i.appendChild(r),this.nav.cornerRight=r}return e.appendChild(t),e},this.gb=function(){var e=document.createElement("div");e.style.position="relative",e.className=this.L("_corner"),e.style.width=this.hourWidth+"px",e.style.height=this.headerHeight+"px",e.oncontextmenu=function(){return!1};var t=document.createElement("div");return t.unselectable="on",t.className=this.L("_corner_inner"),e.appendChild(t),e},this.Z=function(){var e=this.nav.main;e.root=null,e.onmouseup=null;for(var t=0;t<e.rows.length;t++)for(var n=e.rows[t],i=0;i<n.cells.length;i++){var a=n.cells[i];r.hb(a)}this.fasterDispose||DayPilot.pu(e)},this.hb=function(e){e&&(!function(){var t=e,i=t.domArgs;if(t.domArgs=null,i&&"function"==typeof r.onBeforeCellDomRemove&&r.onBeforeCellDomRemove(i),i&&"function"==typeof r.onBeforeCellDomAdd){var a=i&&i.Qa;if(a){r.Sa.Ra&&n(i.element)&&(r.Sa.Ta=!0,r.Sa.Ua(a),r.Sa.Ta=!1)}}}(),e.root=null,e.onmousedown=null,e.onmousemove=null,e.onmouseout=null,e.onmouseup=null)},this.ka=function(){var e=s.fa(),i=this.nav.main,a=60*e*1e3,o=this.ra(),l=r.Ba;for(i&&this.Z();i&&i.rows&&i.rows.length>0;)this.fasterDispose||DayPilot.pu(i.rows[0]),i.deleteRow(0);this.tableCreated=!0;for(var d=l.length,c=this.nav.events;c&&c.rows&&c.rows.length>0;)this.fasterDispose||DayPilot.pu(c.rows[0]),c.deleteRow(0);for(var d=l.length,u=c.insertRow(-1),h=0;h<d;h++){var f=u.insertCell(-1);f.style.padding="0px",f.style.border="0px none",f.style.height="0px",f.style.overflow="visible",r.rtl||(f.style.textAlign="left");var v=document.createElement("div");v.style.marginRight=r.columnMarginRight+"px",v.style.marginLeft=r.columnMarginLeft+"px",v.style.position="relative",v.style.height="1px",v.style.marginTop="-1px";var p=document.createElement("div");p.style.position="relative",f.selection=p,f.appendChild(v),f.appendChild(p)}for(var m=0;m<o;m++){var u=i.insertRow(-1);u.style.MozUserSelect="none",u.style.KhtmlUserSelect="none";for(var h=0;h<d;h++){var y=this.Ba[h],f=u.insertCell(-1);f.start=y.start.addTime(m*a),f.end=f.start.addTime(a),f.resource=y.id,f.onmousedown=this.Ca,f.onmouseup=function(){return!1},f.onclick=function(){return!1},f.root=this,f.style.padding="0px",f.style.border="0px none",f.style.verticalAlign="top",f.style.height=r.cellHeight+"px",f.style.overflow="hidden",f.unselectable="on";var v=document.createElement("div");v.unselectable="on",v.style.height=r.cellHeight+"px",v.style.position="relative",v.className=this.L("_cell");var g=this.ib(f.start,f.end),b={"business":g,"text":null,"html":null,"cssClass":null,"backColor":null,"backImage":null,"backRepeat":null,"fontColor":null},w={"start":f.start,"end":f.end,"resource":f.resource,"properties":b,"x":h,"y":m};!function(){if("function"==typeof r.onBeforeCellRender){var e={};e.cell=w,r.onBeforeCellRender(e)}}(),b.business&&DayPilot.Util.addClass(v,r.L("_cell_business")),b.cssClass&&DayPilot.Util.addClass(v,b.cssClass);var D=document.createElement("div");D.setAttribute("unselectable","on"),D.className=this.L("_cell_inner");var x=DayPilot.Util.escapeTextHtml(b.text,b.html);x&&(D.innerHTML=x),b.backColor&&(D.style.backgroundColor=b.backColor),b.backImage&&(D.style.backgroundImage="url("+b.backImage+")"),b.backRepeat&&(D.style.backgroundRepeat=b.backRepeat),b.fontColor&&(D.style.color=b.fontColor),v.appendChild(D),function(){if("function"==typeof r.onBeforeCellDomAdd||"function"==typeof r.onBeforeCellDomRemove){var e={};if(e.control=r,e.cell=w,e.element=null,f.domArgs=e,"function"==typeof r.onBeforeCellDomAdd&&r.onBeforeCellDomAdd(e),e.element){var t=D;if(t){e.Qa=t;if(n(e.element)){if(!r.Sa.Ra)throw new DayPilot.Exception("Can't reach Vue");r.Sa.Ta=!0,r.Sa.Wa(e.element,t),r.Sa.Ta=!1}else t.appendChild(e.element)}}}}(),f.appendChild(v)}}i.root=this,r.nav.scrollable.onmousemove=function(e){var n=r.nav.scrollable;r.coords=DayPilot.mo3(n,e);var i=DayPilot.mc(e);if(t.resizing){t.resizingShadow||(t.resizingShadow=r.ba(t.resizing,!1,r.shadow));var a=r.cellHeight,o=0,s=i.y-t.originalMouse.y;if("bottom"===t.resizing.dpBorder){var l=t.originalHeight+s;r.snapToGrid&&(l=Math.floor((t.originalHeight+t.originalTop+s+a/2)/a)*a-t.originalTop+o),l<a&&(l=a);var d=r.nav.main.clientHeight;t.originalTop+l>d&&(l=d-t.originalTop),t.resizingShadow.style.height=l+"px"}else if("top"===t.resizing.dpBorder){var c=t.originalTop+s;r.snapToGrid&&(c=Math.floor((t.originalTop+s-o+a/2)/a)*a+o),c<o&&(c=o),c>t.originalTop+t.originalHeight-a&&(c=t.originalTop+t.originalHeight-a);var l=t.originalHeight-(c-t.originalTop);l<a?l=a:t.resizingShadow.style.top=c+"px",t.resizingShadow.style.height=l+"px"}}else if(t.moving){if(!r.coords)return;if(!t.movingShadow){var u=3,i=DayPilot.mc(e),h=Math.abs(i.x-t.originalMouse.x)+Math.abs(i.y-t.originalMouse.y);if(h<=u)return;t.movingShadow=r.ba(t.moving,!0,r.shadow),t.movingShadow.style.width=t.movingShadow.parentNode.offsetWidth+1+"px"}var a=r.cellHeight,o=0,f=t.moveOffsetY;f||(f=a/2);var c=r.coords.y-f;r.snapToGrid&&(c=Math.floor((r.coords.y-f-o+a/2)/a)*a+o),c<o&&(c=o);var v=r.nav.events,d=r.nav.main.clientHeight+o,p=parseInt(t.movingShadow.style.height);c+p>d&&(c=d-p),DayPilot.Util.addClass(t.moving,r.L("_event_moving_source")),t.movingShadow.parentNode.style.display="none",t.movingShadow.style.top=c+"px",t.movingShadow.parentNode.style.display="";var m=v.clientWidth/v.rows[0].cells.length,y=Math.floor((r.coords.x-45)/m);y<0&&(y=0),y<v.rows[0].cells.length&&y>=0&&t.movingShadow.column!==y&&(t.movingShadow.column=y,t.moveShadow(v.rows[0].cells[y]))}else DayPilot.Global.selecting&&(r.Fa(),r.Ga())},r.nav.scrollable.style.display=""},this.ib=function(e,t){return this.businessBeginsHour<this.businessEndsHour?!(e.getHours()<this.businessBeginsHour||e.getHours()>=this.businessEndsHour||6===e.getDayOfWeek()||0===e.getDayOfWeek()):e.getHours()>=this.businessBeginsHour||e.getHours()<this.businessEndsHour},this._=function(){var e=this.nav.header;if(e&&e.rows)for(var t=0;t<e.rows.length;t++)for(var n=e.rows[t],i=0;i<n.cells.length;i++){var a=n.cells[i];a.onclick=null,a.onmousemove=null,a.onmouseout=null}this.fasterDispose||DayPilot.pu(e)},this.jb=function(e){function t(t){var a=i[t],o=e?n.insertCell(-1):n.cells[t];o.data=a,o.style.overflow="hidden",o.style.padding="0px",o.style.border="0px none",o.style.height=r.headerHeight+"px",o.onclick=r.kb;var s,l=e?document.createElement("div"):o.firstChild;e?(l.unselectable="on",l.style.MozUserSelect="none",l.style.cursor="default",l.style.position="relative",l.className=r.L("_colheader"),l.style.height=r.headerHeight+"px",r.headerTextWrappingEnabled||(l.style.whiteSpace="nowrap"),s=document.createElement("div"),s.className=r.L("_colheader_inner"),s.unselectable="on",l.appendChild(s),o.appendChild(l)):s=l.firstChild;var d={};d.header={},d.header.cssClass=null,d.header.verticalAlignment="center",d.header.horizontalAlignment="center",d.column=r.lb(a,r),"function"==typeof r.onBeforeHeaderRender&&(DayPilot.Util.copyProps(a,d.header,["id","start","name","html","backColor","toolTip","areas"]),r.onBeforeHeaderRender(d),DayPilot.Util.copyProps(d.header,a,["html","backColor","toolTip","areas","cssClass","verticalAlignment","horizontalAlignment"])),a.toolTip&&(s.title=a.toolTip),a.cssClass&&DayPilot.Util.addClass(l,a.cssClass),a.backColor&&(s.style.background=a.backColor),a.areas&&DayPilot.Areas.attach(l,a);
var c=a.verticalAlignment;if(c)switch(s.style.display="flex",c){case"center":s.style.alignItems="center";break;case"top":s.style.alignItems="flex-start";break;case"bottom":s.style.alignItems="flex-end"}var u=a.horizontalAlignment;if(u)switch(u){case"center":s.style.justifyContent="center";break;case"left":s.style.justifyContent="flex-start";break;case"right":s.style.justifyContent="flex-end"}l.firstChild.innerHTML=r.ab(a.name,a.html)}for(var n=e?this.nav.header.insertRow(-1):this.nav.header.rows[0],i=this.Ba,a=i.length,o=0;o<a;o++)t(o)},this.kb=function(e){if("Disabled"!==r.headerClickHandling){var t=this.data,n=r.lb(t),i={};i.header={},i.header.id=t.id,i.header.name=t.name,i.header.start=t.start,i.column=n,i.originalEvent=e,i.shift=e.shiftKey,i.ctrl=e.ctrlKey,i.meta=e.metaKey,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof r.onHeaderClick&&(r.onHeaderClick(i),i.preventDefault.value)||"function"==typeof r.onHeaderClicked&&r.onHeaderClicked(i)}},this.lb=function(e){return new DayPilot.CalendarColumn(e,r)},this.mb=function(){return this.width&&this.width.indexOf("px")!==-1?"Pixel":"Percentage"},this.ja=function(){var e=this.nav.header,t=!0,n=this.Ba;for(n.length;this.headerCreated&&e&&e.rows&&e.rows.length>0;)this.fasterDispose||DayPilot.pu(e.rows[0]),e.deleteRow(0);this.headerCreated=!0;this.jb(t)},this.loadingStart=function(){this.loadingLabelVisible&&(this.nav.loading.innerHTML=this.loadingLabelText,this.nav.loading.style.top=this.headerHeight+5+"px",this.nav.loading.style.display="")},this.commandCallBack=function(e,t){var n={};n.command=e,this.U("Command",t,n)},this.loadingStop=function(e){this.callbackTimeout&&window.clearTimeout(this.callbackTimeout),this.nav.loading.style.display="none"},this.nb=function(){var e=this.nav.scroll;e.root=this,r.ob(),e.onscroll||(e.onscroll=function(){r.pb()})},this.callbackError=function(e,t){alert("Error!\r\nResult: "+e+"\r\nContext:"+t)},this.qb=function(){var e=DayPilot.sw(this.nav.scroll),t=this.nav.cornerRight;t&&(t.style.width=e+"px")},this.rb=function(){t.globalHandlers||(t.globalHandlers=!0,DayPilot.re(document,"mouseup",t.gMouseUp))},this.events={},this.events.add=function(e){var t=null;if(e instanceof DayPilot.Event)t=e.data;else{if("object"!=typeof e)throw"DayPilot.Calendar.events.add() expects an object or DayPilot.Event instance.";t=e}r.events.list||(r.events.list=[]),r.events.list.push(t),r.sb({"eventsOnly":!0}),r.wa.notify()},this.events.find=function(e){if(!r.events.list)return null;if("function"==typeof e){for(var t=e,n=0;n<r.events.list.length;n++){var i=new DayPilot.Event(r.events.list[n],r);if(t(i))return i}return null}for(var n=0;n<r.events.list.length;n++){var a=r.events.list[n];if(a.id===e)return new DayPilot.Event(a,r)}return null},this.events.forRange=function(e,t){return e=new DayPilot.Date(e),t=new DayPilot.Date(t),(r.events.list||[]).filter(function(n){return i(e,t,new DayPilot.Date(n.start),new DayPilot.Date(n.end))}).map(function(e){return new DayPilot.Event(e,r)})},this.events.update=function(e){if(e instanceof DayPilot.Event)e.commit();else if("object"==typeof e){var t=r.events.find(e.id);if(t){var n=DayPilot.indexOf(r.events.list,t.data);r.events.list.splice(n,1,e)}}r.sb({"eventsOnly":!0}),r.wa.notify()},this.events.remove=function(e){var t;if(e instanceof DayPilot.Event)t=e.data;else if("object"==typeof e){var n=r.events.find(e.id);n&&(t=n.data)}else if("string"==typeof e||"number"==typeof e){var n=r.events.find(e);n&&(t=n.data)}var i=DayPilot.indexOf(r.events.list,t);r.events.list.splice(i,1),r.sb({"eventsOnly":!0}),r.wa.notify()},this.events.load=function(e,t,n){var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var o={};return o.exception=e,void i(o)}if(DayPilot.isArray(n)){var s={};if(s.preventDefault=function(){this.preventDefault.value=!0},s.data=n,"function"==typeof t&&t(s),s.preventDefault.value)return;r.events.list=n,r.Ma&&r.sb({"eventsOnly":!0})}};if(r.eventsLoadMethod&&"POST"===r.eventsLoadMethod.toUpperCase())DayPilot.Http.ajax({"method":"POST","data":{"start":r.visibleStart().toString(),"end":r.visibleEnd().toString()},"url":e,"success":a,"error":i});else{var o=e,s="start="+r.visibleStart().toString()+"&end="+r.visibleEnd().toString();o+=o.indexOf("?")>-1?"&"+s:"?"+s,DayPilot.Http.ajax({"method":"GET","url":o,"success":a,"error":i})}},this.tb=function(){if(r.nav.top.className!==r.L("_main")){r.nav.top.className=r.L("_main");var e=r.nav.corner;e.className=r.L("_corner"),e.firstChild.className=r.L("_corner_inner");var t=r.nav.cornerRight;t&&(t.className=r.L("_cornerright"),t.firstChild.className=r.L("_cornerright_inner"))}},this.update=function(e){if(r.Q)throw new DayPilot.Exception("You are trying to update a DayPilot.Calendar instance that has been disposed.");r.ub(e),r.sb()},this.sb=function(e){if(this.Ma){var e=e||{},t=!e.eventsOnly;r.vb(),r.Y(),r.nav.top.style.cursor="auto",t&&(r.ga(),r.ja(),r.ka(),r.la(),r.ma(),r.fb(),r.qb(),r.tb(),r.ob()),r.ha(),r.ia(),r.na(),r.clearSelection(),this.visible?this.show():this.hide()}},this.wb=null,this.ub=function(e){if(e){var t={"events":{"preInit":function(){var e=this.data||[];DayPilot.isArray(e.list)?r.events.list=e.list:r.events.list=e}},"columns":{"preInit":function(){r.columns.list=this.data}}};this.wb=t;for(var n in e)if(t[n]){var i=t[n];i.data=e[n],i.preInit&&i.preInit()}else r[n]=e[n]}},this.xb=function(){var e=this.wb;for(var t in e){var n=e[t];n.postInit&&n.postInit()}},this.yb=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Calendar() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Calendar: The placeholder element not found: '"+e+"'."}},this.zb={},this.zb.events=[],this.Ab=function(e){var t=this.zb.events,n=this.events.list[e],i={};for(var a in n)i[a]=n[a];if(i.start=new DayPilot.Date(n.start),i.end=new DayPilot.Date(n.end),"function"==typeof this.onBeforeEventRender){var o={};o.control=r,o.data=i,this.onBeforeEventRender(o)}t[e]=i},this.ha=function(){var e=this.events.list;if(r.zb.events=[],e){if(!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.Calendar.events.list expects an array object. You supplied: "+typeof e);var t=e.length,n=864e5;this.cache.pixels={};var i=[];this.scrollLabels=[],this.minStart=1e4,this.maxEnd=0;for(var a=0;a<t;a++){var o=e[a],s=o;if("object"!=typeof s)throw new DayPilot.Exception("Event data item must be an object");if(!s.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(!s.end)throw new DayPilot.Exception("Event data item must specify 'end' property");if(s instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Calendar: DayPilot.Event object detected in events.list array. Use raw event data instead.")}if("function"==typeof this.onBeforeEventRender)for(var a=0;a<t;a++)this.Ab(a);for(var a=0;a<this.Ba.length;a++){var l={};l.minEnd=1e6,l.maxStart=-1,this.scrollLabels.push(l);var d=this.Ba[a];d.events=[],d.lines=[],d.blocks=[];for(var c=new DayPilot.Date(d.start),u=c.getTime(),h=c.addTime(n),f=h.getTime(),v=0;v<t;v++)if(!i[v]){var o=e[v],p=new DayPilot.Date(o.start),m=new DayPilot.Date(o.end),y=p.getTime(),g=m.getTime();if(!(g<y)){var b=!(g<=u||y>=f);if("Resources"===r.viewType&&(b=b&&d.id===o.resource),b){var w=new DayPilot.Event(o,r);w.part.dayIndex=a,w.part.start=u<y?p:c,w.part.end=f>g?m:h;var D=this.getPixels(w.part.start,d.start),x=this.getPixels(w.part.end,d.start),_=D.top,C=x.top;if(_===C&&(D.cut||x.cut))continue;var k=x.boxBottom,S="Always"===r.useEventBoxes;S?(w.part.top=Math.floor(_/this.cellHeight)*this.cellHeight+1,w.part.height=Math.max(Math.ceil(k/this.cellHeight)*this.cellHeight-w.part.top,this.cellHeight-1)+1):(w.part.top=_+1,w.part.height=C-_+1),w.part.barTop=Math.max(_-w.part.top-1,0),w.part.barHeight=Math.max(C-_-2,1);var p=w.part.top,m=w.part.top+w.part.height;p>l.maxStart&&(l.maxStart=p),m<l.minEnd&&(l.minEnd=m),p<this.minStart&&(this.minStart=p),m>this.maxEnd&&(this.maxEnd=m),d.events.push(w),"function"==typeof this.onBeforeEventRender&&(w.cache=this.zb.events[v]),w.part.start.getTime()===y&&w.part.end.getTime()===g&&(i[v]=!0)}}}}for(var a=0;a<this.Ba.length;a++){var d=this.Ba[a];d.events.sort(this.Bb);for(var v=0;v<d.events.length;v++){var o=d.events[v];d.putIntoBlock(o)}for(var v=0;v<d.blocks.length;v++){var P=d.blocks[v];P.events.sort(this.Bb);for(var M=0;M<P.events.length;M++){var o=P.events[M];P.putIntoLine(o)}}}}},this.Bb=function(e,t){if(!(e&&t&&e.start&&t.start))return 0;var n=e.start().getTime()-t.start().getTime();return 0!==n?n:t.end().getTime()-e.end().getTime()},this.debug=function(e,t){this.debuggingEnabled&&(r.debugMessages||(r.debugMessages=[]),r.debugMessages.push(e),"undefined"!=typeof console&&console.log(e))},this.getPixels=function(e,t){t||(t=this.startDate);var n=t.getTime(),i=e.getTime(),a=s.fa(),o=this.cache.pixels[i+"_"+n];if(o)return o;n=t.getTime();var r=60*a*1e3,l=i-n,d=l%r,c=l-d,u=c+r;0===d&&(u=c);var h={};return h.cut=!1,h.top=this.Cb(l),h.boxTop=this.Cb(c),h.boxBottom=this.Cb(u),this.cache.pixels[i+"_"+n]=h,h},this.Cb=function(e){return Math.floor(this.cellHeight*e/(6e4*s.fa()))},this.Db=function(e){var t=Math.floor((shadowTop-_startOffset)/r.cellHeight);r.snapToGrid||(t=(shadowTop-_startOffset)/r.cellHeight);s.fa()},this.vb=function(){this.startDate=new DayPilot.Date(this.startDate).getDatePart()},this.ia=function(){this.nav.corner&&(this.nav.corner.style.height=this.headerHeight+"px")},this.ma=function(){var e=this.Za();this.nav.scroll&&e>0&&(this.nav.scroll.style.height=e+"px")},this.wa={},this.wa.scope=null,this.wa.notify=function(){r.wa.scope&&r.wa.scope["$apply"]()},this.wa.apply=function(e){e()},this.pb=function(){if(r.nav.scroll&&r.Eb()){var e=r.nav.scroll.scrollTop,t=s.fa(),n=60/t,i=e/(n*r.cellHeight);r.Fb.scrollHour=i}},this.ob=function(){var e=0,t=60/s.fa();e="number"==typeof r.Fb.scrollHour?t*r.cellHeight*r.Fb.scrollHour:"Auto"===r.initScrollPos?"BusinessHours"===this.heightSpec?t*this.cellHeight*this.businessBeginsHour:0:this.initScrollPos;var n=r.nav.top;"none"===n.style.display?(n.style.display="",r.nav.scroll.scrollTop=e,n.style.display="none"):r.nav.scroll.scrollTop=e},this.getScrollY=function(){return r.nav.scroll.scrollTop},this.setScrollY=function(e){r.nav.scroll.scrollTop=e,r.pb()},this.Gb=function(){return!(!this.backendUrl&&"function"!=typeof WebForm_DoCallback)&&("undefined"==typeof r.events.list||!r.events.list)},this.u=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.show=function(){r.visible=!0,r.nav.top.style.display="",this.qb()},this.hide=function(){r.visible=!1,r.nav.top.style.display="none"},this.Hb=function(){this.vb(),this.ga(),this.Ya(),this.ja(),this.ka(),this.qb(),this.nb(),this.rb(),t.register(this),this.Ib(),this.U("Init")},this.Fb={},this.Jb=function(){this.Fb.themes=[],this.Fb.themes.push(this.theme||this.cssClassPrefix)},this.Kb=function(){for(var e=this.Fb.themes,t=0;t<e.length;t++){var n=e[t];DayPilot.Util.removeClass(this.nav.top,n+"_main")}this.Fb.themes=[]},this.Lb=function(){if(this.afterRender(null,!1),"function"==typeof this.onAfterRender){var e={};e.isCallBack=!1,this.onAfterRender(e)}},this.Mb=function(){if("function"==typeof this.onInit&&!this.Nb){this.Nb=!0;var e={};this.onInit(e)}},this.Eb=function(){var e=r.nav.top;return!!e&&(e.offsetWidth>0&&e.offsetHeight>0)},this.Ib=function(){var e=r.Eb;e()||(r.X=setInterval(function(){e()&&(r.nb(),r.qb(),clearInterval(r.X))},100))},this.ab=function(e,t){return r.ca.da()?DayPilot.Util.escapeTextHtml(e,t):DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":e:t},this.Sa={},this.Sa.Ra=null,this.Sa.Ob=function(e,t,n){var i=r.Sa.Ra;if("function"==typeof i.createVNode&&"function"==typeof i.render){var a=i.createVNode(e,n);i.render(a,t)}},this.Sa.Wa=function(e,t){var n=r.Sa.Ra;if("function"==typeof n.render){var i=e;DayPilot.isArray(e)&&(i=n.h("div",null,e)),n.render(i,t)}},this.Sa.Ua=function(e){var t=r.Sa.Ra;"function"==typeof t.render&&t.render(null,e)},this.internal={},this.internal.loadOptions=r.ub,this.internal.xssTextHtml=r.ab,this.internal.enableVue=function(e){r.Sa.Ra=e},this.internal.vueRef=function(){return r.Sa.Ra},this.internal.vueRendering=function(){return r.Sa.Ta},this.init=function(){this.yb();var e=this.Gb();return this.Jb(),e?void this.Hb():(this.vb(),this.ga(),this.ha(),this.Ya(),this.ja(),this.ka(),this.u(),this.qb(),this.nb(),this.rb(),t.register(this),this.events&&(this.ia(),this.na()),this.Lb(),this.Mb(),this.Ib(),this.Ma=!0,this)},this.Init=this.init,this.ub(a)},DayPilot.CalendarColumn=function(e,t){var n=this;n.id=e.id,n.name=e.name,n.data=e.data,n.start=new DayPilot.Date(e.start),n.calendar=t,n.toJSON=function(){var e={};return e.id=this.id,this.start&&(e.start=this.start.toString()),e.name=this.name,e}},DayPilot.Calendar=t.Calendar,"undefined"!=typeof jQuery&&!function(e){e.fn.daypilotCalendar=function(e){var t=null,n=this.each(function(){if(!this.daypilot){var n=new DayPilot.Calendar(this.id);this.daypilot=n;for(name in e)n[name]=e[name];n.init(),t||(t=n)}});return 1===this.length?t:n}}(jQuery),function(){var e=DayPilot.am();e&&e.directive("daypilotCalendar",["$parse",function(e){return{"restrict":"E","template":"<div></div>","replace":!0,"link":function(t,n,i){var a=new DayPilot.Calendar(n[0]);a.wa.scope=t,a.init();var o=i["id"];o&&(t[o]=a);var r=i["publishAs"];if(r){(0,e(r).assign)(t,a)}for(var s in i)0===s.indexOf("on")&&!function(n){a[n]=function(a){var o=e(i[n]);t["$apply"](function(){o(t,{"args":a})})}}(s);var l=t["$watch"],d=i["config"]||i["daypilotConfig"],c=i["events"]||i["daypilotEvents"];l.call(t,d,function(e){for(var t in e)a[t]=e[t];a.update(),a.Mb()},!0),l.call(t,c,function(e){a.events.list=e,a.update()},!0)}}}])}()}}(),"undefined"==typeof DayPilot)var DayPilot={};if("undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";"undefined"==typeof DayPilot.DatePicker&&(DayPilot.DatePicker=function(e){this.v="2025.3.696-lite";var t="navigator_"+(new Date).getTime(),n=this;this.onShow=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.prepare=function(){if(this.locale="en-us",this.target=null,this.targetAlignment="left",this.resetTarget=!0,this.pattern=this.ca.locale().datePattern,this.theme="navigator_default",this.patterns=[],this.zIndex=null,this.showToday=!0,this.todayText="Today",e)for(var t in e)this[t]=e[t]},this.init=function(){this.date=new DayPilot.Date(this.date);var e=this.Pb();this.resetTarget&&!e?this.Qb(this.date):!this.resetTarget&&e&&(n.date=e);var t=this.D();return t&&t.addEventListener("input",function(){n.date=n.Pb(),n.date&&n.navigator.select(n.date,{dontNotify:!0})}),this},this.close=function(){document.removeEventListener("mousedown",n.close),document.removeEventListener("wheel",n.close),window.removeEventListener("resize",n.close),n.Eb&&(n.Eb=!1,n.navigator&&n.navigator.dispose(),n.div.innerHTML="",n.div&&n.div.parentNode===document.body&&document.body.removeChild(n.div))},this.Rb=function(e){this.date=new DayPilot.Date(e),this.Qb(this.date)},this.select=function(e){var t={};t.date=new DayPilot.Date(e),t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof n.onTimeRangeSelect&&(n.onTimeRangeSelect(t),t.preventDefault.value)||(this.Rb(e),"function"==typeof n.onTimeRangeSelected&&n.onTimeRangeSelected(t))},this.Pb=function(){var e=this.D();if(!e)return this.date;var t=null;if(t="INPUT"===e.tagName?e.value:e.innerText,!t)return null;for(var i=DayPilot.Date.parse(t,n.pattern),a=0;a<n.patterns.length;a++){if(i)return i;i=DayPilot.Date.parse(t,n.patterns[a])}return i},this.Qb=function(e){var t=this.D();if(t){var i=e.toString(n.pattern,n.locale);"INPUT"===t.tagName?t.value=i:t.innerHTML=i}},this.ca={},this.ca.locale=function(){return DayPilot.Locale.find(n.locale)},this.D=function(){var e=this.target;return e&&e.nodeType&&1===e.nodeType?e:document.getElementById(e)},Object.defineProperty(this,"visible",{get:function(){return n.Eb}}),this.show=function(){if(!this.Eb){document.addEventListener("mousedown",n.close),document.addEventListener("wheel",n.close),window.addEventListener("resize",n.close);var e=this.D(),i=new DayPilot.Navigator(t);i.api=2,i.cssOnly=!0,i.theme=n.theme,i.weekStarts="Auto",i.locale=n.locale,i.showToday=n.showToday,i.todayText=n.todayText,i.onTodayClick=function(e){i.onTimeRangeSelected({start:DayPilot.Date.today()}),e.preventDefault()},i.onTimeRangeSelected=function(e){n.date=e.start;var t=e.start.addTime(i.Sb),a=t.toString(n.pattern,n.locale),e={};e.start=t,e.date=t,e.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof n.onTimeRangeSelect&&(n.onTimeRangeSelect(e),e.preventDefault.value)||(n.Qb(a),n.close(),"function"==typeof n.onTimeRangeSelected&&n.onTimeRangeSelected(e))},this.navigator=i;var a={x:0,y:0,w:0,h:0};e&&(a=DayPilot.abs(e));var o=a.h,r=n.targetAlignment,s=document.createElement("div");s.style.position="absolute","left"===r&&(s.style.left=a.x+"px"),s.style.top=a.y+o+"px",n.zIndex&&(s.style.zIndex=n.zIndex);var l=document.createElement("div");l.id=t,s.appendChild(l),s.addEventListener("mousedown",function(e){var e=e||window.event;e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}),document.body.appendChild(s),this.div=s;var d=n.Pb()||n.date;if(i.startDate=d,i.Sb=d.getTimePart(),i.selectionDay=d.getDatePart(),i.init(),"right"===r){var c=a.x+a.w-i.nav.top.offsetWidth;s.style.left=c+"px"}this.Eb=!0,this.onShow&&this.onShow()}},this.prepare(),this.init()})}(DayPilot),"undefined"==typeof DayPilot)var DayPilot={};if("undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";if("undefined"==typeof DayPilot.Menu||!DayPilot.Menu.def){var e=function(){},t={};t.mouse=null,t.menu=null,t.handlersRegistered=!1,t.hideTimeout=null,t.waitingSubmenu=null,DayPilot.Menu=function(n){var i=this,a=null;this.v="2025.3.696-lite",this.zIndex=120,this.cssClassPrefix="menu_default",this.cssOnly=!0,this.menuTitle=null,this.showMenuTitle=!1,this.hideOnMouseOut=!1,this.hideAfter=200,this.theme=null,this.onShow=null,this.Tb=function(){},n&&DayPilot.isArray(n)&&(this.items=n),this.toJSON=function(){return null},this.show=function(n,o){o=o||{};var r=null;if(n?"string"==typeof n.id||"number"==typeof n.id?r=n.id:"function"==typeof n.id?r=n.id():"function"==typeof n.value&&(r=n.value()):r=null,"undefined"!=typeof DayPilot.Bubble&&DayPilot.Bubble.hideActive(),o.submenu||t.menuClean(),this.Tb.submenu=null,null!==t.mouse){i.cssOnly||(i.cssOnly=!0);var s=null;if(n&&n.isRow&&n.$.row.task?(s=new DayPilot.Task(n.$.row.task,n.calendar),s.menuType="resource"):s=n&&n.isEvent&&n.data.task?new DayPilot.Task(n,n.calendar):n,"function"==typeof i.onShow){var l={};if(l.source=s,l.menu=i,l.preventDefault=function(){l.preventDefault.value=!0},i.onShow(l),l.preventDefault.value)return}var d=document.createElement("div");if(d.style.position="absolute",d.style.top="0px",d.style.left="0px",d.style.display="none",d.style.overflow="hidden",d.style.zIndex=this.zIndex+1,d.className=this.Ub("main"),d.onclick=function(e){e.cancelBubble=!0,this.parentNode.removeChild(this)},this.hideOnMouseOut&&(d.onmousemove=function(e){clearTimeout(t.hideTimeout)},d.onmouseleave=function(e){i.delayedHide({"hideParent":!0})}),!this.items||0===this.items.length)throw"No menu items defined.";if(this.showMenuTitle){var c=document.createElement("div");c.innerHTML=this.menuTitle,c.className=this.Ub("title"),d.appendChild(c)}for(var u=0;u<this.items.length;u++){var h=this.items[u],f=document.createElement("div");if(DayPilot.Util.addClass(f,this.Ub("item")),h.items&&(DayPilot.Util.addClass(f,this.Ub("item_haschildren")),DayPilot.Util.addClass(d,this.Ub("withchildren"))),"undefined"!=typeof h&&!h.hidden){if("-"===h.text){var v=document.createElement("div");v.addEventListener("click",function(e){e.stopPropagation()}),f.appendChild(v)}else{var p=document.createElement("a");if(p.style.position="relative",p.style.display="block",h.cssClass&&DayPilot.Util.addClass(p,h.cssClass),h.disabled)DayPilot.Util.addClass(p,i.Ub("item_disabled"));else{if(h.onclick||h.onClick){p.item=h,p.onclick=function(e,t){return function(n){if("function"==typeof e.onClick){var i={};if(i.item=e,i.source=t.source,i.originalEvent=n,i.preventDefault=function(){i.preventDefault.value=!0},e.onClick(i),i.preventDefault.value)return void n.stopPropagation()}e.onclick&&e.onclick.call(t,n)}}(h,p);var m=function(e,t){return function(e){e.stopPropagation(),e.preventDefault(),t.source.calendar&&t.source.calendar.internal.touch&&(t.source.calendar.internal.touch.active=!0)}},y=function(e,n){return function(i){i.stopPropagation(),i.preventDefault();var a=function(){window.setTimeout(function(){n.source.calendar&&n.source.calendar.internal.touch&&(n.source.calendar.internal.touch.active=!1)},500)};if("function"==typeof e.onClick){var o={};if(o.item=e,o.source=n.source,o.originalEvent=i,o.preventDefault=function(){o.preventDefault.value=!0},e.onClick(o),o.preventDefault.value)return void a()}e.onclick&&e.onclick.call(n,i),t.menuClean(),a()}};DayPilot.reNonPassive(p,"touchstart",m(h,p)),DayPilot.reNonPassive(p,"touchend",y(h,p))}if(h.items&&!h.disabled){var g=function(e,t){return function(n){n.preventDefault(),n.stopPropagation(),i.Vb(e,t)}};p.ontouchend=g(h,p)}if(h.onclick)e();else if(h.href)p.href=h.href.replace(/\x7B0\x7D/gim,r),h.target&&p.setAttribute("target",h.target);else if(h.command){var g=function(e,t){return function(n){var i=t.source,a=e;a.action=a.action?a.action:"CallBack";var o=i.calendar||i.root;if(i instanceof DayPilot.Link)return void o.internal.linkMenuClick(a.command,i,a.action);if(i instanceof DayPilot.Selection)return void o.internal.timeRangeMenuClick(a.command,i,a.action);if(i instanceof DayPilot.Event)return void o.internal.eventMenuClick(a.command,i,a.action);if(i instanceof DayPilot.Selection)return void o.internal.timeRangeMenuClick(a.command,i,a.action);if(i instanceof DayPilot.Task)return void("resource"===i.menuType?o.internal.resourceHeaderMenuClick(a.command,t.menuSource,a.action):o.internal.eventMenuClick(a.command,t.menuSource,a.action));switch(i.menuType){case"resource":return void o.internal.resourceHeaderMenuClick(a.command,i,a.action);case"selection":return void o.internal.timeRangeMenuClick(a.command,i,a.action);default:return void o.internal.eventMenuClick(a.command,i,a.action)}n.preventDefault()}};p.onclick=g(h,p),p.ontouchend=g(h,p)}}h.items&&p.addEventListener("click",function(e){e.stopPropagation()}),p.source=s,p.menuSource=n;var b=document.createElement("span");if(b.className=i.Ub("item_text"),b.innerHTML=DayPilot.Util.escapeTextHtml(h.text,h.html),p.appendChild(b),h.image){var w=document.createElement("img");w.src=h.image,w.style.position="absolute",w.style.top="0px",w.style.left="0px",p.appendChild(w)}if(h.icon){var D=document.createElement("span");D.className=i.Ub("item_icon");var x=document.createElement("i");x.className=h.icon,D.appendChild(x),p.appendChild(D)}if(h.symbol){var _="http://www.w3.org/2000/svg",C=document.createElementNS(_,"svg");C.setAttribute("width","100%"),C.setAttribute("height","100%");var k=document.createElementNS(_,"use");k.setAttribute("href",h.symbol),C.appendChild(k);var S=document.createElement("span");S.className=i.Ub("item_symbol"),S.style.position="absolute",S.style.top="0px",S.style.left="0px",S.appendChild(C),p.appendChild(S)}var P=function(e,n){return function(){var a=(n.source,e),o=t.waitingSubmenu;if(o){if(o.parent===a)return;clearTimeout(o.timeout),t.waitingSubmenu=null}e.disabled||(t.waitingSubmenu={},t.waitingSubmenu.parent=a,t.waitingSubmenu.timeout=setTimeout(function(){t.waitingSubmenu=null,i.Vb(a,n)},300))}};p.onmouseover=P(h,p),f.appendChild(p)}d.appendChild(f)}}var M=function(e){window.setTimeout(function(){t.menuClean(),DayPilot.MenuBar.deactivate()},100)};d.onclick=M,d.ontouchend=M,d.onmousedown=function(e){e=e||window.event,e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()},d.oncontextmenu=function(){return!1},document.body.appendChild(d),i.Tb.visible=!0,i.Tb.source=n,d.style.display="";var T=d.offsetHeight,A=d.offsetWidth;d.style.display="none";var E=document.documentElement.clientHeight,H=window.innerWidth,N="number"==typeof o.windowMargin?o.windowMargin:5;if(function(){var e=o.initiator;if(e){var t=e.div,n=e.e,i=e.area,r=i.visibility||i.v||"Visible",s=e.a;if("Visible"!==r&&(s=DayPilot.Areas.createArea(t,n,i),t.appendChild(s),a=s),s){var l=DayPilot.abs(s);o.x=l.x,o.y=l.y+l.h+2}}}(),function(){var e="number"==typeof o.x?o.x:t.mouse.x+1,n="number"==typeof o.y?o.y:t.mouse.y+1,a=document.body.scrollTop||document.documentElement.scrollTop,r=document.body.scrollLeft||document.documentElement.scrollLeft,s=0,l=0;if(n-a>E-T&&0!==E){s=n-(n-a-(E-T)+N)}else s=n;if(i.Tb.y=s,d.style.top=s+"px","right"===o.align&&(e-=A),e-r>H-A&&0!==H){l=e-(e-r-(H-A)+N)}else l=e;i.Tb.x=l,d.style.left=l+"px"}(),o.parentLink){var I=o.parentLink,R=parseInt(new DayPilot.StyleReader(d).get("border-top-width")),L=DayPilot.abs(o.parentLink.parentNode),B=L.x+I.offsetWidth,z=L.y-R;B+A>H&&(B=Math.max(0,L.x-A));var U=document.body.scrollTop+document.documentElement.scrollTop;z+T-U>E&&(z=Math.max(0,E-T+U)),d.style.left=B+"px",d.style.top=z+"px"}d.style.display="",this.addShadow(d),this.Tb.div=d,o.submenu||(DayPilot.Menu.active=this)}},this.update=function(){if(i.Tb.visible){var e=i.Tb.source;i.hide(),i.show(e,{"x":i.Tb.x,"y":i.Tb.y})}},this.Vb=function(e,t){var n=e,a=t.source;if((!i.Tb.submenu||i.Tb.submenu.item!==e)&&(i.Tb.submenu&&i.Tb.submenu.item!==e&&(DayPilot.Util.removeClass(i.Tb.submenu.link.parentNode,i.Ub("item_haschildren_active")),i.Tb.submenu.menu.hide(),i.Tb.submenu=null),e.items)){var o=i.cloneOptions();o.items=e.items,i.Tb.submenu={},i.Tb.submenu.menu=new DayPilot.Menu(o),i.Tb.submenu.menu.Wb=i,i.Tb.submenu.menu.show(a,{"submenu":!0,"parentLink":t,"parentItem":n}),i.Tb.submenu.item=e,i.Tb.submenu.link=t,DayPilot.Util.addClass(t.parentNode,i.Ub("item_haschildren_active"))}},this.Ub=function(e){var t=this.theme||this.cssClassPrefix,n=this.cssOnly?"_":"";return t?t+n+e:""},this.cloneOptions=function(){return DayPilot.Util.copyProps(o,{},["cssClassPrefix","theme","hideAfter","hideOnMouseOut","zIndex"])},this.hide=function(e){e=e||{},this.Tb.submenu&&this.Tb.submenu.menu.hide();var n=t.waitingSubmenu;if(n&&(t.waitingSubmenu=null,clearTimeout(n.timeout)),this.removeShadow(),this.Tb.div&&this.Tb.div.parentNode===document.body&&document.body.removeChild(this.Tb.div),a&&(DayPilot.de(a),a=null),i.Tb.visible=!1,i.Tb.source=null,i.Wb&&e.hideParent&&i.Wb.hide(e),DayPilot.Menu.active===i&&(DayPilot.Menu.active=null),"function"==typeof this.onHide){var o={};this.onHide(o)}},this.delayedHide=function(e){t.hideTimeout=setTimeout(function(){i.hide(e)},i.hideAfter)},this.cancelHideTimeout=function(){clearTimeout(t.hideTimeout)},this.init=function(e){return t.mouseMove(e),this},this.addShadow=function(e){},this.removeShadow=function(){};var o=DayPilot.isArray(n)?null:n;if(o)for(var r in o)this[r]=o[r]},DayPilot.MenuBar=function(e,t){var n=this;t=t||{},this.items=[],this.theme="menubar_default",this.windowMargin=0,this.nav={},this.elements={},this.elements.items=DayPilot.list(),this.l=null,this.Ma=!1;for(var i in t)this[i]=t[i];this.Xb=function(e){return this.theme+"_"+e},this.u=function(){this.nav.top=document.getElementById(e);var t=this.nav.top;t.className=this.Xb("main"),DayPilot.list(n.items).forEach(function(e){var i=document.createElement("span");i.innerHTML=DayPilot.Util.escapeTextHtml(e.text,e.html),i.className=n.Xb("item"),e.cssClass&&i.classList.add(e.cssClass),i.data=e,i.onclick=function(t){if(n.active&&n.active.item===e)n.Yb();else if(e.children)return void n.Zb(i);if("function"==typeof e.onClick){var a={};a.item=e,a.originalEvent=t,e.onClick(a)}},i.onmousedown=function(e){e.stopPropagation()},i.onmouseover=function(){n.active&&n.active.item!==e&&n.Zb(i)},t.appendChild(i),n.elements.items.push(i)})},this.Yb=function(){var e=n.Xb("item_active");n.elements.items.forEach(function(t){DayPilot.Util.removeClass(t,e)}),n.active&&n.active.menu&&n.active.menu.hide(),n.active=null},this.$b=function(e){return!!n.active&&n.active.item===e.data},this.Zb=function(e){if(!n.$b(e)){n.Yb();var t=e.data,i=n.active={};i.item=t,i.div=e;var a=n.Xb("item_active");DayPilot.Util.addClass(e,a);var o=DayPilot.abs(e);if(t.children){i.menu=new DayPilot.Menu({"items":t.children});var r=o.x;"right"===t.align&&(r+=o.w),i.menu.show(null,{"x":r,"y":o.y+o.h,"align":t.align,"windowMargin":n.windowMargin})}DayPilot.MenuBar.active=n}},this.init=function(){return this.u(),this.Ma=!0,this},this.dispose=function(){this.Ma&&(this.nav.top.innerHTML="",this.elements.items=[])}},DayPilot.MenuBar.deactivate=function(){DayPilot.MenuBar.active&&(DayPilot.MenuBar.active.Yb(),DayPilot.MenuBar.active=null)},t.menuClean=function(){"undefined"!=typeof DayPilot.Menu.active&&DayPilot.Menu.active&&(DayPilot.Menu.active.hide(),DayPilot.Menu.active=null)},t.mouseDown=function(e){"undefined"!=typeof t&&(t.menuClean(),DayPilot.MenuBar.deactivate())},t.wheel=function(e){"undefined"!=typeof t&&(t.menuClean(),DayPilot.MenuBar.deactivate())},t.mouseMove=function(e){"undefined"!=typeof t&&(t.mouse=t.mousePosition(e))},t.touchMove=function(e){"undefined"!=typeof t&&(t.mouse=t.touchPosition(e))},t.touchStart=function(e){"undefined"!=typeof t&&(t.mouse=t.touchPosition(e))},t.touchEnd=function(e){},t.touchPosition=function(e){if(!e||!e.touches)return null;var t=e.touches[0],n={};return n.x=t.pageX,n.y=t.pageY,n},t.mousePosition=function(e){return DayPilot.mo3(null,e)},DayPilot.Menu.touchPosition=function(e){e.touches&&(t.mouse=t.touchPosition(e))},DayPilot.Menu.hide=function(e){if(e=e||{},e.calendar){var n=DayPilot.Menu.active;if(n){var i=n.Tb.source;i&&i.calendar===e.calendar&&t.menuClean()}}else t.menuClean()},t.handlersRegistered||"undefined"==typeof document||(DayPilot.re(document,"mousemove",t.mouseMove),DayPilot.re(document,"mousedown",t.mouseDown),DayPilot.re(document,"wheel",t.wheel),DayPilot.re(document,"touchmove",t.touchMove),DayPilot.re(document,"touchstart",t.touchStart),DayPilot.re(document,"touchend",t.touchEnd),t.handlersRegistered=!0),DayPilot.Menu.def={}}}(DayPilot),"undefined"==typeof DayPilot&&(DayPilot={}),function(DayPilot){"use strict";function e(t,n,i){var a=n.indexOf(".");if(a===-1)return void("__proto__"!==n&&"constructor"!==n&&(t[n]=i));var o=n.substring(0,a);if("__proto__"!==o&&"constructor"!==o){var r=n.substring(a+1),s=t[o];"object"==typeof s&&null!==s||(t[o]={},s=t[o]),e(s,r,i)}}function t(e,n,i){n=n||{},i=i||"";for(var a in e){var o=e[a];"object"==typeof o?"[object Array]"===Object.prototype.toString.call(o)?n[i+a]=o:o&&o.toJSON?n[i+a]=o.toJSON():t(o,n,i+a+"."):n[i+a]=o}return n}if(!DayPilot.ModalStatic){DayPilot.ModalStatic={},DayPilot.ModalStatic.list=[],DayPilot.ModalStatic.hide=function(){if(this.list.length>0){var e=this.list.pop();e&&e.hide()}},DayPilot.ModalStatic.remove=function(e){for(var t=DayPilot.ModalStatic.list,n=0;n<t.length;n++)if(t[n]===e)return void t.splice(n,1)},DayPilot.ModalStatic.close=function(e){DayPilot.ModalStatic.result(e),DayPilot.ModalStatic.hide()},DayPilot.ModalStatic.result=function(e){var t=DayPilot.ModalStatic.list;t.length>0&&(t[t.length-1].result=e)},DayPilot.ModalStatic.displayed=function(e){for(var t=DayPilot.ModalStatic.list,n=0;n<t.length;n++)if(t[n]===e)return!0;return!1},DayPilot.ModalStatic.stretch=function(){if(this.list.length>0){var e=this.list[this.list.length-1];e&&e.stretch()}},DayPilot.ModalStatic.last=function(){var e=DayPilot.ModalStatic.list;return e.length>0?e[e.length-1]:null};var n=function(){function e(){for(var e=document.querySelectorAll("style[nonce]"),t=0;t<e.length;t++){var n=e[t];if(n.nonce)return n.nonce}if(document.currentScript&&document.currentScript.nonce)return document.currentScript.nonce;
for(var i=document.querySelectorAll("script[nonce]"),a=0;a<i.length;a++){var o=i[a];if(o.nonce)return o.nonce}return""}if("undefined"==typeof window){var t={};return t.add=function(){},t.commit=function(){},t}var n=document.createElement("style");n.nonce=e(),n.styleSheet||n.appendChild(document.createTextNode("")),(document.head||document.getElementsByTagName("head")[0]).appendChild(n);var i=!!n.styleSheet,t={};return t.rules=[],t.commit=function(){try{i&&(n.styleSheet.cssText=this.rules.join("\n"))}catch(e){}},t.add=function(e,t,a){if(i)return void this.rules.push(e+"{"+t+"}");if(n.sheet.insertRule)"undefined"==typeof a&&(a=n.sheet.cssRules.length),n.sheet.insertRule(e+"{"+t+"}",a);else{if(!n.sheet.addRule)throw"No CSS registration method found";n.sheet.addRule(e,t,a)}},t},i="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogICB3aWR0aD0iMTAiCiAgIGhlaWdodD0iMTUiCj4KICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLDUpIj4KICAgIDxwYXRoCiAgICAgICBpZD0icGF0aDMxNzMiCiAgICAgICBzdHlsZT0iZmlsbDpub25lO3N0cm9rZTojOTk5OTk5O3N0cm9rZS13aWR0aDoxLjg1MTk2ODUzO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZSIKICAgICAgIGQ9Ik0gMC45NTQxNDgzOCwwLjY4MTYwMzEgNS4wMzkwNjI1LDUuNDExNTM4NiA5LjEyMzk3NjYsMC42ODE2MDMxIgogICAgICAgIC8+CiAgPC9nPgo8L3N2Zz4K",a=new n;a.add(".modal_default_main","border: 10px solid #ccc; max-width: 90%;"),a.add(".modal_default_main:focus","outline: none;"),a.add(".modal_default_content","padding: 10px 0px;"),a.add(".modal_default_inner","padding: 20px;"),a.add(".modal_default_input","padding: 10px 0px;"),a.add(".modal_default_buttons","margin-top: 10px;"),a.add(".modal_default_buttons","padding: 10px 0px;"),a.add(".modal_default_form_item","padding: 10px 0px; position: relative;"),a.add(".modal_default_form_item_level1","border-left: 2px solid #ccc; margin-left: 10px; padding-left: 20px;"),a.add(".modal_default_form_item.modal_default_form_title","font-size: 1.5rem; font-weight: bold;"),a.add(".modal_default_form_item input[type=text]","width: 100%; box-sizing: border-box;"),a.add(".modal_default_form_item textarea","width: 100%; height: 200px; box-sizing: border-box;"),a.add(".modal_default_form_item input[type=select]","width: 100%; box-sizing: border-box;"),a.add(".modal_default_form_item label","display: block;"),a.add(".modal_default_form_item select","width: 100%; box-sizing: border-box;"),a.add(".modal_default_form_item_label","margin: 2px 0px;"),a.add(".modal_default_form_item_image img","max-width: 100%; height: auto;"),a.add(".modal_default_form_item_invalid",""),a.add(".modal_default_form_item_invalid_message","position: absolute; right: 0px; top: 9px; background-color: red; color: #ffffff; padding: 2px; border-radius: 2px;"),a.add(".modal_default_background","opacity: 0.5; background-color: #000;"),a.add(".modal_default_ok","padding: 3px; width: 80px;"),a.add(".modal_default_cancel","padding: 3px; width: 80px;"),a.add(".modal_default_form_item_date","position: relative;"),a.add(".modal_default_form_item_date:after","content: ''; position: absolute; right: 7px; top: 50%; margin-top: 3px; width: 10px; height: 15px; background-image:url("+"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogICB3aWR0aD0iMTAiCiAgIGhlaWdodD0iMTUiCj4KICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLDUpIj4KICAgIDxyZWN0CiAgICAgICBzdHlsZT0iZmlsbDojY2NjY2NjO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lO3N0cm9rZS13aWR0aDoxLjM4MDM3MzM2O3N0cm9rZS1saW5lY2FwOmJ1dHQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS1taXRlcmxpbWl0OjQ7c3Ryb2tlLWRhc2hhcnJheTpub25lO3N0cm9rZS1vcGFjaXR5OjEiCiAgICAgICBpZD0icmVjdDE5MjgiCiAgICAgICB3aWR0aD0iOS45MTUzMDYxIgogICAgICAgaGVpZ2h0PSIxMS4zNjkzNyIKICAgICAgIHg9IjAuMTE3MTg3NSIKICAgICAgIHk9Ii0zLjAwOTk5NTciCiAgICAgICByeT0iMS4zMTE4NTA1IiAvPgogICAgPHJlY3QKICAgICAgIHN0eWxlPSJmaWxsOiNjY2NjY2M7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmU7c3Ryb2tlLXdpZHRoOjEuNTk4MTQwMTI7c3Ryb2tlLWxpbmVjYXA6YnV0dDtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLW1pdGVybGltaXQ6NDtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MSIKICAgICAgIGlkPSJyZWN0MTkzMCIKICAgICAgIHdpZHRoPSIxLjUzNDQxMzYiCiAgICAgICBoZWlnaHQ9IjIuMjE5ODI1IgogICAgICAgeD0iMi4xNTU4NDgzIgogICAgICAgeT0iLTQuMzkzNzAwMSIKICAgICAgIHJ5PSIwLjY3MTc4OTE3IiAvPgogICAgPHJlY3QKICAgICAgIHJ5PSIwLjI5NjAxNDciCiAgICAgICB5PSItMS4xNjU4NDY2IgogICAgICAgeD0iMS41MjM5NTA2IgogICAgICAgaGVpZ2h0PSIxLjgyOTkwOTEiCiAgICAgICB3aWR0aD0iMS44MzQyMjUxIgogICAgICAgaWQ9InJlY3QxOTQ4IgogICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZTtzdHJva2Utd2lkdGg6MS40MjE4OTE5MztzdHJva2UtbGluZWNhcDpidXR0O3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2Utb3BhY2l0eToxIiAvPgogICAgPHJlY3QKICAgICAgIHJ5PSIwLjY3MTc4OTE3IgogICAgICAgeT0iLTQuMzkzNzAwMSIKICAgICAgIHg9IjYuNDUyNzIzNSIKICAgICAgIGhlaWdodD0iMi4yMTk4MjUiCiAgICAgICB3aWR0aD0iMS41MzQ0MTM2IgogICAgICAgaWQ9InJlY3QyMDAzIgogICAgICAgc3R5bGU9ImZpbGw6I2NjY2NjYztmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZTtzdHJva2Utd2lkdGg6MS41OTgxNDAxMjtzdHJva2UtbGluZWNhcDpidXR0O3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2Utb3BhY2l0eToxIiAvPgogICAgPHJlY3QKICAgICAgIHN0eWxlPSJmaWxsOiNmZmZmZmY7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmU7c3Ryb2tlLXdpZHRoOjEuNDIxODkxOTM7c3Ryb2tlLWxpbmVjYXA6YnV0dDtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLW1pdGVybGltaXQ6NDtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MSIKICAgICAgIGlkPSJyZWN0MjAwNSIKICAgICAgIHdpZHRoPSIxLjgzNDIyNTEiCiAgICAgICBoZWlnaHQ9IjEuODI5OTA5MSIKICAgICAgIHg9IjQuMjE5MjYzMSIKICAgICAgIHk9Ii0xLjE2NTg0NjYiCiAgICAgICByeT0iMC4yOTYwMTQ3IiAvPgogICAgPHJlY3QKICAgICAgIHJ5PSIwLjI5NjAxNDciCiAgICAgICB5PSItMS4xNjU4NDY2IgogICAgICAgeD0iNi45OTI3MDA2IgogICAgICAgaGVpZ2h0PSIxLjgyOTkwOTEiCiAgICAgICB3aWR0aD0iMS44MzQyMjUxIgogICAgICAgaWQ9InJlY3QyMDA3IgogICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZTtzdHJva2Utd2lkdGg6MS40MjE4OTE5MztzdHJva2UtbGluZWNhcDpidXR0O3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2Utb3BhY2l0eToxIiAvPgogICAgPHJlY3QKICAgICAgIHN0eWxlPSJmaWxsOiNmZmZmZmY7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmU7c3Ryb2tlLXdpZHRoOjEuNDIxODkxOTM7c3Ryb2tlLWxpbmVjYXA6YnV0dDtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLW1pdGVybGltaXQ6NDtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MSIKICAgICAgIGlkPSJyZWN0MjAxMyIKICAgICAgIHdpZHRoPSIxLjgzNDIyNTEiCiAgICAgICBoZWlnaHQ9IjEuODI5OTA5MSIKICAgICAgIHg9IjEuNTIzOTUwNiIKICAgICAgIHk9IjEuODAyOTAzNCIKICAgICAgIHJ5PSIwLjI5NjAxNDciIC8+CiAgICA8cmVjdAogICAgICAgcnk9IjAuMjk2MDE0NyIKICAgICAgIHk9IjEuODAyOTAzNCIKICAgICAgIHg9IjQuMjE5MjYzMSIKICAgICAgIGhlaWdodD0iMS44Mjk5MDkxIgogICAgICAgd2lkdGg9IjEuODM0MjI1MSIKICAgICAgIGlkPSJyZWN0MjAxNSIKICAgICAgIHN0eWxlPSJmaWxsOiNmZmZmZmY7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmU7c3Ryb2tlLXdpZHRoOjEuNDIxODkxOTM7c3Ryb2tlLWxpbmVjYXA6YnV0dDtzdHJva2UtbGluZWpvaW46cm91bmQ7c3Ryb2tlLW1pdGVybGltaXQ6NDtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MSIgLz4KICAgIDxyZWN0CiAgICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lO3N0cm9rZS13aWR0aDoxLjQyMTg5MTkzO3N0cm9rZS1saW5lY2FwOmJ1dHQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS1taXRlcmxpbWl0OjQ7c3Ryb2tlLWRhc2hhcnJheTpub25lO3N0cm9rZS1vcGFjaXR5OjEiCiAgICAgICBpZD0icmVjdDIwMTciCiAgICAgICB3aWR0aD0iMS44MzQyMjUxIgogICAgICAgaGVpZ2h0PSIxLjgyOTkwOTEiCiAgICAgICB4PSI2Ljk5MjcwMDYiCiAgICAgICB5PSIxLjgwMjkwMzQiCiAgICAgICByeT0iMC4yOTYwMTQ3IiAvPgogICAgPHJlY3QKICAgICAgIHJ5PSIwLjI5NjAxNDciCiAgICAgICB5PSI0LjczMjU5MDciCiAgICAgICB4PSIxLjU2MzAxMzEiCiAgICAgICBoZWlnaHQ9IjEuODI5OTA5MSIKICAgICAgIHdpZHRoPSIxLjgzNDIyNTEiCiAgICAgICBpZD0icmVjdDIwMTkiCiAgICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lO3N0cm9rZS13aWR0aDoxLjQyMTg5MTkzO3N0cm9rZS1saW5lY2FwOmJ1dHQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS1taXRlcmxpbWl0OjQ7c3Ryb2tlLWRhc2hhcnJheTpub25lO3N0cm9rZS1vcGFjaXR5OjEiIC8+CiAgICA8cmVjdAogICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZTtzdHJva2Utd2lkdGg6MS40MjE4OTE5MztzdHJva2UtbGluZWNhcDpidXR0O3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2Utb3BhY2l0eToxIgogICAgICAgaWQ9InJlY3QyMDIxIgogICAgICAgd2lkdGg9IjEuODM0MjI1MSIKICAgICAgIGhlaWdodD0iMS44Mjk5MDkxIgogICAgICAgeD0iNC4yNTgzMjU2IgogICAgICAgeT0iNC43MzI1OTA3IgogICAgICAgcnk9IjAuMjk2MDE0NyIgLz4KICAgIDxyZWN0CiAgICAgICByeT0iMC4yOTYwMTQ3IgogICAgICAgeT0iNC43MzI1OTA3IgogICAgICAgeD0iNy4wMzE3NjMxIgogICAgICAgaGVpZ2h0PSIxLjgyOTkwOTEiCiAgICAgICB3aWR0aD0iMS44MzQyMjUxIgogICAgICAgaWQ9InJlY3QyMDIzIgogICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZTtzdHJva2Utd2lkdGg6MS40MjE4OTE5MztzdHJva2UtbGluZWNhcDpidXR0O3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2Utb3BhY2l0eToxIiAvPgogIDwvZz4KPC9zdmc+Cg=="+")"),"undefined"!=typeof navigator&&navigator.userAgent.indexOf("Edge")!==-1&&a.add(".modal_default_form_item_date input::-ms-clear","display: none;"),a.add(".modal_default_form_item_scrollable_scroll","width: 100%; height: 200px; box-sizing: border-box; border: 1px solid #ccc; overflow-y: auto;"),a.add(".modal_default_form_item_scrollable_scroll_content","padding: 5px;"),a.add(".modal_default_form_item_searchable","position: relative;"),a.add(".modal_default_form_item_searchable_icon",""),a.add(".modal_default_form_item_searchable_icon:after","content:''; position: absolute; right: 5px; top: 50%; margin-top: -8px; width: 10px; height: 15px; background-image:url("+i+");"),a.add(".modal_default_form_item_searchable_list","box-sizing: border-box; border: 1px solid #999; max-height: 150px; overflow-y: auto;"),a.add(".modal_default_form_item_searchable_list_item","background: white; padding: 2px; cursor: default;"),a.add(".modal_default_form_item_searchable_list_item_highlight","background: #ccc;"),a.add(".modal_default_form_item_time","position: relative;"),a.add(".modal_default_form_item_time_icon",""),a.add(".modal_default_form_item_time_icon:after","content:''; position: absolute; right: 5px; top: 50%; margin-top: -8px; width: 10px; height: 15px; background-image:url("+i+");"),a.add(".modal_default_form_item_time_list","box-sizing: border-box; border: 1px solid #999; max-height: 150px; overflow-y: auto;"),a.add(".modal_default_form_item_time_list_item","background: white; padding: 2px; cursor: default;"),a.add(".modal_default_form_item_time_list_item_highlight","background: #ccc;"),a.add(".modal_default_form_item_datetime_parent","display: flex;"),a.add(".modal_default_form_item_datetime .modal_default_form_item_time_main","margin-left: 5px;"),a.add(".modal_default_form_item_datetime input[type='text'].modal_default_input_date ",""),a.add(".modal_default_form_item_tabular_main","margin-top: 10px;"),a.add(".modal_default_form_item_tabular_table","display: table; width: 100%; xbackground-color: #fff; border-collapse: collapse;"),a.add(".modal_default_form_item_tabular_tbody","display: table-row-group;"),a.add(".modal_default_form_item_tabular_row","display: table-row;"),a.add(".modal_default_form_item_tabular_row.modal_default_form_item_tabular_header",""),a.add(".modal_default_form_item_tabular_cell.modal_default_form_item_tabular_rowaction","padding: 0px; width: 23px;"),a.add(".modal_default_form_item_tabular_cell","display: table-cell; border: 0px; padding: 2px 2px 2px 0px; cursor: default; vertical-align: bottom;"),a.add(".modal_default_form_item_tabular_header .modal_default_form_item_tabular_cell","padding-left: 0px; padding-bottom: 0px;"),a.add(".modal_default_form_item_tabular_table input[type=text], .modal_default_form_item_tabular_table input[type=number]","width:100%; box-sizing: border-box;"),a.add(".modal_default_form_item_tabular_table select","width:100%; height:100%; box-sizing: border-box;"),a.add(".modal_default_form_item_tabular_plus","display: inline-block; background-color: #ccc; color: white; width: 20px; height: 20px; border-radius: 10px; box-sizing: border-box; position: relative; margin-left: 3px; margin-top: 3px; cursor: pointer;"),a.add(".modal_default_form_item_tabular_plus:after","content: ''; position: absolute; left: 5px; top: 5px; width: 10px; height: 10px;   background-image: url(\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSA1LjAgMC41IEwgNS4wIDkuNSBNIDAuNSA1LjAgTCA5LjUgNS4wJyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojZmZmZmZmO3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==\")"),a.add(".modal_default_form_item_tabular_delete","display: inline-block; background-color: #ccc; color: white; width: 20px; height: 20px; border-radius: 10px; box-sizing: border-box; position: relative; margin-left: 3px; margin-top: 3px; cursor: pointer;"),a.add(".modal_default_form_item_tabular_delete:after","content: ''; position: absolute; left: 5px; top: 5px; width: 10px; height: 10px;   background-image: url(\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTAnIGhlaWdodD0nMTAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTSAwLjUgMC41IEwgOS41IDkuNSBNIDAuNSA5LjUgTCA5LjUgMC41JyBzdHlsZT0nZmlsbDpub25lO3N0cm9rZTojZmZmZmZmO3N0cm9rZS13aWR0aDoyO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbGluZWNhcDpidXR0JyAvPjwvc3ZnPg==\")"),a.add(".modal_default_form_item_tabular_disabled .modal_default_form_item_tabular_plus","display: none;"),a.add(".modal_default_form_item_tabular_plus_max.modal_default_form_item_tabular_plus","display: none;"),a.add(".modal_default_form_item_tabular_disabled .modal_default_form_item_tabular_delete","visibility: hidden;"),a.add(".modal_default_form_item_tabular_empty","height: 1px; margin: 5px 23px 5px 0px; background-color: #ccc;"),a.add(".modal_default_form_item_tabular_spacer .modal_default_form_item_tabular_cell","padding: 0px;"),a.add(".modal_min_main","border: 1px solid #ccc; max-width: 90%;"),a.add(".modal_min_background","opacity: 0.5; background-color: #000;"),a.add(".modal_min_ok","padding: 3px 10px;"),a.add(".modal_min_cancel","padding: 3px 10px;"),a.add(".navigator_modal_main","border-left: 1px solid #c0c0c0;border-right: 1px solid #c0c0c0;border-bottom: 1px solid #c0c0c0;background-color: white;color: #000000; box-sizing: content-box;"),a.add(".navigator_modal_main *, .navigator_modal_main *:before, .navigator_modal_main *:after","box-sizing: content-box;"),a.add(".navigator_modal_month","font-size: 11px;"),a.add(".navigator_modal_day","color: black;"),a.add(".navigator_modal_weekend","background-color: #f0f0f0;"),a.add(".navigator_modal_dayheader","color: black;"),a.add(".navigator_modal_line","border-bottom: 1px solid #c0c0c0;"),a.add(".navigator_modal_dayother","color: gray;"),a.add(".navigator_modal_todaybox","border: 1px solid red;"),a.add(".navigator_modal_title, .navigator_modal_titleleft, .navigator_modal_titleright","border-top: 1px solid #c0c0c0;border-bottom: 1px solid #c0c0c0;color: #333;background: #f3f3f3;"),a.add(".navigator_modal_busy","font-weight: bold;"),a.add(".navigator_modal_cell","text-align: center;"),a.add(".navigator_modal_select .navigator_modal_cell_box","background-color: #FFE794; opacity: 0.5;"),a.add(".navigator_modal_title","text-align: center;"),a.add(".navigator_modal_titleleft, .navigator_modal_titleright","text-align: center;"),a.add(".navigator_modal_dayheader","text-align: center;"),a.add(".navigator_modal_weeknumber","text-align: center;"),a.add(".navigator_modal_cell_text","cursor: pointer;"),a.add(".navigator_modal_todaysection","box-sizing: border-box; display: flex; align-items: center; justify-content: center; border-top: 1px solid var(--dp-nav-border-color);"),a.add(".navigator_modal_todaysection_button","cursor: pointer; color: #333; background-color: #f0f0f0; border: 1px solid var(--dp-nav-border-color); padding: 5px 10px; border-radius: 0px; "),a.commit(),DayPilot.Modal=function(e){this.autoFocus=!0,this.focus=null,this.autoStretch=!0,this.autoStretchFirstLoadOnly=!1,this.className=null,this.theme="modal_default",this.disposeOnClose=!0,this.dragDrop=!0,this.loadingHtml=null,this.maxHeight=null,this.scrollWithPage=!0,this.useIframe=!0,this.zIndex=99999,this.left=null,this.width=600,this.top=20,this.height=200,this.locale=null,this.closed=null,this.onClose=null,this.onClosed=null,this.onShow=null;var t=this;this.id="_"+(new Date).getTime()+"n"+10*Math.random(),this._b=!1,this.bc=null,this.cc=null,this.showHtml=function(e){if(DayPilot.ModalStatic.displayed(this))throw"This modal dialog is already displayed.";if(this.div||this.dc(),this.sb(),this.useIframe){var t=function(e,t){return function(){e.setInnerHTML(e.id+"iframe",t)}};window.setTimeout(t(this,e),0)}else e.nodeType?this.div.appendChild(e):this.div.innerHTML=e;this.sb(),this.ec(),this.fc()},this.showUrl=function(e){if(DayPilot.ModalStatic.displayed(this))throw"This modal dialog is already displayed.";if(this.useIframe){this.div||this.dc();var n=this.loadingHtml;n&&(this.iframe.src="about:blank",this.setInnerHTML(this.id+"iframe",n)),this.re(this.iframe,"load",this.gc),this.iframe.src=e,this.sb(),this.ec(),this.fc()}else t.hc({"url":e,"success":function(e){var n=e.request.responseText;t.showHtml(n)},"error":function(e){t.showHtml("Error loading the modal dialog")}})},this.fc=function(){if("function"==typeof t.onShow){var e={};e.root=t.ic(),e.modal=t,t.onShow(e)}},this.ic=function(){return t.iframe?t.iframe.contentWindow.document:t.div},this.hc=function(e){var t=new XMLHttpRequest;if(t){var n=e.method||"GET",i=e.success||function(){},a=e.error||function(){},o=e.data,r=e.url;t.open(n,r,!0),t.setRequestHeader("Content-type","text/plain"),t.onreadystatechange=function(){if(4===t.readyState)if(200===t.status||304===t.status){var e={};e.request=t,i(e)}else if(a){var e={};e.request=t,a(e)}else window.console&&console.log("HTTP error "+t.status)},4!==t.readyState&&("object"==typeof o&&(o=JSON.stringify(o)),t.send(o))}},this.sb=function(){delete this.result;var e=window,n=document,i=e.pageYOffset?e.pageYOffset:n.documentElement&&n.documentElement.scrollTop?n.documentElement.scrollTop:n.body.scrollTop;this.theme&&(this.hideDiv.className=this.theme+"_background"),this.zIndex&&(this.hideDiv.style.zIndex=this.zIndex),this.hideDiv.style.display="",window.setTimeout(function(){t.hideDiv&&(t.hideDiv.onclick=function(){t.hide({"backgroundClick":!0})})},500),this.theme?this.div.className=this.theme+"_main":this.div.className="",this.className&&(this.div.className+=" "+this.className),this.left?this.div.style.left=this.left+"px":this.div.style.marginLeft="-"+Math.floor(this.width/2)+"px",this.div.style.position="absolute",this.div.style.boxSizing="content-box",this.div.style.top=i+this.top+"px",this.div.style.width=this.width+"px",this.zIndex&&(this.div.style.zIndex=this.zIndex),this.height&&(this.useIframe||!this.autoStretch?this.div.style.height=this.height+"px":this.div.style.height=""),this.useIframe&&this.height&&(this.iframe.style.height=this.height+"px"),this.div.style.display="",this.kc(),DayPilot.ModalStatic.remove(this),DayPilot.ModalStatic.list.push(this)},this.gc=function(){t.iframe.contentWindow.modal=t,t.autoStretch&&t.stretch()},this.stretch=function(){var e=function(){return t.jc().y},n=function(){return t.jc().x};if(this.useIframe){for(var i=n()-40,a=this.width;a<i&&this.lc();a+=10)this.div.style.width=a+"px",this.div.style.marginLeft="-"+Math.floor(a/2)+"px";for(var o=this.maxHeight||e()-2*this.top,r=this.height;r<o&&this.nc();r+=10)this.iframe.style.height=r+"px",this.div.style.height=r+"px";this.autoStretchFirstLoadOnly&&this.ue(this.iframe,"load",this.gc)}else this.div.style.height=""},this.lc=function(){for(var e=this.iframe.contentWindow.document,t="BackCompat"===e.compatMode?e.body:e.documentElement,n=t.scrollWidth,i=e.body.children,a=0;a<i.length;a++){var o=i[a].offsetLeft+i[a].offsetWidth;n=Math.max(n,o)}return n>t.clientWidth},this.nc=function(){for(var e=this.iframe.contentWindow.document,t="BackCompat"===e.compatMode?e.body:e.documentElement,n=t.scrollHeight,i=e.body.children,a=0;a<i.length;a++){var o=i[a].offsetTop+i[a].offsetHeight;n=Math.max(n,o)}return n>t.clientHeight},this.jc=function(){var e=document;if("CSS1Compat"===e.compatMode&&e.documentElement&&e.documentElement.clientWidth){var t=e.documentElement.clientWidth,n=e.documentElement.clientHeight;return{x:t,y:n}}var t=e.body.clientWidth,n=e.body.clientHeight;return{x:t,y:n}},this.ec=function(){this._b||(this.re(window,"resize",this.oc),this.re(window,"scroll",this.pc),this.dragDrop&&(this.re(document,"mousemove",this.qc),this.re(document,"mouseup",this.rc)),this._b=!0)},this.sc=function(){this.ue(window,"resize",this.oc),this.ue(window,"scroll",this.pc),this.dragDrop&&(this.ue(document,"mousemove",this.qc),this.ue(document,"mouseup",this.rc)),this._b=!1},this.tc=function(e){e.target===t.div&&(e.preventDefault(),t.div.style.cursor="move",t.uc(),t.cc=t.mc(e||window.event),t.bc={x:t.div.offsetLeft,y:t.div.offsetTop})},this.qc=function(e){if(t.cc){var e=e||window.event,n=t.mc(e),i=n.x-t.cc.x,a=n.y-t.cc.y;t.div.style.marginLeft="0px",t.div.style.top=t.bc.y+a+"px",t.div.style.left=t.bc.x+i+"px"}},this.rc=function(e){t.cc&&(t.vc(),t.div.style.cursor=null,t.cc=null)},this.uc=function(){if(this.useIframe){var e=document.createElement("div");e.style.backgroundColor="#ffffff",e.style.filter="alpha(opacity=80)",e.style.opacity="0.80",e.style.width="100%",e.style.height=this.height+"px",e.style.position="absolute",e.style.left="0px",e.style.top="0px",this.div.appendChild(e),this.mask=e}},this.vc=function(){this.useIframe&&(this.div.removeChild(this.mask),this.mask=null)},this.oc=function(){t.wc(),t.kc()},this.pc=function(){t.wc()},this.kc=function(){if(!t.left&&t.div){var e=t.div.offsetWidth;t.div.style.marginLeft="-"+Math.floor(e/2)+"px"}},this.wc=function(){if(t.hideDiv&&t.div&&"none"!==t.hideDiv.style.display&&"none"!==t.div.style.display){var e=t.xc.scrollY();t.scrollWithPage||(t.div.style.top=e+t.top+"px")}},this.xc={},this.xc.container=function(){return t.container||document.body},this.xc.scrollY=function(){var e=t.xc.container();return e===document.body?window.pageYOffset?window.pageYOffset:document.documentElement&&document.documentElement.scrollTop?document.documentElement.scrollTop:document.body.scrollTop:e.scrollTop},this.re=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on"+t,n)},this.ue=function(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent&&e.detachEvent("on"+t,n)},this.mc=function(e){return e.pageX||e.pageY?{x:e.pageX,y:e.pageY}:{x:e.clientX+document.documentElement.scrollLeft,y:e.clientY+document.documentElement.scrollTop}},this.abs=function(e){for(var t={x:e.offsetLeft,y:e.offsetTop};e.offsetParent;)e=e.offsetParent,t.x+=e.offsetLeft,t.y+=e.offsetTop;return t},this.dc=function(){var e=t.xc.container(),n=e===document.body,i=n?"fixed":"absolute",a=document.createElement("div");a.id=this.id+"hide",a.style.position=i,a.style.left="0px",a.style.top="0px",a.style.right="0px",a.style.bottom="0px",a.oncontextmenu=function(){return!1},a.onmousedown=function(){return!1},e.appendChild(a);var o=document.createElement("div");o.id=this.id+"popup",o.style.position=i,o.style.left="50%",o.style.top="0px",o.style.backgroundColor="white",o.style.width="50px",o.style.height="50px",this.dragDrop&&(o.onmousedown=this.tc),o.addEventListener("keydown",function(e){e.stopPropagation()});var r=null;this.useIframe&&(r=document.createElement("iframe"),r.id=this.id+"iframe",r.name=this.id+"iframe",r.frameBorder="0",r.style.width="100%",r.style.height="50px",o.appendChild(r)),e.appendChild(o),this.div=o,this.iframe=r,this.hideDiv=a},this.setInnerHTML=function(e,n){var i=window.frames[e],a=i.contentWindow||i.document||i.contentDocument;a.document&&(a=a.document),null==a.body&&a.write("<body></body>"),n.nodeType?a.body.appendChild(n):a.body.innerHTML=n,t.autoStretch&&(t.autoStretchFirstLoadOnly&&t.yc||(t.stretch(),t.yc=!0))},this.close=function(e){this.result=e,this.hide()},this.closeSerialized=function(){for(var e=t.ic(),n=e.querySelectorAll("input, textarea, select"),i={},a=0;a<n.length;a++){var o=n[a],r=o.name;if(r){var s=o.value;i[r]=s}}t.close(i)},this.hide=function(e){e=e||{};var n={};n.backgroundClick=!!e.backgroundClick,n.result=this.result,n.canceled="undefined"==typeof this.result,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof this.onClose&&(this.onClose(n),n.preventDefault.value)||(this.div&&(this.div.style.display="none",this.hideDiv.style.display="none",this.useIframe||(this.div.innerHTML=null)),window.focus(),DayPilot.ModalStatic.remove(this),"function"==typeof this.onClosed?this.onClosed(n):this.closed&&this.closed(),delete this.result,this.disposeOnClose&&(t.sc(),t.zc(t.div),t.zc(t.hideDiv),t.div=null,t.hideDiv=null,t.iframe=null))},this.zc=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},this.Ac=function(){if(e)for(var t in e)this[t]=e[t]},this.Ac()},DayPilot.Modal.alert=function(e,t){t=t||{},t.height=t.height||40,t.useIframe=!1;var n=t.okText||"OK";t.cancelText||"Cancel";return DayPilot.getPromise(function(i,a){t.onClosed=function(e){i(e)};var o=new DayPilot.Modal(t),r=document.createElement("div");r.className=o.theme+"_inner";var s=document.createElement("div");s.className=o.theme+"_content",s.innerHTML=e;var l=document.createElement("div");l.className=o.theme+"_buttons";var d=document.createElement("button");d.innerText=n,d.className=o.theme+"_ok",d.onclick=function(e){DayPilot.ModalStatic.close("OK")},l.appendChild(d),r.appendChild(s),r.appendChild(l),o.showHtml(r),o.autoFocus&&d.focus()})},DayPilot.Modal.confirm=function(e,t){t=t||{},t.height=t.height||40,t.useIframe=!1;var n=t.okText||"OK",i=t.cancelText||"Cancel";return DayPilot.getPromise(function(a,o){t.onClosed=function(e){a(e)};var r=new DayPilot.Modal(t),s=document.createElement("div");s.className=r.theme+"_inner";var l=document.createElement("div");l.className=r.theme+"_content",l.innerHTML=e;var d=document.createElement("div");d.className=r.theme+"_buttons";var c=document.createElement("button");c.innerText=n,c.className=r.theme+"_ok",c.onclick=function(e){DayPilot.ModalStatic.close("OK")};var u=document.createTextNode(" "),h=document.createElement("button");h.innerText=i,h.className=r.theme+"_cancel",h.onclick=function(e){DayPilot.ModalStatic.close()},d.appendChild(c),d.appendChild(u),d.appendChild(h),s.appendChild(l),s.appendChild(d),r.showHtml(s),r.autoFocus&&c.focus()})},DayPilot.Modal.prompt=function(e,t,n){"object"==typeof t&&(n=t,t=""),n=n||{},n.height=n.height||40,n.useIframe=!1;var i=n.okText||"OK",a=n.cancelText||"Cancel",o=t||"";return DayPilot.getPromise(function(t,r){n.onClosed=function(e){t(e)};var s=new DayPilot.Modal(n),l=document.createElement("div");l.className=s.theme+"_inner";var d=document.createElement("div");d.className=s.theme+"_content",d.innerHTML=e;var c=document.createElement("div");c.className=s.theme+"_input";var u=document.createElement("input");u.value=o,u.style.width="100%",u.onkeydown=function(e){var t=!1;switch(e.keyCode){case 13:s.close(this.value);break;case 27:s.close();break;default:t=!0}t||(e.preventDefault(),e.stopPropagation())},c.appendChild(u);var h=document.createElement("div");h.className=s.theme+"_buttons";var f=document.createElement("button");f.innerText=i,f.className=s.theme+"_ok",f.onclick=function(e){s.close(u.value)};var v=document.createTextNode(" "),p=document.createElement("button");p.innerText=a,p.className=s.theme+"_cancel",p.onclick=function(e){s.close()},h.appendChild(f),h.appendChild(v),h.appendChild(p),l.appendChild(d),l.appendChild(c),l.appendChild(h),s.showHtml(l),s.autoFocus&&u.focus()})};var o=function(e){return"[object Array]"===Object.prototype.toString.call(e)};DayPilot.Modal.form=function(t,n,i){if(1===arguments.length){var a=t;if(o(a))n={};else{if("object"!=typeof a)throw"Invalid DayPilot.Modal.form() parameter";n=t,t=[];for(var s in n){var l={};l.name=s,l.id=s,t.push(l)}}}var d={};for(var s in i)d[s]=i[s];d.height=d.height||40,d.useIframe=!1;var c=d.okText||"OK",u=d.cancelText||"Cancel";return DayPilot.getPromise(function(i,a){d.onClosed=function(t){if(t.result){var a=JSON.parse(JSON.stringify(n));for(var o in t.result)e(a,o,t.result[o]);t.result=a}i(t)};var o=new DayPilot.Modal(d),s=document.createElement("div");s.className=o.theme+"_inner";var l=document.createElement("div");l.className=o.theme+"_input";var h=new r({theme:o.theme,form:t,data:n,zIndex:o.zIndex,locale:o.locale,plugins:o.plugins,onKey:function(e){switch(e.key){case"Enter":h.validate()&&o.close(h.serialize());break;case"Escape":o.close()}},onChange:function(e){"function"==typeof o.onChange&&o.onChange(e)}}),f=h.create();l.append(f);var v=document.createElement("div");v.className=o.theme+"_buttons";var p=document.createElement("button");p.innerText=c,p.className=o.theme+"_ok",d.okDisabled&&(p.disabled=!0),p.onclick=function(e){h.validate()&&o.close(h.serialize())};var m=document.createTextNode(" "),y=document.createElement("button");if(y.innerText=u,y.className=o.theme+"_cancel",y.onclick=function(e){o.close()},y.onmousedown=function(e){h.canceling=!0},v.appendChild(p),v.appendChild(m),v.appendChild(y),s.appendChild(l),s.appendChild(v),o.showHtml(s),o.div.setAttribute("tabindex","-1"),o.div.addEventListener("keydown",function(e){switch(e.keyCode){case 27:o.close();break;case 13:h.validate()&&o.close(h.serialize())}}),o.focus){var g=null;if("object"==typeof o.focus){var b=o.focus.id,w=o.focus.value;g=h.findViewById(b,w)}else"string"==typeof o.focus&&(g=h.findViewById(o.focus));g&&g.focus()}else{var D=h.firstFocusable();o.autoFocus&&D?D.focus():o.div.focus()}})},DayPilot.Modal.close=function(e){var t=DayPilot.Modal.opener();t&&t.close(e)},DayPilot.Modal.stretch=function(e){var t=DayPilot.Modal.opener();if(!t)throw"Unable to find the opener DayPilot.Modal instance.";t.stretch()},DayPilot.Modal.closeSerialized=function(){var e=DayPilot.Modal.opener()||DayPilot.ModalStatic.last();e&&e.closeSerialized()},DayPilot.Modal.opener=function(){return"undefined"!=typeof DayPilot&&"undefined"!=typeof DayPilot.ModalStatic&&DayPilot.ModalStatic.list.length>0?DayPilot.ModalStatic.list[DayPilot.ModalStatic.list.length-1]:parent&&parent.DayPilot&&parent.DayPilot.ModalStatic&&parent.DayPilot.ModalStatic.list[parent.DayPilot.ModalStatic.list.length-1]},DayPilot.Modal.Experimental={},DayPilot.Modal.Experimental.Form=r,"undefined"==typeof DayPilot.getPromise&&(DayPilot.getPromise=function(e){return"undefined"!=typeof Promise?new Promise(e):(DayPilot.Promise=function(e){var t=this;this.then=function(t,n){return t=t||function(){},n=n||function(){},e(t,n),DayPilot.getPromise(e)},this["catch"]=function(n){return t.then(null,n),DayPilot.getPromise(e)}},new DayPilot.Promise(e))});var r=function(e){this.form=[],this.data={},this.theme="form_default",this.zIndex=99999,this.locale="en-us",this.plugins={},this.onKey=null,this.Bc=[],this.Cc=null,this.canceling=!1,this.Dc=[],this.f=[],this.Ec=null,e=e||{};for(var t in e)this[t]=e[t]};r.prototype.create=function(){return this.load(),this.render(),this.Ec},r.prototype.render=function(){var e=this;this.Ec=document.createElement("div"),this.Bc.forEach(function(t){e.createView(t)}),this.applyState()},r.prototype.createView=function(e){var t=this.theme,n=this,i=document.createElement("div");if(i.className=t+"_form_item "+t+"_form_item_level"+e.level,e.interactive||"title"!==e.type?i.className+=" "+t+"_form_item_"+e.type:i.className+=" "+t+"_form_title",e.data.cssClass&&(i.className+=" "+e.data.cssClass),!e.isValue){var a=document.createElement("div");a.className=t+"_form_item_label",a.innerText=e.text,i.appendChild(a)}var o=this.createInteractive(e);o.onInput=function(e){if(e=e||{},n.Fc(o,{"debounce":!e.immediate}),"function"==typeof n.onChange){var t={};t.result=n.serialize(),n.onChange(t)}},o.onBlur=function(){n.canceling||n.Fc(o)},o.apply(e),o.Ec=i,o.row=e,o.element&&i.appendChild(o.element),this.f.push(o),this.Ec.appendChild(i)},r.prototype.validate=function(){var e=this,t=!0;return this.f.forEach(function(n){var i=e.Fc(n);t=t&&i;
}),t},r.prototype.Fc=function(e,t){function n(){e.Gc&&(e.Gc.remove(),e.Gc=null),e.Ec.classList.add(u);var t=document.createElement("div");t.classList.add(h),t.innerText=c.message,e.Gc=t,e.Ec.appendChild(t)}t=t||{};var i=t.debounce,a=t.silent,o=e.row,r=!0,s="function"==typeof o.data.onValidate?o.data.onValidate:null,l="function"==typeof o.data.validate?o.data.validate:null,d=s||l;if(d){var c={};c.valid=!0,c.value=e.save()[o.field],c.message="Error",c.values=this.serialize(),c.result=this.serialize(),d(c);var u=this.theme+"_form_item_invalid",h=this.theme+"_form_item_invalid_message";if(c.valid)clearTimeout(this.Dc[o.field]),e.Gc&&(e.Gc.remove(),e.Gc=null),e.Ec.classList.remove(u);else if(!a)if(i){var f=1e3;clearTimeout(this.Dc[o.field]),this.Dc[o.field]=setTimeout(function(){n()},f)}else n();r=c.valid}return r},r.prototype.load=function(){var e=this;this.form.forEach(function(t){e.processFormItem(t,0)});var n;try{var i=JSON.stringify(this.data);n=t(JSON.parse(i))}catch(e){throw new Error("The 'data' object is not serializable (it may contain circular dependencies): "+e)}for(var a in n)this.setValue(a,n[a])},r.prototype.setValue=function(e,t){this.Bc.forEach(function(n){n.applyValue(e,t)})},r.prototype.updateDependentState=function(){var e=this,t=[!0];(this.Cc?this.Cc:this.Bc).forEach(function(n){var i=e.updateState(n,{enabled:t[n.level]&&!n.data.disabled});i.isValue&&(t[i.level+1]=i.enabled&&i.checked)})},r.prototype.processFormItem=function(e,t){var n=this,i=this.getFieldType(e),a=[];if("radio"===i){if(e.name){var o=new s;o.field=e.id,o.data=e,o.level=t,o.type="label",o.interactive=!1,o.text=e.name,n.Bc.push(o),a.push(o)}e.options.forEach(function(o){var r=new s;r.field=e.id,r.data=o,r.level=t,r.type=i,r.isValue=!0,r.text=o.name,r.resolved=o.id,n.Bc.push(r),a.push(r),o.children&&o.children.forEach(function(e){var i=n.processFormItem(e,t+1);a=a.concat(i)})})}else if("title"===i){var o=new s;o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=e.name,n.Bc.push(o),a.push(o)}else if("image"===i){var o=new s;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,n.Bc.push(o),a.push(o)}else if("html"===i){var o=new s;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,n.Bc.push(o),a.push(o)}else if("scrollable"===i){var o=new s;o.isValue=!0,o.field=e.id,o.data=e,o.level=t,o.type=i,o.interactive=!1,o.text=null,n.Bc.push(o),a.push(o)}else{var o=new s;o.field=e.id,o.data=e,o.level=t,o.type=i,o.text=e.name,o.children=[],n.Bc.push(o),a.push(o)}return"checkbox"===i&&(o.isValue=!0,o.resolved=!0,e.children&&e.children.forEach(function(e){var i=n.processFormItem(e,t+1);a=a.concat(i)})),a},r.prototype.doOnKey=function(e){if("function"==typeof this.onKey){var t={key:e};this.onKey(t)}},r.prototype.createInteractive=function(e){var t=this,n={"label":function(){return new l},"title":function(){return new l},"image":function(){var t=new l,n=document.createElement("img");return n.src=e.data.image,t.element=n,t},"html":function(){var t=new l,n=document.createElement("div");return"string"==typeof e.data.text?n.innerText=e.data.text:"string"==typeof e.data.html&&(n.innerHTML=e.data.html),t.element=n,t},"scrollable":function(){var n=new l,i=document.createElement("div");i.className=t.theme+"_form_item_scrollable_scroll",e.data.height&&(i.style.height=e.data.height+"px");var a=document.createElement("div");return a.className=t.theme+"_form_item_scrollable_scroll_content","string"==typeof e.data.text?a.innerText=e.data.text:"string"==typeof e.data.html&&(a.innerHTML=e.data.html),i.appendChild(a),n.element=i,n},"text":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.element;t.value=e.value,t.disabled=!e.enabled};var i=document.createElement("input");return i.name=e.field,i.type="text",i.autocomplete="off",i.onkeydown=function(e){var n=!1;switch(e.keyCode){case 13:t.doOnKey("Enter");break;case 27:t.doOnKey("Escape");break;default:n=!0}n||(e.preventDefault(),e.stopPropagation())},i.oninput=function(e){n.onInput()},i.onblur=function(e){n.onBlur()},n.element=i,n.canFocus=function(){return!n.element.disabled},n.focus=function(){n.element.focus(),n.element.setSelectionRange(0,n.element.value.length)},n.save=function(){var t={};return t[e.field]=i.value,t},n},"textarea":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.element;t.value=e.value,t.disabled=!e.enabled};var i=document.createElement("textarea");return i.name=e.field,e.data.height&&(i.style.height=e.data.height+"px"),i.onkeydown=function(e){var n=!1;switch(e.keyCode){case 13:(e.ctrlKey||e.metaKey)&&t.doOnKey("Enter"),n=!1;break;case 27:t.doOnKey("Escape");break;default:n=!0}n||e.stopPropagation()},i.oninput=function(e){n.onInput()},i.onblur=function(e){n.onBlur()},n.element=i,n.canFocus=function(){return!n.element.disabled},n.focus=function(){n.element.focus(),n.element.setSelectionRange(0,0)},n.save=function(){var t={};return t[e.field]=i.value,t},n},"date":function(){var n=new l;n.apply=function(e){n.row=e;var i=n.element,a=n.picker;e.data.dateFormat&&(a.pattern=e.data.dateFormat);var o=e.data.locale||t.locale;o&&(a.locale=o),i.disabled=!e.enabled,a.date=new DayPilot.Date(e.value);var r=new DayPilot.Date(e.value).toString(e.data.dateFormat||a.pattern,a.locale);i.value=r};var i=document.createElement("input");i.name=e.field;var a=new DayPilot.DatePicker({target:i,theme:"navigator_modal",zIndex:t.zIndex+1,resetTarget:!1,targetAlignment:"left",onTimeRangeSelect:function(e){n.onInput({"immediate":!0})}});return i.picker=a,i.className=t.theme+"_input_date",i.type="text",i.onkeydown=function(e){var n=!1;switch(e.keyCode){case 13:a.visible?a.close():t.doOnKey("Enter");break;case 27:a.visible?a.close():t.doOnKey("Escape");break;case 9:a.close(),n=!0;break;default:n=!0}n||(e.preventDefault(),e.stopPropagation())},i.onfocus=function(){a.show()},i.onclick=function(){a.show()},i.oninput=function(e){n.onInput()},i.onblur=function(e){n.onBlur()},n.element=i,n.picker=a,n.canFocus=function(){return!n.element.disabled},n.focus=function(){n.element.focus()},n.save=function(){var t=a.date?a.date.toString():null,n={};return n[e.field]=t,n},n},"time":function(){return t.Hc(e)},"datetime":function(){return t.Ic(e)},"select":function(){var t=new l;t.apply=function(e){t.row=e;var n=t.element;n.value=e.value,n.disabled=!e.enabled};var n=document.createElement("select");return n.name=e.field,e.data.options&&e.data.options.forEach&&e.data.options.forEach(function(e){var t=document.createElement("option");t.innerText=e.name||e.id,t.value=e.id,t.Jc=e.id,n.appendChild(t)}),n.onchange=function(e){t.onInput({"immediate":!0})},n.onblur=function(e){t.onBlur()},t.element=n,t.canFocus=function(){return!t.element.disabled},t.focus=function(){t.element.focus()},t.save=function(){var t=null,i=n.options[n.selectedIndex];i&&"undefined"!=typeof i.Jc&&(t=i.Jc);var a={};return a[e.field]=t,a},t},"searchable":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.searchable;t.disabled=!e.enabled,t.select(e.value)};var i=new d({data:e.data.options||[],name:e.field,theme:t.theme+"_form_item_searchable",listZIndex:t.zIndex+1,onSelect:function(e){e.ui&&n.onInput({"immediate":!0})}}),a=i.create();return n.element=a,n.searchable=i,n.canFocus=function(){return!n.searchable.disabled},n.focus=function(){n.searchable.focus()},n.save=function(){var t=i.selected&&i.selected.id,n={};return n[e.field]=t,n},n},"radio":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.radio;t.checked=e.checked,t.disabled=!e.enabled};var i=document.createElement("label"),a=document.createElement("input");a.type="radio",a.name=e.field,a.Jc=e.resolved,a.onchange=function(e){var i=n.row;t.findRowsByField(i.field).forEach(function(e){t.updateState(e,{checked:!1})}),t.updateState(i,{checked:!0}),t.applyState(),n.onInput({"immediate":!0})},a.onblur=function(e){n.onBlur()},i.appendChild(a);var o=document.createTextNode(e.text);return i.append(o),n.element=i,n.radio=a,n.canFocus=function(){return!1},n.focus=function(){n.radio.focus()},n.save=function(){if(!a.checked)return{};var t=a.Jc,n={};return n[e.field]=t,n},n},"checkbox":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.checkbox;t.checked=e.checked,t.disabled=!e.enabled};var i=document.createElement("label"),a=document.createElement("input");a.type="checkbox",a.name=e.field,a.Jc=e.resolved,a.onchange=function(e){var i=n.row;t.updateState(i,{checked:this.checked}),t.applyState(),n.onInput({"immediate":!0})},a.onblur=function(e){n.onBlur()},i.appendChild(a);var o=document.createTextNode(e.text);return i.append(o),n.element=i,n.checkbox=a,n.canFocus=function(){return!1},n.focus=function(){n.checkbox.focus()},n.save=function(){var t=a.checked,n={};return n[e.field]=t,n},n},"table":function(){var n=new l;n.apply=function(e){n.row=e;var t=n.table;t.disabled=!e.enabled,t.load(e.value||[])};var i=new c({name:e.field,form:t,theme:t.theme+"_form_item_tabular",item:e.data,onInput:function(e){n.onInput()}}),a=i.create();return n.element=a,n.table=i,n.canFocus=function(){return!1},n.focus=function(){n.table.focus()},n.save=function(){var t=i.save(),n={};return n[e.field]=t,n},n}};return t.plugins&&t.plugins[e.type]?t.plugins[e.type](e):n[e.type]()},r.prototype.Hc=function(e){var t=this,n=new l;n.apply=function(e){n.row=e;var t=n.searchable;t.disabled=!e.enabled,t.select(e.value)};var i=[],a=e.data.timeInterval||15;[1,5,10,15,20,30,60].includes(a)||(a=15);for(var o=60/a,r=e.data.locale||t.locale,s=DayPilot.Locale.find(r)||DayPilot.Locale.US,c=DayPilot.Date.today(),u=0;u<24*o;u++){var h=c.addMinutes(a*u),f={};f.name=h.toString(e.data.timeFormat||s.timePattern,s),f.id=h.toString("HH:mm"),i.push(f)}var v=new d({data:i,name:e.field,theme:t.theme+"_form_item_time",listZIndex:t.zIndex+1,strategy:"startsWith",onSelect:function(e){e.ui&&n.onInput({"immediate":!0})}}),p=v.create();return n.element=p,n.searchable=v,n.canFocus=function(){return!n.searchable.disabled},n.focus=function(){n.searchable.focus()},n.save=function(){var t=v.selected&&v.selected.id,n={};return n[e.field]=t,n},n},r.prototype.Ic=function(e){var t=this,n=new l;n.apply=function(e){n.row=e;var i=n.searchable;i.disabled=!e.enabled;var a=new DayPilot.Date(e.value).toString("HH:mm");i.select(a);var o=n.dateInput,r=n.picker;e.data.dateFormat&&(r.pattern=e.data.dateFormat);var s=e.data.locale||t.locale;if(s){var l=DayPilot.Locale.find(s)||DayPilot.Locale.US;r.locale=s,r.pattern=l.datePattern}o.disabled=!e.enabled,r.date=new DayPilot.Date(e.value);var d=new DayPilot.Date(e.value).toString(e.data.dateFormat||r.pattern,r.locale);o.value=d};var i=function(){var i=document.createElement("input");i.name=e.field;var a=new DayPilot.DatePicker({target:i,theme:"navigator_modal",zIndex:t.zIndex+1,resetTarget:!1,targetAlignment:"left",onTimeRangeSelect:function(e){n.onInput({"immediate":!0})}});return i.picker=a,i.className=t.theme+"_input_date",i.type="text",i.onkeydown=function(e){var n=!1;switch(e.keyCode){case 13:a.visible?a.close():t.doOnKey("Enter");break;case 27:a.visible?a.close():t.doOnKey("Escape");break;case 9:a.close(),n=!0;break;default:n=!0}n||(e.preventDefault(),e.stopPropagation())},i.onfocus=function(){a.show()},i.onclick=function(){a.show()},i.oninput=function(e){n.onInput()},i.onblur=function(e){n.onBlur()},n.dateInput=i,n.picker=a,i}(),a=function(){var i=[],a=e.data.timeInterval||15;[1,5,10,15,20,30,60].includes(a)||(a=15);for(var o=60/a,r=e.data.locale||t.locale,s=DayPilot.Locale.find(r)||DayPilot.Locale.US,l=DayPilot.Date.today(),c=0;c<24*o;c++){var u=l.addMinutes(a*c),h={};h.name=u.toString(e.data.timeFormat||s.timePattern,s),h.id=u.toString("HH:mm"),i.push(h)}var f=new d({data:i,name:e.field,theme:t.theme+"_form_item_time",listZIndex:t.zIndex+1,strategy:"startsWith",onSelect:function(e){e.ui&&n.onInput({"immediate":!0})}});return n.searchable=f,f.create()}(),o=document.createElement("div");return o.className=t.theme+"_form_item_datetime_parent",o.appendChild(i),o.appendChild(a),n.element=o,n.canFocus=function(){return!n.searchable.disabled},n.focus=function(){n.dateInput.focus()},n.save=function(){var t=n.searchable.selected&&n.searchable.selected.id,i=n.picker.date?n.picker.date.toString():null,a=new DayPilot.Date(i).getDatePart(),o=DayPilot.Date.parse(a.toString("yyyy-dd-MM ")+t,"yyyy-dd-MM HH:mm"),r={};return r[e.field]=o,r},n},r.prototype.findRowsByField=function(e){return this.Bc.filter(function(t){return t.field===e})},r.prototype.findViewById=function(e,t){return this.f.find(function(n){return n.row.field===e&&("radio"!==n.row.type||n.row.resolved===t)})},r.prototype.firstFocusable=function(){return this.f.find(function(e){return e.canFocus&&e.canFocus()})},r.prototype.updateState=function(e,t){var n=this.Cc?this.Cc:this.Bc,i=n.indexOf(e);return this.Cc=n.map(function(n){if(n!==e)return n;if(e.propsEqual(t))return e;var i=e.clone();for(var a in t)i[a]=t[a];return i}),this.Cc[i]},r.prototype.updateInteractive=function(e){var t=this.Cc.indexOf(e);this.f[t].apply(e)},r.prototype.applyState=function(){var e=this;if(this.updateDependentState(),this.Cc){this.Cc.filter(function(t,n){return e.Bc[n]!==t}).forEach(function(t){e.updateInteractive(t)}),this.Bc=this.Cc,this.Cc=null}},r.prototype.getFieldType=function(e){return["text","date","select","searchable","radio","checkbox","table","title","image","html","textarea","scrollable","time","datetime"].indexOf(e.type)!==-1?e.type:e.type&&this.plugins&&this.plugins[e.type]?e.type:e.image?"image":e.html||e.text?"html":e.id?e.options?"searchable":e.dateFormat?"date":e.columns?"table":"text":"title"},r.prototype.serialize=function(){var e={};return this.f.forEach(function(t){var n=t.save();for(var i in n)e[i]=n[i]}),e};var s=function(){this.id=this.guid(),this.field=null,this.data=null,this.type=null,this.level=0,this.enabled=!0,this.value=null,this.text=null,this.interactive=!0,this.isValue=!1,this.checked=!1,this.resolved=null};s.prototype.clone=function(){var e=new s;for(var t in this)"id"!==t&&(e[t]=this[t]);return e},s.prototype.propsEqual=function(e){for(var t in e)if(this[t]!==e[t])return!1;return!0},s.prototype.guid=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return""+e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},s.prototype.applyValue=function(e,t){this.field===e&&(this.value=t,this.isValue&&t===this.resolved&&(this.checked=!0))};var l=function(){this.element=null,this.canFocus=function(){return!1},this.apply=function(e){},this.focus=function(){},this.save=function(){return{}}},d=function(e){this.data=[],this.name=null,this.theme="searchable_default",this.Kc=!1,this.listZIndex=1e5,this.onSelect=null,this.Lc=null,this.Mc=null,this.Nc=!1,this.Oc=null,this.Pc=null,this.q=[],this.Qc=null,e=e||{};var t=this,n={"selected":{post:function(e){"object"==typeof e&&e.id?t.Lc=e:"string"!=typeof e&&"number"!=typeof e||t.select(e)}}};Object.defineProperty(this,"selected",{get:function(){return this.Lc}}),Object.defineProperty(this,"disabled",{get:function(){return this.Kc},set:function(e){this.Kc=e,this.Oc&&(this.Oc.disabled=e,e&&this.Rc())}});for(var i in e)n[i]||(this[i]=e[i]);for(var i in e)n[i]&&n[i].post(e[i])};d.prototype.select=function(e){return this.Lc=this.data.find(function(t){return t.id===e}),this.Sc(!1),this},d.prototype.create=function(){function e(e){var n=l.strategy;"includes"!==l.strategy&&"startsWith"!==l.strategy&&(n="includes"),e=e||n||"includes",h.style.display="",h.style.top=v.offsetHeight+"px",h.style.left="0px",h.style.width=v.offsetWidth+"px",h.innerHTML="",h.addEventListener("mousedown",function(e){e.preventDefault()}),l.Mc=null,l.q=[];var a=null;l.data.forEach(function(n){var o=n.name||n.id;if("includes"===e){if(o.toLowerCase().indexOf(v.value.toLowerCase())===-1)return}else if("startsWith"===e&&0!==o.toLowerCase().indexOf(v.value.toLowerCase()))return;var r=document.createElement("div");r.className=l.theme+"_list_item",r.innerText=o,r.item=n,n===l.Lc&&(l.Mc=r),a||(a=r),r.addEventListener("mousedown",function(e){i(r),e.preventDefault()}),r.addEventListener("mousemove",function(e){l.Mc!==r&&(l.Mc=r,t({dontScroll:!0}))}),h.appendChild(r),l.q.push(r)}),l.Mc||(l.Mc=a),t()}function t(e){e=e||{};var t=!e.dontScroll;document.querySelectorAll("."+l.theme+"_list_item_highlight").forEach(function(e){e.className=e.className.replace(l.theme+"_list_item_highlight","")}),l.Mc&&(l.Mc.className+=" "+l.theme+"_list_item_highlight",t&&!n(l.Mc,h)&&l.Mc.scrollIntoView())}function n(e,t){var n=e.getBoundingClientRect(),i=t.getBoundingClientRect();return n.top>=i.top&&n.bottom<=i.bottom}function i(e){var t=e.item;l.Lc=t,l.Sc(!0),o(),r()}function a(){l.Rc()}function o(){l.s()}function r(){l.Nc=!0,v.setAttribute("readonly","readonly"),v.focus()}function s(){l.Nc=!1,v.removeAttribute("readonly"),v.select(),e("all")}var l=this,d=this,c=document.createElement("div");c.className=this.theme+"_main",c.style.position="relative";var u=document.createElement("div");u.className=this.theme+"_icon",u.style.position="absolute",u.style.right="0",u.style.top="0",u.style.bottom="0",u.style.width="20px",u.addEventListener("mousedown",function(e){e.preventDefault(),l.Nc?(l.focus(),s()):(a(),r())});var h=document.createElement("div");h.className=this.theme+"_list",h.style.display="none",h.style.position="absolute",h.style.zIndex=this.listZIndex;var f=document.createElement("input");f.type="hidden",f.name=this.name,f.searchable=d,this.Qc=f;var v=document.createElement("input");return v.type="text",v.className=this.theme+"_input",v.disabled=this.Kc,v.addEventListener("click",function(e){s()}),v.addEventListener("focus",function(t){e("all")}),v.addEventListener("input",function(t){e()}),v.addEventListener("blur",function(e){v.removeAttribute("readonly"),a()}),v.addEventListener("keydown",function(e){if(l.Nc){if("Enter"===e.key)return;if("Esc"===e.key||"Escape"===e.key)return;s()}if("ArrowDown"===e.key){var n=d.q.indexOf(d.Mc);n+1<d.q.length&&(d.Mc=d.q[n+1]),t()}else if("ArrowUp"===e.key){var n=d.q.indexOf(d.Mc);n-1>=0&&(d.Mc=d.q[n-1]),t()}else"Enter"===e.key?l.Mc?(e.stopPropagation(),i(l.Mc)):(e.stopPropagation(),a(),r()):"Esc"!==e.key&&"Escape"!==e.key||(e.stopPropagation(),a(),r())}),this.Oc=v,this.Pc=h,this.Lc||(this.Lc=this.data[0],this.Lc&&(v.value=this.Lc.name)),c.appendChild(v),c.appendChild(u),c.appendChild(f),c.appendChild(h),c},d.prototype.Rc=function(){this.s(),this.Lc?this.Oc.value=this.Lc.name:(this.Oc.value="",this.Sc(!0))},d.prototype.focus=function(){this.Nc=!0,this.Oc.setAttribute("readonly","readonly"),this.Oc.focus(),this.Rc()},d.prototype.s=function(){this.Pc.style.display="none"},d.prototype.Sc=function(e){if(this.Qc.value=this.selected?this.selected.id:null,this.Lc?this.Oc.value=this.Lc.name:this.Oc.value="","function"==typeof this.onSelect){var t={control:this,ui:e};this.onSelect(t)}};var c=function(e){this.form=null,this.item=null,this.data=null,this.name=null,this.theme="edit_table_default",this.onInput=null,this.nav={},this.Tc=null,this.Bc=[],e=e||{};for(var t in e)this[t]=e[t]};c.prototype.create=function(){var e=this,t=document.createElement("div");t.className=this.theme+"_main",t.style.position="relative";var n=document.createElement("input");n.type="hidden",n.name=e.name,n.table=this,t.appendChild(n);var i=document.createElement("div");i.className=this.theme+"_table";var a=this.Uc();i.appendChild(a);var o=e.Vc({});o.spacer=!0;var r=this.Wc(o);r.classList.add(e.theme+"_spacer"),i.appendChild(r);var s=document.createElement("div");s.className=e.theme+"_tbody",i.appendChild(s),t.appendChild(i);var l=document.createElement("div");t.appendChild(l),this.nav.body=s,this.nav.table=i,this.nav.main=t,this.nav.after=l;var d=document.createElement("div"),c=document.createElement("span");return c.className=this.theme+"_plus",c.addEventListener("click",function(t){if(!e.disabled){var n=e.item.onNewRow,i={};if("function"==typeof n){var a={};a.result=e.form.serialize(),a.value={},n(a),i=a.value}var o=e.Vc(i);e.Bc.push(o),e.Xc(),e.Yc()}}),this.nav.plus=c,d.appendChild(c),t.appendChild(d),t},c.prototype.Uc=function(){var e=this,t=document.createElement("div");return t.classList.add(this.theme+"_row"),t.classList.add(this.theme+"_header"),this.item.columns.forEach(function(n){var i=document.createElement("div");i.classList.add(e.theme+"_cell"),i.innerText=n.name,t.appendChild(i)}),t},c.prototype.Zc=function(){var e=this.item.max||0;return!!(e&&this.Bc.length>=e)},c.prototype.save=function(){var e=this,t=[];return e.Bc.forEach(function(e){var n={};e.cells.forEach(function(e){n[e.id]=e.value}),t.push(n)}),t},c.prototype.load=function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new Error("Array expected");this.data=e,this.$c(),this.Xc()},c.prototype._c=function(){this.disabled?this.nav.main.classList.add(this.theme+"_disabled"):this.nav.main.classList.remove(this.theme+"_disabled"),this.Zc()?this.nav.plus.classList.add(this.theme+"_plus_max"):this.nav.plus.classList.remove(this.theme+"_plus_max")},c.prototype.$c=function(){var e=this;this.Bc=[],this.data.forEach(function(t){var n=e.Vc(t);e.Bc.push(n)})},c.prototype.ad=function(e){var t=this,n=t.Bc.indexOf(e);t.Bc.splice(n,1)},c.prototype.Vc=function(e){var t=this,n={};return n.data=e,n.cells=[],t.item.columns.forEach(function(i){var a=i.id,o=e[a],r=t.bd(i);if("undefined"==typeof o)if("text"===r)o="";else if("number"===r)o=0;else if("select"===r){var s=i.options;o=s&&s[0].id}var l={};l.id=a,l.value=o,l.type=r,l.data=i,n.cells.push(l)}),n},c.prototype.bd=function(e){var t=e.type;return t||(t=e.options?"select":"text"),t},c.prototype.Xc=function(){var e=this;if(this.nav.body.innerHTML="",this.nav.after.innerHTML="",this.Bc.forEach(function(t){var n=e.Wc(t);e.nav.body.appendChild(n)}),0===this.Bc.length){var t=e.cd();e.nav.after.appendChild(t)}this._c()},c.prototype.cd=function(){var e=document.createElement("div");return e.className=this.theme+"_empty",e},c.prototype.Wc=function(e){var t=this,n=document.createElement("div");n.className=t.theme+"_row",e.cells.forEach(function(i){var a=document.createElement("div");a.className=t.theme+"_cell";var o=t.dd(i);if(e.spacer){var r=document.createElement("div");r.style.height="0px",r.style.overflow="hidden",r.appendChild(o),a.appendChild(r)}else a.appendChild(o);n.appendChild(a)});var i=document.createElement("div");i.classList.add(t.theme+"_cell"),i.classList.add(t.theme+"_rowaction");var a=document.createElement("span");return a.className=this.theme+"_delete",a.addEventListener("click",function(n){t.disabled||(t.ad(e),t.Xc(),t.Yc())}),e.spacer||i.appendChild(a),n.appendChild(i),n},c.prototype.Yc=function(){var e=this;if("function"==typeof e.onInput){var t={};e.onInput(t)}},c.prototype.dd=function(e){var t=this,n=e.type;if("text"===n||"number"===n){var i=document.createElement("input");return i.type=n,t.disabled&&(i.disabled=!0),e.value&&(i.value=e.value),i.addEventListener("keyup",function(i){"number"===n?e.value=Number(this.value):e.value=this.value,t.Yc()}),i}if("select"===n){var a=document.createElement("select");return t.disabled&&(a.disabled=!0),e.data.options.forEach(function(t){var n=document.createElement("option");n.innerText=t.name,n.value=t.id,n.Jc=t.id,a.appendChild(n),e.value===t.id&&n.setAttribute("selected",!0)}),a.addEventListener("change",function(n){var i=a.options[a.selectedIndex];i&&"undefined"!=typeof i.Jc&&(e.value=i.Jc),t.Yc()}),a}throw new Error("Unsupported item type: "+n)},c.prototype.focus=function(){}}}(DayPilot),"undefined"==typeof DayPilot)var DayPilot={};if("undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(){if("undefined"==typeof DayPilot.Month||!DayPilot.Month.events){var e={},t=DayPilot.Util.isVueVNode;e.Month=function(n,i){this.v="2025.3.696-lite",this.nav={};var a=this;this.id=n,this.isMonth=!0,this.api=2,this.backendUrl=null,this.cellHeaderHeight=24,this.cellHeight=100,this.cellMarginBottom=0,this.contextMenu=null,this.cssClassPrefix="month_default",this.eventBarVisible=!0,this.eventBorderRadius=null,this.eventHeight=25,this.eventsLoadMethod="GET",this.headerHeight=30,this.hideUntilInit=!0,this.lineSpace=1,this.locale="en-us",this.showToolTip=!0,this.startDate=new DayPilot.Date,this.theme=null,this.visible=!0,this.weekStarts="Auto",this.width="100%",this.xssProtection="Enabled",this.afterRender=function(){},this.cellHeaderClickHandling="Enabled",this.eventClickHandling="Enabled",this.eventDeleteHandling="Disabled",this.eventMoveHandling="Update",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.headerClickHandling="Enabled",this.timeRangeSelectedHandling="Enabled",this.onCellHeaderClick=null,this.onCellHeaderClicked=null,this.onEventClick=null,this.onEventClicked=null,this.onEventDelete=null,this.onEventDeleted=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onEventRightClick=null,this.onEventRightClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onBeforeEventRender=null,this.onBeforeCellRender=null,this.cellEvents=[],this.elements={},this.elements.events=[],this.cache={},this.Q=!1,this.W=function(e,t){var e=JSON.parse(e);return e.CallBackRedirect?void(document.location.href=e.CallBackRedirect):"None"===e.UpdateType?void a.fireAfterRenderDetached(e.CallBackData,!0):(a.events.list=e.Events,"Full"===e.UpdateType&&(a.startDate=e.StartDate,a.timeFormat=e.TimeFormat?e.TimeFormat:a.timeFormat,"undefined"!=typeof e.WeekStarts&&(a.weekStarts=e.WeekStarts),a.hashes=e.Hashes),a.Y(),a.ed(),a.ha(),"Full"===e.UpdateType&&(a.fd(),a.gd()),a.ma(),a.show(),a.na(),void a.fireAfterRenderDetached(e.CallBackData,!0))},this.fireAfterRenderDetached=function(e,t){var n=function(e,t){return function(){a.afterRender&&a.afterRender(e,t)}};window.setTimeout(n(e,t),0)},this.lineHeight=function(){return this.eventHeight+this.lineSpace},this.events={},this.events.add=function(e){var t=null;if(e instanceof DayPilot.Event)t=e.data;else{if("object"!=typeof e)throw"DayPilot.Month.events.add() expects an object or DayPilot.Event instance.";t=e}a.events.list||(a.events.list=[]),a.events.list.push(t),a.update(),a.wa.notify()},this.events.find=function(e){if(!a.events.list)return null;if("function"==typeof e){for(var t=e,n=0;n<a.events.list.length;n++){var i=new DayPilot.Event(a.events.list[n],a);if(t(i))return i}return null}for(var n=0;n<a.events.list.length;n++){var o=a.events.list[n];if(o.id===e)return new DayPilot.Event(o,a)}return null},this.events.update=function(e){if(e instanceof DayPilot.Event)e.commit();else if("object"==typeof e){var t=a.events.find(e.id);if(t){var n=DayPilot.indexOf(a.events.list,t.data);a.events.list.splice(n,1,e)}}a.update(),a.wa.notify()},this.events.remove=function(e){var t;if(e instanceof DayPilot.Event)t=e.data;else if("object"==typeof e){var n=a.events.find(e.id);n&&(t=n.data)}else if("string"==typeof e||"number"==typeof e){var n=a.events.find(e);n&&(t=n.data)}var i=DayPilot.indexOf(a.events.list,t);a.events.list.splice(i,1),a.update(),a.wa.notify()},this.events.load=function(e,t,n){var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},o=function(e){var n,o=e.request;try{n=JSON.parse(o.responseText)}catch(e){var r={};return r.exception=e,void i(r)}if(DayPilot.isArray(n)){var s={};if(s.preventDefault=function(){this.preventDefault.value=!0},s.data=n,"function"==typeof t&&t(s),s.preventDefault.value)return;a.events.list=n,a.Ma&&a.update()}};if(a.eventsLoadMethod&&"POST"===a.eventsLoadMethod.toUpperCase())DayPilot.Http.ajax({"method":"POST","data":{"start":a.visibleStart().toString(),"end":a.visibleEnd().toString()},"url":e,"success":o,"error":i});else{var r=e,s="start="+a.visibleStart().toString()+"&end="+a.visibleEnd().toString();r+=r.indexOf("?")>-1?"&"+s:"?"+s,DayPilot.Http.ajax({"method":"GET","url":r,"success":o,"error":i})}},this.events.forRange=function(e,t){e=new DayPilot.Date(e),t=new DayPilot.Date(t);var n=(a.events.list||[]).map(function(e){return new DayPilot.Event(e)});return n.sort(a.Bb),n.filter(function(n){var i=n.start(),a=n.end();return i===a&&i===e||DayPilot.Util.overlaps(e,t,i,a)})},this.update=function(e){if(a.ub(e),this.Ma){if(a.Q)throw new DayPilot.Exception("You are trying to update a DayPilot.Month instance that has been disposed.");if(this.cells){a.Y(),a.ed(),a.ha(),a.fd(),a.gd(),a.ma(),a.u(),a.na(),this.visible?this.show():this.hide()}}},this.wb=null,this.ub=function(e){if(e){var t={"events":{"preInit":function(){var e=this.data||[];DayPilot.isArray(e.list)?a.events.list=e.list:a.events.list=e}}};this.wb=t;for(var n in e)if(t[n]){var i=t[n];i.data=e[n],i.preInit&&i.preInit()}else a[n]=e[n]}},this.xb=function(){var e=this.wb;for(var t in e){var n=e[t];n.postInit&&n.postInit()}},this.zb={},this.zb.events=[],this.Ab=function(e){var t=this.zb.events,n=this.events.list[e],i={};for(var o in n)i[o]=n[o];if("function"==typeof this.onBeforeEventRender){var r={};r.control=a,r.data=i,this.onBeforeEventRender(r)}t[e]=i},this.ha=function(){var e=this.events.list;if(e){if(!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.Month.events.list expects an array object. You supplied: "+typeof e);if("function"==typeof this.onBeforeEventRender)for(var t=0;t<e.length;t++)this.Ab(t);for(var n=0;n<e.length;n++){var i=e[n];if("object"!=typeof i)throw new DayPilot.Exception("Event data item must be an object");if(!i.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(!i.end)throw new DayPilot.Exception("Event data item must specify 'end' property");var a=new DayPilot.Date(i.start),o=new DayPilot.Date(i.end);if(!(a.getTime()>o.getTime()))for(var t=0;t<this.rows.length;t++){var r=this.rows[t],s=new DayPilot.Event(i,this);r.belongsHere(s)&&(r.events.push(s),"function"==typeof this.onBeforeEventRender&&(s.cache=this.zb.events[n]))}}for(var l=0;l<this.rows.length;l++){var r=this.rows[l];r.events.sort(this.Bb);for(var d=0;d<this.rows[l].events.length;d++){var c=r.events[d],u=r.getStartColumn(c),h=r.getWidth(c);r.putIntoLine(c,u,h,l)}}}},this.Y=function(){for(var e=0;e<this.elements.events.length;e++){var t=this.elements.events[e];a.Pa(t)}this.elements.events=[]},this.Pa=function(e){!function(){var n=e.domArgs;if(e.domArgs=null,n&&"function"==typeof a.onBeforeEventDomRemove&&a.onBeforeEventDomRemove(n),n&&"function"==typeof a.onBeforeEventDomAdd){var i=n&&n.Qa;if(i){a.Sa.Ra&&t(n.element)&&(a.Sa.Ta=!0,a.Sa.Ua(i),a.Sa.Ta=!1)}}}(),e.event=null,e.click=null,e.parentNode.removeChild(e)},this.na=function(){this.hd()},this.hd=function(){this.elements.events=[];for(var e=0;e<this.rows.length;e++)for(var t=this.rows[e],n=0;n<t.lines.length;n++)for(var i=t.lines[n],a=0;a<i.length;a++)this.Va(i[a])},this.Bb=function(e,t){if(!(e&&t&&e.start&&t.start))return 0;var n=e.start().getTime()-t.start().getTime();return 0!==n?n:t.end().getTime()-e.end().getTime()},this.drawShadow=function(t,n,i,o,r,s){r||(r=0);var l=o;this.shadow={},this.shadow.list=[],this.shadow.start={x:t,y:n},this.shadow.width=o;var d=7*n+t-r;d<0&&(l+=d,t=0,n=0);for(var c=r;c>=7;)n--,c-=7;if(c>t){c>t+(7-this.getColCount())?(n--,t=t+7-c):(l=l-c+t,t=0)}else t-=c;n<0&&(n=0,t=0);var u=null;e.resizingEvent?u="w-resize":e.movingEvent&&(u="move"),this.nav.top.style.cursor=u;var h=a.eventBorderRadius;for("number"==typeof h&&(h+="px");l>0&&n<this.rows.length;){var f=Math.min(this.getColCount()-t,l),v=this.rows[n],p=this.getRowTop(n),m=v.getHeight(),y=document.createElement("div");y.setAttribute("unselectable","on"),y.style.position="absolute",y.style.left=this.getCellWidth()*t+"%",y.style.width=this.getCellWidth()*f+"%",y.style.top=p+"px",y.style.height=m+"px",y.style.cursor=u,y.classList.add(a.L("_shadow"));var g=document.createElement("div");g.setAttribute("unselectable","on"),y.appendChild(g),g.style.position="absolute",g.style.top="0px",g.style.right="0px",g.style.left="0px",g.style.bottom="0px",g.classList.add(a.L("_shadow_inner")),h&&(l===o&&(y.style.borderTopLeftRadius=h,y.style.borderBottomLeftRadius=h,g.style.borderTopLeftRadius=h,g.style.borderBottomLeftRadius=h),
l<=f&&(y.style.borderTopRightRadius=h,y.style.borderBottomRightRadius=h,g.style.borderTopRightRadius=h,g.style.borderBottomRightRadius=h)),this.nav.top.appendChild(y),this.shadow.list.push(y),l-=f+7-this.getColCount(),t=0,n++}},this.clearShadow=function(){if(this.shadow){for(var e=0;e<this.shadow.list.length;e++)this.nav.top.removeChild(this.shadow.list[e]);this.shadow=null,this.nav.top.style.cursor=""}},this.getEventTop=function(e,t){for(var n=this.headerHeight,i=0;i<e;i++)n+=this.rows[i].getHeight();return n+=this.cellHeaderHeight,n+=t*this.lineHeight()},this.getDateFromCell=function(e,t){return this.firstDate.addDays(7*t+e)},this.Va=function(n){var i=n.cache||n.data,o=i.borderRadius||a.eventBorderRadius;"number"==typeof o&&(o+="px");var r=n.part.row,s=n.part.line,l=n.part.colStart,d=n.part.colWidth,c=this.getCellWidth()*l,u=this.getCellWidth()*d,h=this.getEventTop(r,s),f=document.createElement("div");f.setAttribute("unselectable","on"),f.style.height=this.eventHeight+"px",f.style.overflow="hidden",f.className=this.L("_event"),i.cssClass&&DayPilot.Util.addClass(f,i.cssClass),n.part.startsHere||DayPilot.Util.addClass(f,this.L("_event_continueleft")),n.part.endsHere||DayPilot.Util.addClass(f,this.L("_event_continueright")),f.event=n,f.style.width=u+"%",f.style.position="absolute",f.style.left=c+"%",f.style.top=h+"px",this.showToolTip&&n.client.toolTip()&&(f.title=n.client.toolTip()),f.onclick=a.va,f.oncontextmenu=a.jd,f.onmousedown=function(t){t=t||window.event;var i=t.which||t.button;if(t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation(),1===i)if(e.movingEvent=null,"w-resize"===this.style.cursor||"e-resize"===this.style.cursor){var o={};o.start={},o.start.x=l,o.start.y=r,o.event=f.event,o.width=DayPilot.DateUtil.daysSpan(o.event.start(),o.event.end())+1,o.direction=this.style.cursor,e.resizingEvent=o}else if("move"===this.style.cursor||n.client.moveEnabled()){a.clearShadow();var c=DayPilot.mo3(a.nav.top,t);if(!c)return;var u=a.getCellBelowPoint(c.x,c.y),h=DayPilot.DateUtil.daysDiff(n.start(),a.rows[r].start),v=7*u.y+u.x-(7*r+l);h&&(v+=h);var p={};p.start={},p.start.x=l,p.start.y=r,p.start.line=s,p.offset=a.eventMoveToPosition?0:v,p.colWidth=d,p.event=f.event,p.coords=c,e.movingEvent=p}},f.onmousemove=function(t){if("undefined"!=typeof e&&!e.movingEvent&&!e.resizingEvent){var i=DayPilot.mo3(f,t);if(i){f.deleteIcon&&(f.deleteIcon.style.display="");var a=6;i.x<=a&&n.client.resizeEnabled()?n.part.startsHere?(f.style.cursor="w-resize",f.dpBorder="left"):f.style.cursor="not-allowed":f.clientWidth-i.x<=a&&n.client.resizeEnabled()?n.part.endsHere?(f.style.cursor="e-resize",f.dpBorder="right"):f.style.cursor="not-allowed":n.client.clickEnabled()?f.style.cursor="pointer":f.style.cursor="default"}}},f.onmouseleave=function(e){f.deleteIcon&&(f.deleteIcon.style.display="none"),f.style.cursor=""},f.onmouseenter=function(e){f.deleteIcon&&(f.deleteIcon.style.display="")};var v=document.createElement("div");if(v.setAttribute("unselectable","on"),v.className=this.L("_event_inner"),"darker"===i.borderColor&&i.backColor?v.style.borderColor=DayPilot.ColorUtil.darker(i.backColor,2):v.style.borderColor=i.borderColor,i.backColor&&(v.style.background=i.backColor),i.fontColor&&(v.style.color=i.fontColor),o&&(f.style.borderRadius=o,v.style.borderRadius=o),f.appendChild(v),n.client.barVisible()){var p=document.createElement("div");p.setAttribute("unselectable","on"),p.className=this.L("_event_bar"),p.style.position="absolute";var m=document.createElement("div");m.setAttribute("unselectable","on"),m.className=this.L("_event_bar_inner"),m.style.top="0%",m.style.height="100%",i.barColor&&(m.style.backgroundColor=i.barColor),p.appendChild(m),f.appendChild(p)}if(n.client.deleteEnabled()){var y=Math.floor(a.eventHeight/2-9),g=document.createElement("div");g.style.position="absolute",g.style.right="2px",g.style.top=y+"px",g.style.width="18px",g.style.height="18px",g.className=a.L("_event_delete"),g.onmousedown=function(e){e.stopPropagation()},g.onclick=function(e){e.stopPropagation();var t=this.parentNode.event;t&&a.ya(t)},g.style.display="none",f.deleteIcon=g,f.appendChild(g)}var b=i.areas?DayPilot.Areas.copy(i.areas):[];if(DayPilot.Areas.attach(f,n,{"areas":b}),"function"==typeof a.onAfterEventRender){var w={};w.e=f.event,w.div=f,a.onAfterEventRender(w)}!function(){var e={};if(e.control=a,e.e=n,e.element=null,f.domArgs=e,"function"==typeof a.onBeforeEventDomAdd&&a.onBeforeEventDomAdd(e),e.element){var i=v;if(i){e.Qa=i;if(t(e.element)){if(!a.Sa.Ra)throw new DayPilot.Exception("Can't reach Vue");a.Sa.Ta=!0,a.Sa.Wa(e.element,i,{"style":{"flexGrow":1}}),a.Sa.Ta=!1}else i.appendChild(e.element)}}else v.innerHTML=n.client.innerHTML()}(),this.elements.events.push(f),this.nav.events.appendChild(f)},this.lastVisibleDayOfMonth=function(){return this.startDate.lastDayOfMonth()},this.ed=function(){"string"==typeof this.startDate&&(this.startDate=new DayPilot.Date(this.startDate)),this.startDate=this.startDate.firstDayOfMonth(),this.firstDate=this.startDate.firstDayOfWeek(this.getWeekStart());var e,t=(this.startDate,this.lastVisibleDayOfMonth()),n=DayPilot.DateUtil.daysDiff(this.firstDate,t)+1;e=Math.ceil(n/7),this.days=7*e,this.rows=[];for(var i=0;i<e;i++){var o={};o.start=this.firstDate.addDays(7*i),o.end=o.start.addDays(this.getColCount()),o.events=[],o.lines=[],o.index=i,o.minHeight=this.cellHeight,o.calendar=this,o.belongsHere=function(e){return e.end().getTime()===e.start().getTime()&&e.start().getTime()===this.start.getTime()||!(e.end().getTime()<=this.start.getTime()||e.start().getTime()>=this.end.getTime())},o.getPartStart=function(e){return DayPilot.DateUtil.max(this.start,e.start())},o.getPartEnd=function(e){return DayPilot.DateUtil.min(this.end,e.end())},o.getStartColumn=function(e){var t=this.getPartStart(e);return DayPilot.DateUtil.daysDiff(this.start,t)},o.getWidth=function(e){return DayPilot.DateUtil.daysSpan(this.getPartStart(e),this.getPartEnd(e))+1},o.putIntoLine=function(e,t,n,i){for(var a=this,o=0;o<this.lines.length;o++){var r=this.lines[o];if(r.isFree(t,n))return r.addEvent(e,t,n,i,o),o}var r=[];return r.isFree=function(e,t){for(var n=!0,i=0;i<this.length;i++)e+t-1<this[i].part.colStart||e>this[i].part.colStart+this[i].part.colWidth-1||(n=!1);return n},r.addEvent=function(e,t,n,i,o){e.part.colStart=t,e.part.colWidth=n,e.part.row=i,e.part.line=o,e.part.startsHere=a.start.getTime()<=e.start().getTime(),e.part.endsHere=a.end.getTime()>=e.end().getTime(),this.push(e)},r.addEvent(e,t,n,i,this.lines.length),this.lines.push(r),this.lines.length-1},o.getStart=function(){for(var e=0,t=0;t<a.rows.length&&t<this.index;t++)e+=a.rows[t].getHeight()},o.getHeight=function(){return Math.max(this.lines.length*a.lineHeight()+a.cellHeaderHeight+a.cellMarginBottom,this.calendar.cellHeight)},this.rows.push(o)}this.endDate=this.firstDate.addDays(7*e)},this.visibleStart=function(){return a.firstDate},this.visibleEnd=function(){return a.endDate},this.getHeight=function(){for(var e=this.headerHeight,t=0;t<this.rows.length;t++)e+=this.rows[t].getHeight();return e},this.getWidth=function(e,t){return 7*t.y+t.x-(7*e.y+e.x)+1},this.getMinCoords=function(e,t){return 7*e.y+e.x<7*t.y+t.x?e:t},this.L=function(e){var t=this.theme||this.cssClassPrefix;return t?t+e:""},this.Ya=function(){var t=this.nav.top;t.setAttribute("unselectable","on"),t.style.MozUserSelect="none",t.style.KhtmlUserSelect="none",t.style.WebkitUserSelect="none",t.style.position="relative",this.width&&(t.style.width=this.width),t.style.height=this.getHeight()+"px",t.onselectstart=function(e){return!1},this.hideUntilInit&&(t.style.visibility="hidden"),this.visible||(t.style.display="none"),t.className=this.L("_main");var n=document.createElement("div");this.nav.cells=n,n.style.position="absolute",n.style.left="0px",n.style.right="0px",n.setAttribute("unselectable","on"),t.appendChild(n);var i=document.createElement("div");this.nav.events=i,i.style.position="absolute",i.style.left="0px",i.style.right="0px",i.setAttribute("unselectable","on"),t.appendChild(i),t.onmousemove=function(t){if(e.resizingEvent){var n=DayPilot.mo3(a.nav.top,t);if(!n)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var o,r,s=e.resizingEvent;s.start;if("w-resize"===s.direction){r=i;var l=s.event.end();l.getDatePart()===l&&(l=l.addDays(-1));var d=a.getCellFromDate(l);o=a.getWidth(i,d)}else r=a.getCellFromDate(s.event.start()),o=a.getWidth(r,i);o<1&&(o=1),a.drawShadow(r.x,r.y,0,o)}else if(e.movingEvent){var n=DayPilot.mo3(a.nav.top,t);if(!n)return;if(n.x===e.movingEvent.coords.x&&n.y===e.movingEvent.coords.y)return;var c=3,u=Math.abs(n.x-e.movingEvent.coords.x)+Math.abs(n.y-e.movingEvent.coords.y);if(u<=c)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var h=e.movingEvent.event,f=e.movingEvent.offset,o=a.cellMode?1:DayPilot.DateUtil.daysSpan(h.start(),h.end())+1;o<1&&(o=1),a.drawShadow(i.x,i.y,0,o,f,h)}else if(e.timeRangeSelecting){var n=DayPilot.mo3(a.nav.top,t);if(!n)return;var i=a.getCellBelowPoint(n.x,n.y);a.clearShadow();var r=e.timeRangeSelecting,v=7*r.y+r.x,p=7*i.y+i.x,o=Math.abs(p-v)+1;o<1&&(o=1);var m=v<p?r:i;e.timeRangeSelecting.from={x:m.x,y:m.y},e.timeRangeSelecting.width=o,e.timeRangeSelecting.moved=!0,a.drawShadow(m.x,m.y,0,o,0,null)}}},this.ma=function(){this.nav.top.style.height=this.getHeight()+"px";for(var e=0;e<this.cells.length;e++)for(var t=0;t<this.cells[e].length;t++)this.cells[e][t].style.top=this.getRowTop(t)+"px",this.cells[e][t].style.height=this.rows[t].getHeight()+"px"},this.getCellBelowPoint=function(e,t){for(var n=Math.floor(this.nav.top.clientWidth/this.getColCount()),i=Math.min(Math.floor(e/n),this.getColCount()-1),a=null,o=this.headerHeight,r=0,s=0;s<this.rows.length;s++){var l=o;if(o+=this.rows[s].getHeight(),t<o){r=t-l,a=s;break}}null===a&&(a=this.rows.length-1);var d={};return d.x=i,d.y=a,d.relativeY=r,d},this.getCellFromDate=function(e){for(var t=DayPilot.DateUtil.daysDiff(this.firstDate,e),n={x:0,y:0};t>=7;)n.y++,t-=7;return n.x=t,n},this.gd=function(){var e=document.createElement("div");e.oncontextmenu=function(){return!1},this.nav.cells.appendChild(e),this.cells=[];for(var t=0;t<this.getColCount();t++){this.cells[t]=[];var n=document.createElement("div");n.setAttribute("unselectable","on"),n.style.position="absolute",n.style.left=this.getCellWidth()*t+"%",n.style.width=this.getCellWidth()+"%",n.style.top="0px",n.style.height=this.headerHeight+"px";var i=t+this.getWeekStart();i>6&&(i-=7),n.className=this.L("_header");var a=document.createElement("div");a.setAttribute("unselectable","on"),a.innerHTML=o.locale().dayNames[i],n.appendChild(a),a.style.position="absolute",a.style.top="0px",a.style.bottom="0px",a.style.left="0px",a.style.right="0px",a.className=this.L("_header_inner"),a.innerHTML=o.locale().dayNames[i],e.appendChild(n);for(var r=0;r<this.rows.length;r++)this.kd(t,r,e)}},this.fd=function(){for(var e=0;e<this.cells.length;e++)for(var t=0;t<this.cells[e].length;t++){var n=a.cells[e][t];a.hb(n)}this.nav.cells.innerHTML=""},this.hb=function(e){!function(){var n=e.domArgs;if(e.domArgs=null,n&&"function"==typeof a.onBeforeCellDomRemove&&a.onBeforeCellDomRemove(n),n&&"function"==typeof a.onBeforeCellDomAdd){var i=n&&n.Qa;if(i){var o=a.ld.reactDOM&&isReactCmp(n.element),r=a.Sa.Ra&&(isVueCmp(n.element)||t(n.element));o?a.ld.md(i):r&&(a.Sa.Ta=!0,a.Sa.Ua(i),a.Sa.Ta=!1)}}}(),e.onclick=null},this.ta=function(){return 2===a.api},this.kd=function(n,i,r){var s=this.rows[i],l=this.firstDate.addDays(7*i+n),d=l.getDay(),c=null;c=1===d?o.locale().monthNames[l.getMonth()]+" "+d:""+d;var u=!a.isWeekend(l),h={"start":l,"end":l.addDays(1),"properties":{"headerHtml":c,"backColor":null,"business":u,"html":null}},f={};f.control=a,f.cell=h,"function"==typeof a.onBeforeCellRender&&a.onBeforeCellRender(f);var v=f.cell.properties,p=document.createElement("div");if(p.setAttribute("unselectable","on"),p.style.position="absolute",p.style.cursor="default",p.style.left=this.getCellWidth()*n+"%",p.style.width=this.getCellWidth()+"%",p.style.top=this.getRowTop(i)+"px",p.style.height=s.getHeight()+"px",p.className=this.L("_cell"),v.business){var u=this.L("_cell_business");DayPilot.Util.addClass(p,u)}var m=(this.startDate.addMonths(-1).getMonth(),this.startDate.addMonths(1).getMonth(),this.startDate.getMonth(),document.createElement("div"));m.setAttribute("unselectable","on"),p.appendChild(m),m.style.position="absolute",m.style.left="0px",m.style.right="0px",m.style.top="0px",m.style.bottom="0px",m.className=this.L("_cell_inner"),v.backColor&&(m.style.backgroundColor=f.cell.properties.backColor),p.onmousedown=function(t){"Disabled"!==a.timeRangeSelectedHandling&&(a.clearShadow(),e.timeRangeSelecting={"root":a,"x":n,"y":i,"from":{x:n,y:i},"width":1})},p.onclick=function(){var e=function(e){var t=new DayPilot.Date(e),n=t.addDays(1);a.O(t,n)};if("Disabled"!==a.timeRangeSelectedHandling)return void e(l)};var y=document.createElement("div");if(y.setAttribute("unselectable","on"),y.style.height=this.cellHeaderHeight+"px",y.className=this.L("_cell_header"),y.onclick=function(e){if("Enabled"===a.cellHeaderClickHandling){e.stopPropagation();var t={};t.control=a,t.start=l,t.end=l.addDays(1),t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onCellHeaderClick&&(a.onCellHeaderClick(t),t.preventDefault.value)||"function"==typeof a.onCellHeaderClicked&&a.onCellHeaderClicked(t)}},y.innerHTML=v.headerHtml,m.appendChild(y),v.html){var g=document.createElement("div");g.style.height=s.getHeight()-this.cellHeaderHeight+"px",g.style.overflow="hidden",g.innerHTML=v.html,m.appendChild(g)}!function(){if("function"==typeof a.onBeforeCellDomAdd||"function"==typeof a.onBeforeCellDomRemove){var e={};if(e.control=a,e.cell=h,e.element=null,p.domArgs=e,"function"==typeof a.onBeforeCellDomAdd&&a.onBeforeCellDomAdd(e),e.element){var n=m;if(n){e.Qa=n;if(t(e.element)){if(!a.Sa.Ra)throw new DayPilot.Exception("Can't reach Vue");a.Sa.Ta=!0,a.Sa.Wa(e.element,n),a.Sa.Ta=!1}else n.appendChild(e.element)}}}}(),this.cells[n][i]=p,r.appendChild(p)},this.getWeekStart=function(){if("Auto"===a.weekStarts){var e=o.locale();return e?e.weekStarts:0}return a.weekStarts||0},this.getColCount=function(){return 7},this.getCellWidth=function(){return 14.285},this.getRowTop=function(e){for(var t=this.headerHeight,n=0;n<e;n++)t+=this.rows[n].getHeight();return t},this.U=function(e,t,n){var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.T();var a="JSON"+DayPilot.JSON.stringify(i);this.backendUrl&&DayPilot.request(this.backendUrl,this.V,a,this.ajaxError)},this.V=function(e){a.W(e.responseText)},this.T=function(){var e={};return e.control="dpm",e.id=this.id,e.v=this.v,e.visibleStart=new DayPilot.Date(this.firstDate),e.visibleEnd=e.visibleStart.addDays(this.days),e.startDate=a.startDate,e.timeFormat=this.timeFormat,e.weekStarts=this.weekStarts,e},this.eventClickCallBack=function(e,t){this.U("EventClick",t,e)},this.va=function(t){e.movingEvent=null,e.resizingEvent=null;var n=this,t=t||window.event;t.ctrlKey;t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation(),a.eventClickSingle(n,t)},this.eventClickSingle=function(e,t){var n=e.event;if(n&&n.client.clickEnabled())if(a.ta()){var i={};if(i.e=n,i.control=a,i.div=e,i.originalEvent=t,i.meta=t.metaKey,i.ctrl=t.ctrlKey,i.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventClick&&(a.wa.apply(function(){a.onEventClick(i)}),i.preventDefault.value))return;switch(a.eventClickHandling){case"CallBack":a.eventClickCallBack(n);break;case"ContextMenu":var o=n.client.contextMenu();o?o.show(n):a.contextMenu&&a.contextMenu.show(n)}"function"==typeof a.onEventClicked&&a.wa.apply(function(){a.onEventClicked(i)})}else switch(a.eventClickHandling){case"CallBack":a.eventClickCallBack(n);break;case"JavaScript":a.onEventClick(n)}},this.jd=function(){var e=this;return a.xa(e.event),!1},this.xa=function(e){if(this.event=e,!e.client.rightClickEnabled())return!1;var t={};if(t.e=e,t.preventDefault=function(){this.preventDefault.value=!0},"function"!=typeof a.onEventRightClick||(a.onEventRightClick(t),!t.preventDefault.value)){switch(a.eventRightClickHandling){case"ContextMenu":var n=e.client.contextMenu();n?n.show(e):a.contextMenu&&a.contextMenu.show(this.event)}return"function"==typeof a.onEventRightClicked&&a.onEventRightClicked(t),!1}},this.ya=function(e){if(a.ta()){var t={};if(t.e=e,t.control=a,t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventDelete&&(a.wa.apply(function(){a.onEventDelete(t)}),t.preventDefault.value))return;switch(a.eventDeleteHandling){case"CallBack":a.eventDeleteCallBack(e);break;case"PostBack":a.eventDeletePostBack(e);break;case"Update":a.events.remove(e)}"function"==typeof a.onEventDeleted&&a.wa.apply(function(){a.onEventDeleted(t)})}else switch(a.eventDeleteHandling){case"PostBack":a.eventDeletePostBack(e);break;case"CallBack":a.eventDeleteCallBack(e);break;case"JavaScript":a.onEventDelete(e)}},this.eventDeleteCallBack=function(e,t){this.U("EventDelete",t,e)},this.eventDeletePostBack=function(e,t){this.S("EventDelete",t,e)},this.eventMoveCallBack=function(e,t,n,i,a){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var o={};o.e=e,o.newStart=t,o.newEnd=n,o.position=a,this.U("EventMove",i,o)},this.M=function(e,t,n,i,o,r){var s=e.start().getTimePart(),l=e.end().getDatePart();l.getTime()!==e.end().getTime()&&(l=l.addDays(1));var d=DayPilot.DateUtil.diff(e.end(),l),c=this.getDateFromCell(t,n);c=c.addDays(-i);var u=DayPilot.DateUtil.daysSpan(e.start(),e.end())+1,h=c.addDays(u),f=c.addTime(s),v=h.addTime(d);if(a.ta()){var p={};if(p.e=e,p.control=a,p.newStart=f,p.newEnd=v,p.ctrl=o.ctrlKey,p.shift=o.shiftKey,p.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventMove&&(a.wa.apply(function(){a.onEventMove(p)}),p.preventDefault.value))return;switch(a.eventMoveHandling){case"CallBack":a.eventMoveCallBack(e,f,v);break;case"Update":e.start(f),e.end(v),a.events.update(e)}"function"==typeof a.onEventMoved&&a.wa.apply(function(){a.onEventMoved(p)})}else switch(a.eventMoveHandling){case"CallBack":a.eventMoveCallBack(e,f,v);break;case"JavaScript":a.onEventMove(e,f,v)}},this.eventResizeCallBack=function(e,t,n,i){if(!t)throw"newStart is null";if(!n)throw"newEnd is null";var a={};a.e=e,a.newStart=t,a.newEnd=n,this.U("EventResize",i,a)},this.K=function(e,t,n){var i=e.start().getTimePart(),o=e.end().getDatePart();o.getTime()!==e.end().getTime()&&(o=o.addDays(1));var r=DayPilot.DateUtil.diff(e.end(),o),s=this.getDateFromCell(t.x,t.y),l=s.addDays(n),d=s.addTime(i),c=l.addTime(r);if(a.ta()){var u={};if(u.e=e,u.control=a,u.newStart=d,u.newEnd=c,u.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onEventResize&&(a.wa.apply(function(){a.onEventResize(u)}),u.preventDefault.value))return;switch(a.eventResizeHandling){case"CallBack":a.eventResizeCallBack(e,d,c);break;case"Update":e.start(d),e.end(c),a.events.update(e)}"function"==typeof a.onEventResized&&a.wa.apply(function(){a.onEventResized(u)})}else switch(a.eventResizeHandling){case"CallBack":a.eventResizeCallBack(e,d,c);break;case"JavaScript":a.onEventResize(e,d,c)}},this.timeRangeSelectedCallBack=function(e,t,n){var i={};i.start=e,i.end=t,this.U("TimeRangeSelected",n,i)},this.O=function(e,t){if(this.ta()){var n={};if(n.control=a,n.start=e,n.end=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof a.onTimeRangeSelect&&(a.wa.apply(function(){a.onTimeRangeSelect(n)}),n.preventDefault.value))return;switch(a.timeRangeSelectedHandling){case"CallBack":a.timeRangeSelectedCallBack(e,t)}"function"==typeof a.onTimeRangeSelected&&a.wa.apply(function(){a.onTimeRangeSelected(n)})}else switch(a.timeRangeSelectedHandling){case"CallBack":a.timeRangeSelectedCallBack(e,t);break;case"JavaScript":a.onTimeRangeSelected(e,t)}},this.wa={},this.wa.scope=null,this.wa.notify=function(){a.wa.scope&&a.wa.scope["$apply"]()},this.wa.apply=function(e){e()},this.clearSelection=function(){a.clearShadow()},this.commandCallBack=function(e,t){var n={};n.command=e,this.U("Command",t,n)},this.isWeekend=function(e){return e=new DayPilot.Date(e),0===e.dayOfWeek()||6===e.dayOfWeek()},this.ca={},this.ca.locale=function(){var e=DayPilot.Locale.find(a.locale);return e?e:DayPilot.Locale.US},this.ca.da=function(){return"Disabled"!==a.xssProtection};var o=this.ca;this.debug=function(e,t){this.debuggingEnabled&&(a.debugMessages||(a.debugMessages=[]),a.debugMessages.push(e),"undefined"!=typeof console&&console.log(e))},this.dispose=function(){var e=a;e.Q||(e.Q=!0,e.fd(),e.Y(),e.nav.top.removeAttribute("style"),e.nav.top.removeAttribute("class"),e.nav.top.innerHTML="",e.nav.top.dp=null,e.nav.top.onmousemove=null,e.nav.top=null)},this.disposed=function(){return this.Q},this.rb=function(){e.globalHandlers||(e.globalHandlers=!0,DayPilot.re(document,"mouseup",e.gMouseUp))},this.loadFromServer=function(){return!(!this.backendUrl&&"function"!=typeof WebForm_DoCallback)&&("undefined"==typeof a.events.list||!a.events.list)},this.u=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.show=function(){a.visible=!0,a.nav.top.style.display=""},this.hide=function(){a.visible=!1,a.nav.top.style.display="none"},this.yb=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Month() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Month: The placeholder element not found: '"+id+"'."}},this.Hb=function(){this.ed(),this.Ya(),this.gd(),this.rb(),this.U("Init")},this.ab=function(e,t){return a.ca.da()?DayPilot.Util.escapeTextHtml(e,t):DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":e:t},this.Sa={},this.Sa.Ra=null,this.Sa.Ob=function(e,t,n){var i=a.Sa.Ra;if("function"==typeof i.createVNode&&"function"==typeof i.render){var o=i.createVNode(e,n);i.render(o,t)}},this.Sa.Wa=function(e,t,n){var i=a.Sa.Ra;if("function"==typeof i.render){var o=e;DayPilot.isArray(e)&&(o=i.h("div",n,e)),i.render(o,t)}},this.Sa.Ua=function(e){var t=a.Sa.Ra;"function"==typeof t.render&&t.render(null,e)},this.internal={},this.internal.loadOptions=this.ub,this.internal.xssTextHtml=a.ab,this.internal.enableVue=function(e){a.Sa.Ra=e},this.internal.vueRef=function(){return a.Sa.Ra},this.internal.vueRendering=function(){return a.Sa.Ta},this.init=function(){return this.yb(),this.loadFromServer()?void this.Hb():(this.ed(),this.ha(),this.Ya(),this.gd(),this.u(),this.na(),this.rb(),this.fireAfterRenderDetached(null,!1),this.Ma=!0,this)},this.Init=this.init,Object.defineProperty(this,"durationBarVisible",{get:function(){return a.eventBarVisible}}),this.ub(i)},e.gMouseUp=function(t){if(e.movingEvent){var n=e.movingEvent;if(!n.event)return;if(!n.event.calendar)return;if(!n.event.calendar.shadow)return;if(!n.event.calendar.shadow.start)return;var i=e.movingEvent.event.calendar,a=e.movingEvent.event,o=i.shadow.start,r=i.shadow.position,s=e.movingEvent.offset;i.clearShadow(),e.movingEvent=null;var t=t||window.event;return i.M(a,o.x,o.y,s,t,r),t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation(),e.movingEvent=null,!1}if(e.resizingEvent){var n=e.resizingEvent;if(!n.event)return;if(!n.event.calendar)return;if(!n.event.calendar.shadow)return;if(!n.event.calendar.shadow.start)return;var i=e.resizingEvent.event.calendar,a=e.resizingEvent.event,o=i.shadow.start,l=i.shadow.width;return i.clearShadow(),e.resizingEvent=null,i.K(a,o,l),t.cancelBubble=!0,e.resizingEvent=null,!1}if(e.timeRangeSelecting){if(e.timeRangeSelecting.moved){var d=e.timeRangeSelecting,i=d.root,o=new DayPilot.Date(i.getDateFromCell(d.from.x,d.from.y)),c=o.addDays(d.width);i.O(o,c),i.clearShadow()}e.timeRangeSelecting=null}},DayPilot.Month=e.Month,"undefined"!=typeof jQuery&&!function(e){e.fn.daypilotMonth=function(e){var t=null,n=this.each(function(){if(!this.daypilot){var n=new DayPilot.Month(this.id);this.daypilot=n;for(name in e)n[name]=e[name];n.Init(),t||(t=n)}});return 1===this.length?t:n}}(jQuery),function(){var e=DayPilot.am();e&&e.directive("daypilotMonth",["$parse",function(e){return{"restrict":"E","template":"<div></div>","replace":!0,"link":function(t,n,i){var a=new DayPilot.Month(n[0]);a.wa.scope=t,a.init();var o=i["id"];o&&(t[o]=a);var r=i["publishAs"];if(r){(0,e(r).assign)(t,a)}for(var s in i)0===s.indexOf("on")&&!function(n){a[n]=function(a){var o=e(i[n]);t["$apply"](function(){o(t,{"args":a})})}}(s);var l=t["$watch"],d=i["config"]||i["daypilotConfig"],c=i["events"]||i["daypilotEvents"];l.call(t,d,function(e){for(var t in e)a[t]=e[t];a.update()},!0),l.call(t,c,function(e){a.events.list=e,a.update()},!0)}}}])}(),"undefined"!=typeof Sys&&Sys.Application&&Sys.Application.notifyScriptLoaded&&Sys.Application.notifyScriptLoaded()}}(),"undefined"==typeof DayPilot)var DayPilot={};if("undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";if("undefined"==typeof DayPilot.Navigator||!DayPilot.Navigator.def){var e=DayPilot.Util.isVueVNode;DayPilot.Navigator=function(t,n){this.v="2025.3.696-lite";var i=this;this.id=t,this.api=2,this.isNavigator=!0,this.autoFocusOnClick=!0,this.weekStarts="Auto",this.selectMode="Day",this.titleHeight=30,this.dayHeaderHeight=30,this.bound=null,this.cellWidth=30,this.cellHeight=30,this.cssClassPrefix="navigator_default",this.freeHandSelectionEnabled=!1,this.selectionStart=(new DayPilot.Date).getDatePart(),this.selectionEnd=null,this.selectionDay=null,this.showMonths=1,this.skipMonths=1,this.command="navigate",this.year=(new DayPilot.Date).getYear(),this.month=(new DayPilot.Date).getMonth()+1,this.showToday=!1,this.showWeekNumbers=!1,this.todayHtml=null,this.todayHeight=40,this.todayPosition="Bottom",this.todayText="Today",this.weekNumberAlgorithm="Auto",this.rowsPerMonth="Six",this.orientation="Vertical",this.locale="en-us",this.rtl=!1,this.visible=!0,this.timeRangeSelectedHandling="Bind",this.visibleRangeChangedHandling="Enabled",this.onVisibleRangeChange=null,this.onVisibleRangeChanged=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onTodayClick=null,this.nav={},this.zb={},this.nd=function(){this.root.dp=this,this.root.className=this.L("_main"),"Horizontal"===this.orientation?(r.od()||(this.root.style.width=this.showMonths*(7*r.cellWidth()+this.pd())+"px"),this.root.style.height=6*this.cellHeight+this.titleHeight+this.dayHeaderHeight+"px"):r.od()||(this.root.style.width=7*r.cellWidth()+this.pd()+"px"),this.rtl&&(this.root.style.direction="rtl"),this.root.style.position="relative",this.visible||(this.root.style.display="none");var e=document.createElement("input");e.type="hidden",e.name=i.id+"_state",e.id=e.name,this.root.appendChild(e),this.state=e,this.startDate?this.startDate=new DayPilot.Date(this.startDate).firstDayOfMonth():this.selectionDay?this.startDate=new DayPilot.Date(this.selectionDay).firstDayOfMonth():this.startDate=DayPilot.Date.fromYearMonthDay(this.year,this.month),this.calendars=[],this.selected=[],this.months=[]},this.ta=function(){return 2===i.api},this.fd=function(){this.root.innerHTML=""},this.L=function(e){var t=this.theme||this.cssClassPrefix;return t?t+e:""},this.qd=function(e,t){var n=this.L("_"+t);DayPilot.Util.addClass(e,n)},this.rd=function(e,t){var n=this.L("_"+t);DayPilot.Util.removeClass(e,n)},this.sd=function(){if(!r.od())return null;var e=7;return this.showWeekNumbers&&e++,100/e},this.td=function(){return r.od()?null:r.cellWidth()},this.ud=function(e){return"number"!=typeof e&&(e=1),r.od()?this.sd()*e:this.td()*e},this.vd=function(e){var t=r.od()?"%":"px";return this.ud(e)+t},this.gd=function(t,n){var a={};a.cells=[],a.days=[],a.weeks=[];var o=this.startDate.addMonths(t),s=n.before,l=n.after,d=o.firstDayOfMonth(),c=d.firstDayOfWeek(r.weekStarts()),u=d.addMonths(1),h=DayPilot.DateUtil.daysDiff(c,u),f="Auto"===this.rowsPerMonth?Math.ceil(h/7):6;a.rowCount=f;var v=(new DayPilot.Date).getDatePart(),p=7*r.cellWidth()+this.pd();a.width=p;var m=this.cellHeight*f+this.titleHeight+this.dayHeaderHeight;a.height=m;var y=document.createElement("div");if(r.od()?"Horizontal"===this.orientation&&(y.style.width=100/i.showMonths+"%"):y.style.width=p+"px",y.style.height=m+"px","Horizontal"===this.orientation)y.style.position="absolute",r.od()?(y.style.left=100/i.showMonths*t+"%",a.leftPct=100/i.showMonths*t):y.style.left=p*t+"px",y.style.top="0px",a.top=0,a.left=p*t;else{y.style.position="relative";var g=t>0?i.months[t-1].top+i.months[t-1].height:0;a.top=g,a.left=0,a.leftPct=0}y.className=this.L("_month"),y.style.cursor="default",y.style.userSelect="none",y.style.webkitUserSelect="none",y.month=a,a.div=y,this.root.appendChild(y);var b=this.titleHeight+this.dayHeaderHeight,w=document.createElement("div");w.style.position="absolute",w.style.left="0px",w.style.right="0px",w.style.top="0px",w.style.width=i.vd(),w.style.height=this.titleHeight+"px",w.style.lineHeight=this.titleHeight+"px",w.className=this.L("_titleleft"),n.left&&(w.style.cursor="pointer",w.innerHTML="<span>&lt;</span>",w.onclick=this.wd),y.appendChild(w),this.tl=w;var D=document.createElement("div");D.style.position="absolute",D.style.left=i.vd(),D.style.top="0px",D.style.width=i.vd(i.showWeekNumbers?6:5),D.style.height=this.titleHeight+"px",D.style.lineHeight=this.titleHeight+"px",D.className=this.L("_title"),D.innerHTML=r.locale().monthNames[o.getMonth()]+" "+o.getYear(),y.appendChild(D),this.ti=D;var x=document.createElement("div");x.style.position="absolute",x.style.left=i.vd(i.showWeekNumbers?7:6),x.style.right=i.vd(i.showWeekNumbers?7:6),x.style.top="0px",x.style.width=i.vd(),x.style.height=this.titleHeight+"px",x.style.lineHeight=this.titleHeight+"px",x.className=this.L("_titleright"),n.right&&(x.style.cursor="pointer",x.innerHTML="<span>&gt;</span>",x.onclick=this.xd),y.appendChild(x),this.tr=x;var _=(this.pd(),i.showWeekNumbers?1:0);if(this.showWeekNumbers)for(var C=0;C<f;C++){var k=c.addDays(7*C),S=null;switch(this.weekNumberAlgorithm){case"Auto":S=1===r.weekStarts()?k.weekNumberISO():k.weekNumber();break;case"US":S=k.weekNumber();break;case"ISO8601":S=k.weekNumberISO();break;default:throw"Unknown weekNumberAlgorithm value."}var P=document.createElement("div");P.style.position="absolute",P.style.left="0px",P.style.right="0px",P.style.top=C*this.cellHeight+b+"px",P.style.width=i.vd(),P.style.height=this.cellHeight+"px",P.style.lineHeight=this.cellHeight+"px",P.className=this.L("_weeknumber"),P.innerHTML="<span>"+S+"</span>",y.appendChild(P),a.weeks.push(P)}if(i.showWeekNumbers){var P=document.createElement("div");P.style.position="absolute",P.style.left="0px",P.style.right="0px",P.style.top=this.titleHeight+"px",P.style.width=i.vd(),P.style.height=this.dayHeaderHeight+"px",P.className=this.L("_dayheader"),y.appendChild(P)}for(var M=0;M<7;M++){a.cells[M]=[];var P=document.createElement("div");P.style.position="absolute",P.style.left=i.vd(M+_),P.style.right=i.vd(M+_),P.style.top=this.titleHeight+"px",P.style.width=i.vd(),P.style.height=this.dayHeaderHeight+"px",P.style.lineHeight=this.dayHeaderHeight+"px",P.className=this.L("_dayheader"),P.innerHTML="<span>"+this.yd(M)+"</span>",y.appendChild(P),a.days.push(P);for(var C=0;C<f;C++){var k=c.addDays(7*C+M),T=this.zd(k)&&"none"!==this.Ad(),A=k.firstDayOfMonth()===o,E=k<o,H=k>=o.addMonths(1);if("month"===this.Ad())T=T&&A;else if("day"===this.Ad())T=T&&(A||s&&E||l&&H);else if("week"===this.Ad()){var N=k.firstDayOfMonth()===o;T=T&&(N||s&&E||l&&H)}var I=document.createElement("div");a.cells[M][C]=I;var R=i.Bd(M,C),L=R.x,B=R.y,z=r.od()?"%":"px";I.day=k,I.x=M,I.y=C,I.left=L,I.top=B,I.isCurrentMonth=A,I.isNextMonth=H,I.isPrevMonth=E,I.showBefore=s,I.showAfter=l,I.className=this.L(A?"_day":"_dayother"),i.qd(I,"cell"),k.getTime()===v.getTime()&&A&&this.qd(I,"today"),0!==k.dayOfWeek()&&6!==k.dayOfWeek()||this.qd(I,"weekend"),I.style.position="absolute",I.style.left=L+z,I.style.right=L+z,I.style.top=B+"px",I.style.width=i.vd(),I.style.height=this.cellHeight+"px",I.style.lineHeight=this.cellHeight+"px";
var U=document.createElement("div");U.style.position="absolute",U.className=k.getTime()===v.getTime()&&A?this.L("_todaybox"):this.L("_daybox"),i.qd(U,"cell_box"),U.style.left="0px",U.style.top="0px",U.style.right="0px",U.style.bottom="0px",I.appendChild(U);var O=null;this.cells&&this.cells[k.toStringSortable()]&&(O=this.cells[k.toStringSortable()]);var j=O||{};if(j.day=k,j.isCurrentMonth=A,j.isToday=k.getTime()===v.getTime()&&A,j.isWeekend=0===k.dayOfWeek()||6===k.dayOfWeek(),O?(j.html=O.html||k.getDay(),j.cssClass=O.css):(j.html=k.getDay(),j.cssClass=null),"function"==typeof i.onBeforeCellRender){var W={};W.cell=j,i.onBeforeCellRender(W),O=W.cell}if(O&&DayPilot.Util.addClass(I,O.cssClass||O.css),A||s&&E||l&&H){var F=document.createElement("div");F.innerHTML=k.getDay(),F.style.position="absolute",F.style.left="0px",F.style.top="0px",F.style.right="0px",F.style.bottom="0px",i.qd(F,"cell_text"),I.isClickable=!0,O&&O.html&&(F.innerHTML=O.html),I.appendChild(F)}I.onclick=this.Cd,function(){if("function"==typeof i.onBeforeCellDomAdd||"function"==typeof i.onBeforeCellDomRemove){var t={};if(t.control=i,t.cell=j,t.element=null,I.domArgs=t,"function"==typeof i.onBeforeCellDomAdd&&i.onBeforeCellDomAdd(t),t.element){var n=U;if(n){t.Qa=n;if(e(t.element)){if(!i.Sa.Ra)throw new DayPilot.Exception("Can't reach Vue");i.Sa.Ta=!0,i.Sa.Wa(t.element,n),i.Sa.Ta=!1}else n.appendChild(t.element)}}}}(),y.appendChild(I),T&&(i.Dd(y,M,C),this.selected.push(I))}}var Y=document.createElement("div");Y.style.position="absolute",Y.style.left="0px",Y.style.top=b-2+"px",Y.style.width=i.vd(7+_),Y.style.height="1px",Y.style.fontSize="1px",Y.style.lineHeight="1px",Y.className=this.L("_line"),y.appendChild(Y),this.months.push(a)},this.Bd=function(e,t){var n=this.titleHeight+this.dayHeaderHeight,a=(this.pd(),this.showWeekNumbers?1:0);return{"x":i.ud(e+a),"y":t*this.cellHeight+n}},this.Dd=function(e,t,n){var a=e.month.cells[t][n];i.qd(a,"select")},this.Ed=function(e,t,n){var a=e.month.cells[t][n];i.rd(a,"select")},this.pd=function(){return this.showWeekNumbers?r.cellWidth():0},this.Fd=function(){if(this.items)for(var e=0;e<this.showMonths;e++)for(var t=0;t<7;t++)for(var n=0;n<6;n++){var i=this.months[e].cells[t][n];i&&(1===this.items[i.day.toStringSortable()]?(this.qd(i,"busy"),this.rd(i,"free")):(this.rd(i,"busy"),this.qd(i,"free")))}},this.Gd=function(){var e={};e.startDate=i.startDate,e.selectionStart=i.selectionStart,e.selectionEnd=i.selectionEnd.addDays(1),i.state.value=JSON.stringify(e)},this.Ad=function(){return(this.selectMode||"").toLowerCase()},this.Hd=function(){var e=this.selectionDay||this.selectionStart;switch(e||(e=DayPilot.Date.today()),e=new DayPilot.Date(e),this.Ad()){case"day":this.selectionStart=e,this.selectionDay=e,this.selectionEnd=e;break;case"week":this.selectionDay=e,this.selectionStart=e.firstDayOfWeek(r.weekStarts()),this.selectionEnd=this.selectionStart.addDays(6);break;case"month":this.selectionDay=e,this.selectionStart=e.firstDayOfMonth(),this.selectionEnd=this.selectionStart.lastDayOfMonth();break;case"none":this.selectionEnd=e;break;default:throw"Unknown selectMode value."}},this.Id=null,this.select=function(e,t,n){var a=t&&(t instanceof DayPilot.Date||"string"==typeof t),o=t&&"object"==typeof t||"boolean"==typeof t,r=e,s=a?t:null,l=o?t:n;if(!this.Ma)return void(this.Id={"date1":r,"date2":s,"options":l});var d=!0,c=!0;l&&"object"==typeof l?(l.dontFocus&&(d=!1),l.dontNotify&&(c=!1)):"boolean"==typeof l&&(d=!l);var u=this.selectionStart,h=this.selectionEnd;this.selectionStart=new DayPilot.Date(r).getDatePart(),this.selectionDay=this.selectionStart;var f=!1;if(d){var v=this.startDate;(this.selectionStart<this.Jd()||this.selectionStart>=this.Kd())&&(v=this.selectionStart.firstDayOfMonth()),v.toStringSortable()!==this.startDate.toStringSortable()&&(f=!0),this.startDate=v}s&&i.freeHandSelectionEnabled?i.selectionEnd=new DayPilot.Date(s):this.Hd(),this.fd(),this.nd(),this.Ld(),this.Fd(),this.Gd(),!c||u.equals(this.selectionStart)&&h.equals(this.selectionEnd)||this.O(),f&&this.Md()},this.update=function(e){i.Nd(e)},this.Nd=function(e){if(i.ub(e),this.Ma){if(i.Q)throw new DayPilot.Exception("You are trying to update a DayPilot.Navigator instance that has been disposed.");i.Od();var t={"day":i.selectionDay,"start":i.selectionStart,"end":i.selectionEnd};i.sb(),t.start===i.selectionStart&&t.end===i.selectionEnd&&t.day===i.selectionDay||i.O()}},this.sb=function(){this.fd(),this.nd(),this.Hd(),this.Ld(),this.ha(),this.Fd(),this.Gd(),this.visible?this.show():this.hide()},this.Od=function(){i.zb={}},this.wb=null,this.ub=function(e){if(e){var t={"events":{"preInit":function(){var e=this.data;e&&(DayPilot.isArray(e.list)?i.events.list=e.list:i.events.list=e)}}};this.wb=t;for(var n in e)if(t[n]){var a=t[n];a.data=e[n],a.preInit&&a.preInit()}else i[n]=e[n]}},this.xb=function(){var e=this.wb;for(var t in e){var n=e[t];n.postInit&&n.postInit()}},this.U=function(e,t,n){var i={};i.action=e,i.parameters=n,i.data=t,i.header=this.T();var a="JSON"+JSON.stringify(i);this.backendUrl?DayPilot.request(this.backendUrl,this.V,a,this.Pd):WebForm_DoCallback(this.uniqueID,a,this.W,null,this.callbackError,!0)},this.Pd=function(e){if("function"==typeof i.onAjaxError){var t={};t.request=e,i.onAjaxError(t)}else"function"==typeof i.ajaxError&&i.ajaxError(e)},this.V=function(e){i.W(e.responseText)},this.S=function(e,t,n){var a={};a.action=e,a.parameters=n,a.data=t,a.header=this.T();var o="JSON"+JSON.stringify(a);__doPostBack(i.uniqueID,o)},this.T=function(){var e={};return e.v=this.v,e.startDate=this.startDate,e.selectionStart=this.selectionStart,e.showMonths=this.showMonths,e},this.Qd=function(e,t){"refresh"===e&&this.Md()},this.yd=function(e){var t=e+r.weekStarts();return t>6&&(t-=7),r.locale().dayNamesShort[t]},this.zd=function(e){return null!==this.selectionStart&&null!==this.selectionEnd&&(this.selectionStart.getTime()<=e.getTime()&&e.getTime()<=this.selectionEnd.getTime())},this.Rd=function(e){for(var t=0;t<i.months.length;t++){var n=i.months[t];if(!n)return null;if(e.x<n.left)return null;if(!(n.left+n.width<e.x)){i.months[t].height;if(n.top<=e.y&&e.y<n.top+n.height)return t}}return null},this.Sd=function(e){},this.Td=function(){if(!r.od())return void(i.Ud=i.cellWidth);var e=i.months[0].cells[0][0],t=e.clientWidth;i.Ud=t,i.months.forEach(function(e){e.width=e.div.clientWidth,"Horizontal"===i.orientation&&(e.left=e.div.offsetLeft),e.cells.forEach(function(e,n){e.forEach(function(e,i){e.width=t,e.left=n*t})})})},this.Vd=function(e){i.Td();var t=DayPilot.mo3(i.nav.top,e),n=i.Rd(t);if(null===n)return null;var a=i.months[n],o=this.titleHeight+this.dayHeaderHeight;if(a.top<=t.y&&t.y<a.top+o)return{"month":n,"x":0,"y":0,"coords":t,"header":!0};for(var r=0;r<a.cells.length;r++)for(var s=0;s<a.cells[r].length;s++){var l=a.cells[r][s],d=l.top+a.top,c=l.left+a.left;if(c<=t.x&&t.x<c+i.Ud&&d<=t.y&&t.y<d+i.cellHeight)return{"month":n,"x":r,"y":s,"coords":t}}return null},this.Wd=function(e){if(i.freeHandSelectionEnabled){var t=i.Vd(e);t&&!t.header&&(a.start=t),i.months[t.month].cells[t.x][t.y],e.preventDefault()}},this.Xd=function(e){if(a.start){var t=i.Vd(e);if(a.end)a.end=t;else if(t){var n=3,o=DayPilot.distance(a.start.coords,t.coords);o>n&&(a.end=t)}a.end&&(a.clear(),a.draw())}},this.Yd={};var a=this.Yd;a.start=null,a.drawCell=function(e){var t=i.months[e.month],n=i.Bd(e.x,e.y),o=t.top+n.y,s=t.left+n.x,l="px",d=i.vd();if(r.od()){var c="Horizontal"===i.orientation?i.showMonths:1;s=t.leftPct+n.x/c,l="%",d=i.vd(1/c)}var u=document.createElement("div");u.style.position="absolute",u.style.left=s+l,u.style.top=o+"px",u.style.height=i.cellHeight+"px",u.style.width=d,u.style.backgroundColor="#ccc",u.style.opacity=.5,u.style.cursor="default",i.nav.preselection.appendChild(u),a.cells.push(u)},a.clear=function(){if(a.cells){for(var e=0;e<a.cells.length;e++)i.nav.preselection.removeChild(a.cells[e]);a.cells=[]}},a.draw=function(){var e=a.ordered(),t=new o(e.start),n=e.end;if(n){if(n===a.end&&n.header&&n.month>0){n.month-=1;var r=i.months[n.month];n.x=6,n.y=r.rowCount-1}for(a.cells=[];!t.is(n);){t.visible()&&a.drawCell(t);var s=new o(t).next();if(!s)return;t.month=s.month,t.x=s.x,t.y=s.y}t.visible()&&a.drawCell(t)}},a.ordered=function(){var e=a.start,t=a.end,n={};return!t||new o(e).before(t)?(n.start=e,n.end=t):(n.start=t,n.end=e),n};var o=function(e,t,n){if(e instanceof o)return e;if("object"==typeof e){var a=e;this.month=a.month,this.x=a.x,this.y=a.y}else this.month=e,this.x=t,this.y=n;this.is=function(e){return this.month===e.month&&this.x===e.x&&this.y===e.y},this.next=function(){var e=this;if(e.x<6)return{"month":e.month,"x":e.x+1,"y":e.y};var t=i.months[e.month];return e.y<t.rowCount-1?{"month":e.month,"x":0,"y":e.y+1}:e.month<i.months.length-1?{"month":e.month+1,"x":0,"y":0}:null},this.visible=function(){var e=this.cell();return!!e.isCurrentMonth||(!(!e.isPrevMonth||!e.showBefore)||!(!e.isNextMonth||!e.showAfter))},this.nextVisible=function(){for(var e=this;!e.visible();){var t=e.next();if(!t)return null;e=new o(t)}return e},this.previous=function(){var e=this;if(e.x>0)return{"month":e.month,"x":e.x-1,"y":e.y};i.months[e.month];if(e.y>0)return{"month":e.month,"x":6,"y":e.y-1};if(e.month>0){var t=i.months[e.month-1];return{"month":e.month-1,"x":6,"y":t.rowCount-1}}return null},this.previousVisible=function(){for(var e=this;!e.visible();){var t=e.previous();if(!t)return null;e=new o(t)}return e},this.cell=function(){return i.months[this.month].cells[this.x][this.y]},this.date=function(){return this.cell().day},this.before=function(e){return this.date()<new o(e).date()}};this.Cd=function(e){var t=this.parentNode,n=this.parentNode.month,a=this.x,o=this.y,r=n.cells[a][o].day;if(n.cells[a][o].isClickable){i.clearSelection(),i.selectionDay=r;var r=i.selectionDay;switch(i.Ad()){case"none":i.selectionStart=r,i.selectionEnd=r;break;case"day":if(i.autoFocusOnClick){var s=r;if(r<i.Jd()||r>=i.Kd())return void i.select(r)}var l=n.cells[a][o];i.Dd(t,a,o),i.selected.push(l),i.selectionStart=l.day,i.selectionEnd=l.day;break;case"week":if(i.autoFocusOnClick){var s=n.cells[0][o].day,d=n.cells[6][o].day;if(s.firstDayOfMonth()===d.firstDayOfMonth()&&(s<i.Jd()||d>=i.Kd()))return void i.select(r)}for(var c=0;c<7;c++)i.Dd(t,c,o),i.selected.push(n.cells[c][o]);i.selectionStart=n.cells[0][o].day,i.selectionEnd=n.cells[6][o].day;break;case"month":if(i.autoFocusOnClick){var s=r;if(r<i.Jd()||r>=i.Kd())return void i.select(r)}for(var s=null,d=null,o=0;o<6;o++)for(var a=0;a<7;a++){var l=n.cells[a][o];l&&l.day.getYear()===r.getYear()&&l.day.getMonth()===r.getMonth()&&(i.Dd(t,a,o),i.selected.push(l),null===s&&(s=l.day),d=l.day)}i.selectionStart=s,i.selectionEnd=d;break;default:throw"unknown selectMode"}i.Gd(),i.O()}},this.O=function(e){var t=i.selectionStart,n=i.selectionEnd.addDays(1),a=DayPilot.DateUtil.daysDiff(t,n),o=i.selectionDay;if(e=e||{},i.ta()){var r={};if(r.start=t,r.end=n,r.day=o,r.days=a,r.mode=e.mode||i.selectMode,r.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof i.onTimeRangeSelect&&(i.onTimeRangeSelect(r),r.preventDefault.value))return;switch(i.timeRangeSelectedHandling){case"Bind":if("object"==typeof bound){var s={};s.start=t,s.end=n,s.days=a,s.day=o,bound.commandCallBack(i.command,s)}break;case"None":break;case"PostBack":i.timeRangeSelectedPostBack(t,n,o)}"function"==typeof i.onTimeRangeSelected&&i.onTimeRangeSelected(r)}else switch(i.timeRangeSelectedHandling){case"Bind":if("object"==typeof bound){var s={};s.start=t,s.end=n,s.days=a,s.day=o,bound.commandCallBack(i.command,s)}break;case"JavaScript":i.onTimeRangeSelected(t,n,o);break;case"None":break;case"PostBack":i.timeRangeSelectedPostBack(t,n,o)}},this.timeRangeSelectedPostBack=function(e,t,n,i){var a={};a.start=e,a.end=t,a.day=i,this.S("TimeRangeSelected",n,a)},this.xd=function(e){i.Zd(i.skipMonths)},this.wd=function(e){i.Zd(-i.skipMonths)},this.Zd=function(e){this.startDate=this.startDate.addMonths(e),this.fd(),this.nd(),this.Ld(),this.Gd(),this.Md(),this.Fd()},this.Jd=function(){return i.startDate.firstDayOfMonth()},this.Kd=function(){return i.startDate.firstDayOfMonth().addMonths(this.showMonths)},this.visibleStart=function(){return i.startDate.firstDayOfMonth().firstDayOfWeek(r.weekStarts())},this.visibleEnd=function(){return i.startDate.firstDayOfMonth().addMonths(this.showMonths-1).firstDayOfWeek(r.weekStarts()).addDays(42)},this.Md=function(){var e=this.visibleStart(),t=this.visibleEnd();if(i.ta()){var n={};if(n.start=e,n.end=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof i.onVisibleRangeChange&&(i.onVisibleRangeChange(n),n.preventDefault.value))return;switch(this.visibleRangeChangedHandling){case"CallBack":this.visibleRangeChangedCallBack(null);break;case"PostBack":this.visibleRangeChangedPostBack(null);break;case"Disabled":}"function"==typeof i.onVisibleRangeChanged&&i.onVisibleRangeChanged(n)}else switch(this.visibleRangeChangedHandling){case"CallBack":this.visibleRangeChangedCallBack(null);break;case"PostBack":this.visibleRangeChangedPostBack(null);break;case"JavaScript":this.onVisibleRangeChanged(e,t);break;case"Disabled":}},this.visibleRangeChangedCallBack=function(e){var t={};this.U("Visible",e,t)},this.visibleRangeChangedPostBack=function(e){var t={};this.S("Visible",e,t)},this.W=function(e,t){var e=JSON.parse(e);i.items=e.Items,i.cells=e.Cells,i.cells?i.update():i.Fd()},this.Ld=function(){this.showToday&&"Top"===this.todayPosition&&this.$d();for(var e=0;e<this.showMonths;e++){var t=this._d(e);this.gd(e,t)}this.showToday&&"Bottom"===this.todayPosition&&this.$d(),this.root.style.height=this.ae()+"px",this.nav.preselection=document.createElement("div"),this.nav.preselection.style.position="absolute",this.nav.preselection.style.left="0px",this.nav.preselection.style.right="0px",this.nav.preselection.style.top="0px",this.root.appendChild(this.nav.preselection)},this.$d=function(){if(this.showToday){var e=document.createElement("span");e.className=this.L("_todaysection_button"),this.todayHtml?e.innerHTML=this.todayHtml:e.innerText=this.todayText,e.onclick=function(){if("function"==typeof i.onTodayClick){var e={};if(e.preventDefault=function(){this.preventDefault.value=!0},i.onTodayClick(e),e.preventDefault.value)return}i.select(DayPilot.Date.today())};var t=document.createElement("div");t.style.height=this.todayHeight+"px",t.className=this.L("_todaysection"),t.appendChild(e),this.root.appendChild(t)}},this.ae=function(){var e=0;if(this.showToday&&(e+=this.todayHeight),"Horizontal"===this.orientation){for(var t=0;t<this.months.length;t++){var n=this.months[t];n.height>e&&(e=n.height)}return e}for(var t=0;t<this.months.length;t++){var n=this.months[t];e+=n.height}return e},this._d=function(e){if(this.internal.showLinks)return this.internal.showLinks;var t={};return t.left=0===e,t.right=0===e,t.before=0===e,t.after=e===this.showMonths-1,"Horizontal"===this.orientation&&(t.right=e===this.showMonths-1),t},this.wa={},this.wa.scope=null,this.wa.notify=function(){i.wa.scope&&i.wa.scope["$apply"]()},this.Sa={},this.Sa.Ra=null,this.Sa.Ob=function(e,t,n){var a=i.Sa.Ra;if("function"==typeof a.createVNode&&"function"==typeof a.render){var o=a.createVNode(e,n);a.render(o,t)}},this.Sa.Wa=function(e,t){var n=i.Sa.Ra;if("function"==typeof n.render){var a=e;DayPilot.isArray(e)&&(a=n.h("div",null,e)),n.render(a,t)}},this.Sa.Ua=function(e){var t=i.Sa.Ra;"function"==typeof t.render&&t.render(null,e)},this.internal={},this.internal.loadOptions=i.ub,this.internal.initialized=function(){return i.Ma},this.internal.enableVue=function(e){i.Sa.Ra=e},this.internal.vueRef=function(){return i.Sa.Ra},this.internal.vueRendering=function(){return i.Sa.Ta},this.internal.upd=function(e){i.Nd(e)},this.ca={};var r=this.ca;r.locale=function(){return DayPilot.Locale.find(i.locale)},r.weekStarts=function(){if("Auto"===i.weekStarts){var e=r.locale();return e?e.weekStarts:0}return i.weekStarts},r.cellWidth=function(){if(i.zb.cellWidth)return i.zb.cellWidth;var e=i.be("_cell_dimensions").width;return e||(e=i.cellWidth),i.zb.cellWidth=e,e},r.od=function(){return"Auto"===i.ca.cellWidth()},this.clearSelection=function(){for(var e=0;e<this.selected.length;e++){var t=this.selected[e];i.Ed(t.parentNode,t.x,t.y)}this.selected=[]},this.ce=function(){return!!this.backendUrl&&("undefined"==typeof i.items||!i.items)},this.events={},this.ha=function(){if(DayPilot.isArray(this.events.list)){this.items={};for(var e=0;e<this.events.list.length;e++){var t=this.events.list[e];if(!t.hidden){var n=this.ee(t);for(var i in n)this.items[i]=1}}}},this.be=function(e){var t=document.createElement("div");t.style.position="absolute",t.style.top="-2000px",t.style.left="-2000px",t.className=this.L(e);var n=i.root||document.body;n.appendChild(t);var a=t.offsetHeight,o=t.offsetWidth;n.removeChild(t);var r={};return r.height=a,r.width=o,r},this.ee=function(e){for(var t=new DayPilot.Date(e.start),n=new DayPilot.Date(e.end),i={},a=t.getDatePart();a.getTime()<=n.getTime();)i[a.toStringSortable()]=1,a=a.addDays(1);return i},this.show=function(){i.visible=!0,i.root.style.display=""},this.hide=function(){i.visible=!1,i.root.style.display="none"},this.yb=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw"DayPilot.Navigator() constructor requires the target element or its ID as a parameter";if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw"DayPilot.Navigator: The placeholder element not found: '"+t+"'."}this.root=this.nav.top},this.init=function(){if(this.yb(),!this.root.dp){this.Hd(),this.nd(),this.Ld(),this.ha(),this.Fd(),this.aa(),this.fe(),this.rb();if(this.ce()&&this.Md(),this.Ma=!0,this.xb(),this.Id){var e=this.Id;this.select(e.date1,e.date2,e.options),this.Id=null}return this}},this.fe=function(){i.nav.top.onmousedown=this.Wd,i.nav.top.onmousemove=this.Xd},this.rb=function(){DayPilot.re(document,"mouseup",i.ge)},this.ge=function(e){if(a.start&&a.end){var t=DayPilot.mo3(i.nav.top,e);if(t.x===a.start.coords.x&&t.y===a.start.coords.y)return a.start=null,void a.clear();a.clear();var n=a.ordered();n.start=new o(n.start).nextVisible(),n.end=new o(n.end).previousVisible(),i.selectionDay=new o(n.start).date(),i.selectionStart=i.selectionDay,i.selectionEnd=new o(n.end).date(),a.start=null,a.end=null,i.fd(),i.nd(),i.Ld(),i.Fd(),i.Gd();i.O({"mode":"FreeHand"})}a.start=null,a.end=null},this.dispose=function(){var e=i;e.root&&(e.root.removeAttribute("style"),e.root.removeAttribute("class"),e.root.dp=null,e.root.innerHTML=null,e.root=null,e.Q=!0)},this.aa=function(){this.root.dispose=this.dispose},this.Init=this.init,this.ub(n)},"undefined"!=typeof jQuery&&!function(e){e.fn.daypilotNavigator=function(e){var t=null,n=this.each(function(){if(!this.daypilot){var n=new DayPilot.Navigator(this.id);this.daypilot=n;for(var i in e)n[i]=e[i];n.Init(),t||(t=n)}});return 1===this.length?t:n}}(jQuery),function(){var e=DayPilot.am();e&&e.directive("daypilotNavigator",["$parse",function(e){return{"restrict":"E","template":"<div id='{{id}}'></div>","compile":function(t,n){return t.replaceWith(this["template"].replace("{{id}}",n["id"])),function(t,n,i){var a=new DayPilot.Navigator(n[0]);a.wa.scope=t,a.init();var o=i["id"];o&&(t[o]=a);var r=i["publishAs"];if(r){(0,e(r).assign)(t,a)}for(var s in i)if(0===s.indexOf("on")){var l=DayPilot.Util.shouldApply(s);l?!function(n){a[n]=function(a){var o=e(i[n]);t["$apply"](function(){o(t,{"args":a})})}}(s):!function(n){a[n]=function(a){e(i[n])(t,{"args":a})}}(s)}var d=t["$watch"],c=i["config"]||i["daypilotConfig"],u=i["events"]||i["daypilotEvents"];d.call(t,c,function(e,t){for(var n in e)a[n]=e[n];a.update()},!0),d.call(t,u,function(e){a.events.list=e,a.ha(),a.Fd()},!0)}}}}])}(),DayPilot.Navigator.def={},"undefined"!=typeof Sys&&Sys.Application&&Sys.Application.notifyScriptLoaded&&Sys.Application.notifyScriptLoaded()}}(DayPilot),"undefined"==typeof DayPilot)var DayPilot={};"undefined"==typeof DayPilot.Global&&(DayPilot.Global={}),function(DayPilot){"use strict";function e(e){var t=e.touches[0].pageX,n=e.touches[0].pageY,i={};return i.x=t,i.y=n,i}if("undefined"==typeof DayPilot.Scheduler){var t=navigator.userAgent.indexOf("iPad")>-1||navigator.userAgent.indexOf("iPhone")>-1,n={},i=function(){},a=!1,o=function(){return document.body},r=function(){return document.createElement("div")};DayPilot.Scheduler=function(a,s){this.v="2025.3.696-lite";var l=this;this.isScheduler=!0,this.id=a,this.beforeCellRenderCaching=!0,this.businessBeginsHour=9,this.businessEndsHour=18,this.businessWeekends=!1,this.cellDuration=60,this.cellGroupBy="Day",this.cellSweeping=!0,this.cellSweepingCacheSize=1e3,this.cellWidth=40,this.cellsMarkBusiness=!0,this.cssClassPrefix="scheduler_default",this.days=1,this.durationBarHeight=3,this.durationBarVisible=!0,this.dynamicEventRendering="Progressive",this.dynamicEventRenderingMargin=50,this.dynamicEventRenderingMarginX=null,this.dynamicEventRenderingMarginY=null,this.dynamicEventRenderingCacheSweeping=!1,this.dynamicEventRenderingCacheSize=200,this.eventBorderRadius=null,this.eventEndSpec="DateTime",this.eventHeight=35,this.eventMinWidth=1,this.eventResizeMargin=5,this.eventTapAndHoldHandling="Move",this.eventTextWrappingEnabled=!1,this.eventsLoadMethod="GET",this.floatingEvents=!t,this.floatingTimeHeaders=!0,this.headerHeight=30,this.heightSpec="Max",this.height=600,this.locale="en-us",this.progressiveRowRendering=!0,this.progressiveRowRenderingPreload=25,this.rowHeaderWidth=80,this.rowMarginTop=0,this.rowMarginBottom=0,this.rowsLoadMethod="GET",this.scale="CellDuration",this.scrollDelayEvents=200,this.scrollDelayCells=t?100:0,this.scrollDelayFloats=0,this.scrollDelayRows=0,this.showToolTip=!0,this.snapToGrid=!0,this.startDate=DayPilot.Date.today(),this.tapAndHoldTimeout=300,this.timeHeaders=[{"groupBy":"Default"},{"groupBy":"Cell"}],this.timeHeaderTextWrappingEnabled=!1,this.timeFormat="Auto",this.useEventBoxes="Always",this.visible=!0,this.weekStarts="Auto",this.width=null,this.xssProtection="Enabled",this.eventClickHandling="Enabled",this.eventDeleteHandling="Disabled",this.eventMoveHandling="Update",this.eventResizeHandling="Update",this.eventRightClickHandling="ContextMenu",this.timeHeaderClickHandling="Enabled",this.timeHeaderRightClickHandling="Enabled",this.timeRangeClickHandling="Enabled",this.timeRangeSelectedHandling="Enabled",this.onEventClick=null,this.onEventClicked=null,this.onEventMove=null,this.onEventMoved=null,this.onEventResize=null,this.onEventResized=null,this.onRowClick=null,this.onRowClicked=null,this.onTimeHeaderClick=null,this.onTimeHeaderClicked=null,this.onTimeHeaderRightClick=null,this.onTimeHeaderRightClicked=null,this.onTimeRangeClick=null,this.onTimeRangeClicked=null,this.onTimeRangeSelect=null,this.onTimeRangeSelected=null,this.onBeforeCellRender=null,this.onBeforeEventRender=null,this.onBeforeRowHeaderRender=null,this.onAfterUpdate=null,this.Q=!1,this.he=-1,this.ie=!0,this.rowlist=[],this.events={},this.cells={},this.elements={},this.elements.events=[],this.elements.bars=[],this.elements.text=[],this.elements.cells=[],this.elements.linesVertical=[],this.elements.range=[],this.elements.timeHeader=[],this.zb={},this.zb.cells=[],this.zb.linesVertical={},this.zb.linesHorizontal={},this.zb.timeHeaderGroups=[],this.zb.timeHeader={},this.zb.events=[],this.nav={},this.scrollTo=function(e){l.je(e)},this.je=function(e){if(e){if(!l.Ma)return void(l.ke=e);var t;if(e instanceof DayPilot.Date)t=this.getPixels(e).left;else if("string"==typeof e)t=this.getPixels(new DayPilot.Date(e)).left;else{if("number"!=typeof e)throw new DayPilot.Exception("Invalid scrollTo() parameter. Accepted parameters: string (ISO date), number (pixels), DayPilot.Date object");t=e}var n=l.le.clientWidth,i=l.nav.scroll.clientWidth;t<0&&(t=0),t>n-i&&(t=n-i),l.me(t)}},this.scrollToResource=function(e){DayPilot.complete(function(){var t;if("string"==typeof e||"number"==typeof e)t=l.ne(e);else{if(!(e instanceof DayPilot.Row))throw new DayPilot.Exception("Invalid scrollToResource() argument: id or DayPilot.Row expected");t=l.ne(e.id)}t&&setTimeout(function(){var e=t.top;l.nav.scroll.scrollTop=e},100)})},this.oe=function(){if(this.floatingTimeHeaders&&this.timeHeader){var e=l.pe();if(e){l.qe();for(var t=e.pixels.left,n=e.pixels.right+e.sw,i=[],a=0;a<this.timeHeader.length;a++)for(var o=0;o<this.timeHeader[a].length;o++){var r=this.timeHeader[a][o],s=r.left,d=r.left+r.width,c=null;if(s<t&&t<d&&(c={},c.x=o,c.y=a,c.marginLeft=t-s,c.marginRight=0,c.div=l.zb.timeHeader[o+"_"+a],i.push(c)),s<n&&n<d){c||(c={},c.x=o,c.y=a,c.marginLeft=0,c.div=l.zb.timeHeader[o+"_"+a],i.push(c)),c.marginRight=d-n;break}}for(var u=0;u<i.length;u++){var c=i[u];l.se(c.div,c.marginLeft,c.marginRight)}}}},this.te=function(){l.oe(),l.ve()},this.we={};var d=l.we;d.xe=function(e,t,n,i){var a=e,o=e+n,r=t,s=t+i;return l.elements.events.filter(function(e){var t=e.event,n=t.part.left,i=t.part.left+t.part.width,d=l.rowlist[t.part.dayIndex],c=d.top+t.part.top,u=c+l.eventHeight;if(DayPilot.Util.overlaps(n,i,a,o)&&DayPilot.Util.overlaps(c,u,r,s))return!0})},d.ye=function(){var e=[],t=l.pe();if(!t)return e;for(var n=t.pixels.left,i=0;i<l.elements.events.length;i++){var a=l.elements.events[i],o=a.event,r=o.part.left,s=o.part.left+o.part.width;r<n&&n<s&&e.push(a)}return e.area=t,e},this.ve=function(){if(this.floatingEvents){var e=d.ye();l.ze=performance.now(),e.forEach(function(t){var n=t.event,i=e.area.pixels.left,a=n.part.left,o=i-a;l.Ae(t,o,0)}),l.Be()}},this.elements.sections=[],this.elements.hsections=[],this.se=function(e,t,n){var i=r();i.className=l.L("_timeheader_float"),i.style.position="absolute",i.style.left=t+"px",i.style.right=n+"px",i.style.top="0px",i.style.bottom="0px",i.style.overflow="hidden";var a=r();a.className=l.L("_timeheader_float_inner");var o=e.cell.th;a.innerHTML=l.ab(o.text,o.innerHTML),o.fontColor&&(a.style.color=o.fontColor),i.appendChild(a),i.Ce={marginLeft:t,marginRight:n},e.section=i,e.insertBefore(i,e.firstChild.nextSibling),e.firstChild.innerHTML="",this.elements.hsections.push(e)},this.qe=function(){for(var e=0;e<this.elements.hsections.length;e++){var t=this.elements.hsections[e],n=t.cell;n&&t.firstChild&&(t.firstChild.innerHTML=l.ab(n.th.text,n.th.innerHTML)),DayPilot.de(t.section),t.section=null}this.elements.hsections=[]},this.Ae=function(e,t,n){var i=e.section;if(i)return i.Ce&&i.Ce.marginLeft===t&&i.Ce.marginRight===n?void(i.Ce.stamp=l.ze):(e.section.style.left=t+"px",e.section.style.right=n+"px",void(i.Ce={marginLeft:t,marginRight:n,stamp:l.ze}));i=r(),i.className=l.L("_event_float"),i.style.position="absolute",i.style.left=t+"px",i.style.right=n+"px",i.style.top="0px",i.style.bottom="0px",i.style.overflow="hidden";var a=r();a.className=l.L("_event_float_inner"),a.innerHTML=e.event.client.html(),i.appendChild(a),i.Ce={marginLeft:t,marginRight:n,stamp:l.ze},e.section=i,e.insertBefore(i,e.firstChild.nextSibling),e.firstChild.innerHTML="";var o=e.event,s=o.cache||o.data;s.fontColor&&(a.style.color=s.fontColor),this.elements.sections.push(e)},this.Be=function(){for(var e=[],t=0;t<this.elements.sections.length;t++){var n=this.elements.sections[t];if(n.section&&n.section.Ce&&n.section.Ce.stamp===l.ze)e.push(n);else{var i=n.event;i&&(n.firstChild.innerHTML=i.client.html()),DayPilot.de(n.section),n.section=null}}this.elements.sections=e},this.setScrollX=function(e){l.De.enabled?(l.De.scrollXRequested=e,setTimeout(function(){var e=l.De.scrollXRequested;"number"==typeof e&&l.me(e)},0)):l.me(e)},this.me=function(e){var t=l.nav.scroll,n=l.Ee();t.clientWidth+e>n&&(e=n-t.clientWidth),l.divTimeScroll.scrollLeft=e,t.scrollLeft=e},this.setScrollY=function(e){l.De.enabled?(l.De.scrollYRequested=e,setTimeout(function(){var e=l.De.scrollYRequested;"number"==typeof e&&l.Fe(e)},0)):l.Fe(e)},this.Fe=function(e){var t=l.nav.scroll,n=l.he;t.clientHeight+e>n&&(e=n-t.clientHeight),l.divResScroll.scrollTop=e,t.scrollTop=e},this.setScroll=function(e,t){l.setScrollX(e),l.setScrollY(t)},this.ma=function(){if(this.nav.scroll){!function(){var e=l.Ee();l.le.style.height=l.he+"px",l.le.style.width=e+"px",e>l.nav.scroll.clientWidth?l.nav.scroll.style.overflowX="auto":l.nav.scroll.style.overflowX="hidden"}();var e=1;this.nav.scroll.style.height="30px";var t=this.Za(),n=t+this.Ge()+e;if(t>=0&&(this.nav.scroll.style.height=t+"px",this.He.style.height=t+"px"),this.nav.divider&&((!n||isNaN(n)||n<0)&&(n=0),this.nav.divider.style.height=n+"px"),this.nav.top.style.height=n+"px",l.nav.resScrollSpace){var i=30;"Auto"===l.heightSpec&&(i=DayPilot.sh(l.nav.scroll)),l.nav.resScrollSpace.style.height=i+"px"}for(var a=0;a<this.elements.linesVertical.length;a++)this.elements.linesVertical[a].style.height=this.he+"px"}},this.Ie=function(){this.startDate=new DayPilot.Date(this.startDate).getDatePart(),this.timeHeader=[];var e=this.timeHeaders;e||(e=[{"groupBy":this.cellGroupBy},{"groupBy":"Cell"}]);for(var t=l.startDate.addDays(l.days),n=0;n<e.length;n++){var i=e[n].groupBy,a=e[n].format;"Default"===i&&(i=this.cellGroupBy);for(var o=[],r=l.startDate;r.ticks<t.ticks;){var s={};if(s.start=r,s.end=this.Je(s.start,i),s.start.ticks===s.end.ticks)break;s.left=this.getPixels(s.start).left;var d=this.getPixels(s.end).left,c=d-s.left;s.width=c,"string"==typeof a?s.text=s.start.toString(a,f.Ke()):s.text=this.Le(s,i),c>0&&o.push(s),r=s.end}this.timeHeader.push(o)}},this.getPixels=function(e){var t=l.startDate.addDays(l.days);e=e.ticks>t.ticks?t.addTime(-1):e;var n=e.ticks-this.startDate.ticks,i=l.Cb(n),a=l.cellWidth,o=Math.floor(i/a),r=o*a;return{left:i,boxLeft:r,boxRight:r+a,i:o}},this.getDate=function(e,t,n){var i=this.Me(e,n);if(!i)return null;var a=i.x,o=l.Ne(a);if(!o)return null;var r=n&&!t?o.end:o.start;return t?r.addTime(this.Db(i.offset)):r},this.Me=function(e,t){t&&(e-=1);var n=Math.floor(e/l.cellWidth),i=l.Ne(n);if(!i)return null;var a={};return a.x=n,a.offset=e%l.cellWidth,a.cell=i,a},this.Oe=function(e){var t=e.ticks-this.startDate.ticks,n=60*l.Pe()*1e3;if(t<0)return{past:!0};var i=Math.floor(t/n);return{i:i,current:l.Ne(i)}},this.Cb=function(e){var t=60*l.Pe()*1e3,n=l.cellWidth;return Math.floor(n*e/t)},this.Db=function(e){var t=60*l.Pe()*1e3,n=l.cellWidth;return Math.floor(e/n*t)},this.Qe=function(e){DayPilot.Global.touch.start||n.Re||(l.Se={},l.va(this,e))},this.va=function(e,t){e.event&&l.Te(e,t)},this.xa=function(e){if(!DayPilot.Global.touch.active&&!DayPilot.Global.touch.start){var t=this.event;if(e.cancelBubble=!0,e.preventDefault(),!this.event.client.rightClickEnabled())return!1;l.Ue(e);var n={};if(n.e=t,n.div=this,n.originalEvent=e,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onEventRightClick&&(l.onEventRightClick(n),n.preventDefault.value))return!1;switch(l.eventRightClickHandling){case"ContextMenu":var i=t.client.contextMenu();i?i.show(t):l.contextMenu&&l.contextMenu.show(this.event)}return"function"==typeof l.onEventRightClicked&&l.onEventRightClicked(n),!1}},this.Ne=function(e){var t=l.Pe(),n=60*t*1e3;return{start:l.startDate.addTime(e*n),end:l.startDate.addTime((e+1)*n),left:e*l.cellWidth,width:l.cellWidth}},this.Te=function(e,t){if("boolean"==typeof t)throw new DayPilot.Exception("Invalid _eventClickSingle parameters");var n=e.event;if(n){var i=t.ctrlKey,a=t.metaKey;if(n.client.clickEnabled()){l.Ue(t);var o={};o.e=n,o.control=l,o.div=e,o.originalEvent=t,o.ctrl=i,o.meta=a,o.shift=t.shiftKey,o.preventDefault=function(){this.preventDefault.value=!0},o.toJSON=function(){return DayPilot.Util.copyProps(o,{},["e","ctrl","meta","shift"])},"function"==typeof l.onEventClick&&(l.onEventClick(o),o.preventDefault.value)||"function"==typeof l.onEventClicked&&l.onEventClicked(o)}}},this.ya=function(e){var t={};if(t.e=e,t.preventDefault=function(){this.preventDefault.value=!0},t.control=l,t.toJSON=function(){return DayPilot.Util.copyProps(t,{},["e"])},"function"!=typeof l.onEventDelete||(l.onEventDelete(t),!t.preventDefault.value)){switch(l.eventDeleteHandling){case"Update":l.events.remove(e)}"function"==typeof l.onEventDeleted&&l.onEventDeleted(t)}},this.getScrollX=function(){
return this.nav.scroll.scrollLeft},this.getScrollY=function(){return this.nav.scroll.scrollTop},this.K=function(e,t,n,i){if("Disabled"!==this.eventResizeHandling){n=l.Ve(n);var a={};a.e=e,a.async=!1,a.loaded=function(){o()},a.newStart=t,a.newEnd=n,a.what=i,a.preventDefault=function(){this.preventDefault.value=!0},a.control=l,a.toJSON=function(){return DayPilot.Util.copyProps(a,{},["e","async","newStart","newEnd"])};var o=function(){if(!a.preventDefault.value){switch(a.loaded={},t=a.newStart,n=a.newEnd,l.eventResizeHandling){case"Update":l.We(a)}"function"==typeof l.onEventResized&&l.onEventResized(a)}};"function"==typeof l.onEventResize&&l.onEventResize(a),a.async||o()}},this.sb=function(e){e=e||{},clearTimeout(l.Xe),l.timeHeader=null,l.cellProperties={},l.Ie(),l.Ye(),l.events.Ze(),l.ca.Od(),l.clearSelection(),l.ha(),l.$e(),l._e(),l.tb(),l.af(),l.bf(),l.cf(),l.ia(),l.Y(),l.df(),l.ef={},l.ff(),l.ma(),l.gf(),e.immediateEvents?l.na():setTimeout(function(){l.na()},100),this.visible?l.ie!==l.visible&&this.show():this.hide(),this.ie=this.visible,this.te(),l.hf(),this.if()},this.if=function(){if("function"==typeof l.onAfterUpdate){var e={};l.onAfterUpdate(e)}},this.update=function(e){if(!l.Ma)throw new DayPilot.Exception("You are trying to update a DayPilot.Scheduler object that hasn't been initialized.");if(l.Q)throw new DayPilot.Exception("You are trying to update a DayPilot.Scheduler object that has been disposed already. Calling .dispose() destroys the object and makes it unusable.");c.request(e)};var c={};c.timeout=null,c.options=null,c.enabled=!1,c.request=function(e){c.enabled?(clearTimeout(c.timeout),c.jf(e),c.timeout=setTimeout(c.doit)):(c.jf(e),c.doit())},c.jf=function(e){if(e){c.options||(c.options={});for(var t in e)c.options[t]=e[t]}},c.doit=function(){var e=c.options;return c.options=null,l.Ma?(l.ub(e),l.sb({"immediateEvents":!0}),void l.xb()):void l.ub(e)},this.kf=function(e){e.forEach(function(e){l.lf(e.index)})},this.mf=function(e){return e&&0!==e.length?e[0].isRow?e:e.map(function(e){return l.rowlist[e]}):[]},this.nf=function(e,t,n){e=DayPilot.ua(e),e=l.mf(e),l.kf(e),this.of?(this.$e(),this.bf(),this.df(),e.forEach(function(e){l.pf(e.index)}),e.forEach(function(e){l.qf(e.index)}),this.gf(),this.rf()):(e.forEach(function(e){t||l.pf(e.index),l.qf(e.index)}),e.forEach(function(e){l.sf(e.index)}),l.gf()),l.ve(),n&&n(),this.ff()},this.Ve=function(e){return"DateTime"===l.eventEndSpec?e:e.getDatePart().ticks===e.ticks?e.addDays(-1):e.getDatePart()},this.tf=function(e){return"DateTime"===l.eventEndSpec?e:e.getDatePart().addDays(1)},this.uf=function(e){return"DateTime"===l.eventEndSpec?e:e.getDatePart()},this.M=function(e,t,n,i,a){if(l.vf=null,"Disabled"!==l.eventMoveHandling){n=l.Ve(n);var o={};o.e=e,o.newStart=t,o.newEnd=n,o.newResource=i,o.ctrl=!1,o.meta=!1,o.shift=!1,a&&(o.shift=a.shiftKey,o.ctrl=a.ctrlKey,o.meta=a.metaKey),o.control=l,o.areaData=DayPilot.Global.movingAreaData,o.toJSON=function(){return DayPilot.Util.copyProps(o,{},["e","newStart","newEnd","newResource","ctrl","meta","shift"])},o.preventDefault=function(){this.preventDefault.value=!0};var r=function(){if(o.loaded=function(){},o.preventDefault.value)return void l.wf();switch(t=o.newStart,n=o.newEnd,l.eventMoveHandling){case"Update":l.We(o)}l.wf(),"function"==typeof l.onEventMoved&&l.onEventMoved(o)};o.async=!1,o.loaded=function(){r()},"function"==typeof l.onEventMove&&l.onEventMove(o),o.async||r()}},this.We=function(e){var t=e.e,n=e.newStart,i=e.newEnd,a=e.newResource;t.start(n),t.end(i),t.resource(a),l.events.update(t),l.events.xf()},this.yf=function(e){if(e)if(e.args)l.O(e.args.start,e.args.end,e.args.resource);else{var t=l.zf(e);if(!t)return;l.O(t.start,t.end,t.resource)}},this.O=function(e,t,n){if("Disabled"!==l.timeRangeSelectedHandling){var i=t;t=l.Ve(i);var a={};if(a.control=l,a.start=e,a.end=t,a.resource=n,a.preventDefault=function(){this.preventDefault.value=!0},a.toJSON=function(){return DayPilot.Util.copyProps(a,{},["start","end","resource"])},"function"==typeof l.onTimeRangeSelect){if(l.onTimeRangeSelect(a),a.preventDefault.value)return;e=a.start,t=a.end}t=l.tf(t),l.Af(l.Bf,e,t),l.Cf(l.Bf),"function"==typeof l.onTimeRangeSelected&&l.onTimeRangeSelected(a)}},this.Af=function(e,t,n){if(e){var i,a=n;t.getTime()<l.startDate.getTime()?(e.start.x=0,e.start.time=l.startDate.getTime()):(i=l.Oe(t),e.start.x=i.i,e.start.time=t);var o=l.startDate.addDays(l.days);a.getTime()>o.getTime()?(e.end.x=l.Df(),e.end.time=o.getTime()):(i=l.Oe(a.addMilliseconds(-1)),e.end.x=i.i,e.end.time=n)}},this.Ef=function(e,t){l.Ff(e,t)},this.Ff=function(e,t){var n={};n.resource=e,n.row=e,n.ctrl=t.ctrlKey,n.shift=t.shiftKey,n.meta=t.metaKey,n.originalEvent=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onRowClick&&(l.onRowClick(n),n.preventDefault.value)||"function"==typeof l.onRowClicked&&l.onRowClicked(n)},this.Gf=function(e){var t={};t.header=e,t.control=l,t.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onTimeHeaderClick&&(l.onTimeHeaderClick(t),t.preventDefault.value)||"function"==typeof l.onTimeHeaderClicked&&l.onTimeHeaderClicked(t)},this.getViewport=function(){var e=this.nav.scroll.scrollLeft,t=this.nav.scroll.scrollTop,n={},i=l.Hf(e,t),a=l.If(i),o=l.Ne(i.start.x),r=l.Ne(i.end.x);return n.start=l.getDate(e,!0),n.end=l.getDate(e+l.nav.scroll.clientWidth,!0,!0),n.resources=a,o&&(n.topLeft={"start":o.start,"end":o.end,x:i.start.x,y:i.start.y,"resource":a[0]}),r&&(n.bottomRight={"start":r.start,"end":r.end,x:i.end.x,y:i.end.y,"resource":a[a.length-1]}),n.rows=function(){return n.resources.map(function(e){return l.rows.find(e)})},n.events=function(){var e=[];return n.rows().forEach(function(t){e=e.concat(t.events.forRange(n.start,n.end))}),e},n},this.Hf=function(e,t){var n={};n.start={},n.end={};var i=l.Me(e),a=l.Me(e+l.nav.scroll.clientWidth);i&&(n.start.x=i.x),a&&(n.end.x=a.x);var o=t,r=t+l.nav.scroll.clientHeight;n.start.y=l.Jf(o).i,n.end.y=l.Jf(r).i,n.start.x=DayPilot.Util.atLeast(n.start.x,0);var s=l.Df();return n.end.x>=s&&(n.end.x=s-1),n},this.If=function(e){e||(e=this.Hf(this.nav.scroll.scrollLeft,this.nav.scroll.scrollTop));var t=[];t.ignoreToJSON=!0;for(var n=e.start.y;n<=e.end.y;n++){var i=l.rowlist[n];i&&t.push(i.id)}return t},this.L=function(e){var t=this.theme||this.cssClassPrefix;return t?t+e:""},this.tb=function(){l.nav.top.className!==l.L("_main")&&(l.nav.top.className=l.L("_main"),l.nav.dh1.className=l.L("_divider_horizontal"),l.nav.dh2.className=l.L("_divider_horizontal"),l.divResScroll.className=l.L("_rowheader_scroll"),l.nav.divider.className=l.L("_divider")+" "+l.L("_splitter"),l.nav.scroll.className=l.L("_scrollable"),l.le.className=l.L("_matrix")+" "+l.L("_grid_main"))},this.aa=function(){this.nav.top.dispose=this.dispose},this.dispose=function(){var e=l;if(e.Ma&&!e.Q){e.Q=!0;for(var t in e.Kf){var i=e.Kf[t];DayPilot.isArray(i)?i.forEach(function(e){clearTimeout(e)}):clearTimeout(i)}e.Y(),e.divCells=null,e.divCorner=null,e.divEvents=null,e.divHeader&&(e.divHeader.rows=null),e.divHeader=null,e.divLines=null,e.divNorth=null,e.divRange=null,e.divResScroll=null,e.divStretch=null,e.divTimeScroll=null,e.He=null,e.le.calendar=null,e.le=null,e.nav.top.onmousemove=null,e.nav.top.onmouseout=null,e.nav.top.dispose=null,e.nav.top.ontouchstart=null,e.nav.top.ontouchmove=null,e.nav.top.ontouchend=null,e.nav.top.removeAttribute("style"),e.nav.top.removeAttribute("class"),e.nav.top.innerHTML="",e.nav.top.dp=null,e.nav.top=null,e.nav.scroll.onscroll=null,e.nav.scroll.root=null,e.nav.scroll=null,n.sc(e),u=null}},this.disposed=function(){return l.Q},this.ba=function(e){var t=null;t=e.nodeType?e.event:e;var n=l.eventBorderRadius;"number"==typeof n&&(n+="px");var i=l.Lf(t),a=l.rowlist,o=l.eventHeight,s=t.part&&t.part.top&&a[t.part.dayIndex]?t.part.top+a[t.part.dayIndex].top:i.top,d=i.left,c=i.width,u=document.createElement("div");u.style.position="absolute",u.style.width=c+"px",u.style.height=o+"px",u.style.left=d+"px",u.style.top=s+"px",u.style.overflow="hidden";var h=r();return u.appendChild(h),u.className=this.L("_shadow"),h.className=this.L("_shadow_inner"),n&&(u.style.borderRadius=n,h.style.borderRadius=n),l.divShadow.appendChild(u),u.calendar=l,u},this.Jf=function(e){for(var t,n=l.rowlist,i={},a=0,o=0,r=n.length,s=0;s<r;s++){var d=n[s];if(o+=d.height,a=o-d.height,t=d,i.top=a,i.bottom=o,i.i=s,i.element=t,e<o)break}return i},this.Mf=function(e){if(e>this.rowlist.length-1)throw new DayPilot.Exception("Row index too high");for(var t=0,n=0;n<=e;n++)t+=this.rowlist[n].height;var i=this.rowlist[e];return{top:t-i.height,height:i.height,bottom:t,i:e,data:i}},this.events.find=function(e){if(!l.events.list||"undefined"==typeof l.events.list.length)return null;if("function"==typeof e)return l.Nf(e);for(var t=l.events.list.length,n=0;n<t;n++)if(l.events.list[n].id===e)return new DayPilot.Event(l.events.list[n],l);return null},this.events.findAll=function(e){if("function"==typeof e){for(var t=l.events.list.length,n=[],i=0;i<t;i++){var a=new DayPilot.Event(l.events.list[i],l);e(a)&&n.push(a)}return n}if("object"==typeof e)return l.events.findAll(function(t){for(var n in e)if(e[n]!==t.data[n])return!1;return!0});throw new DayPilot.Exception("function or object argument expected")},this.Nf=function(e){for(var t=l.events.list.length,n=0;n<t;n++){var i=new DayPilot.Event(l.events.list[n],l);if(e(i))return i}return null},this.events.focus=function(e){var t=l.Of(e);t&&t.focus()},this.events.scrollIntoView=function(e){if(l.Of(e)){var t=e.start(),n=l.getViewport();!DayPilot.Util.overlaps(n.start,n.end,e.start(),e.end())&&DayPilot.Util.overlaps(l.sa(),l.Pf(),e.start(),e.end())&&l.scrollTo(t,"fast","middle");var i=e.resource();l.getViewport().resources.indexOf(i)===-1&&l.scrollToResource(i)}},this.events.all=function(){for(var e=[],t=0;t<l.events.list.length;t++){var n=new DayPilot.Event(l.events.list[t],l);e.push(n)}return e},this.events.forRange=function(e,t){e=e?new DayPilot.Date(e):l.visibleStart(),t=t?new DayPilot.Date(t):l.visibleEnd();for(var n=[],i=0;i<l.events.list.length;i++){var a=new DayPilot.Event(l.events.list[i],l);DayPilot.Util.overlaps(a.start(),a.end(),e,t)&&n.push(a)}return n},this.events.load=function(e,t,n){if(!e)throw new DayPilot.Exception("events.load(): 'url' parameter required");var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var o={};return o.exception=e,void i(o)}if(DayPilot.isArray(n)){var r={};if(r.preventDefault=function(){this.preventDefault.value=!0},r.data=n,"function"==typeof t&&t(r),r.preventDefault.value)return;l.events.list=r.data,l.Ma&&l.update()}};if(l.eventsLoadMethod&&"POST"===l.eventsLoadMethod.toUpperCase())DayPilot.ajax({"method":"POST","contentType":"application/json","data":{"start":l.visibleStart().toString(),"end":l.visibleEnd().toString()},"url":e,"success":a,"error":i});else{var o=e,r="start="+l.visibleStart().toString()+"&end="+l.visibleEnd().toString();o+=o.indexOf("?")>-1?"&"+r:"?"+r,DayPilot.ajax({"method":"GET","url":o,"success":a,"error":i})}},this.events.Qf=function(e){var t=[];return l.rowlist.forEach(function(n){l.Rf(n.index);for(var i=0;i<n.events.length;i++){var a=n.events[i].data;if(l.Sf(a,e)){t.push(n),n.events.splice(i,1);break}}}),t},this.events.Tf=function(e){if(!e)return null;for(var t=l.rowlist,n=0;n<t.length;n++){var i=t[n];l.Rf(i.index);for(var a=0;a<i.events.length;a++){var o=i.events[a];if(l.Sf(o.data,e))return i.events[a]}}return null},this.events.Uf=function(e){var t=[],n=l.Vf(),i=DayPilot.indexOf(l.events.list,e);l.Ab(i);var a=l.rowlist,o=!1;return a.forEach(function(a){if(!o){l.Rf(a.index);var r=l.Wf(e,a);r&&("function"==typeof l.onBeforeEventRender&&(r.cache=l.zb.events[i]),t.push(a),n||(o=!0))}}),t},this.Sf=function(e,t){return DayPilot.Util.isSameEvent(e,t)},this.events.update=function(e,t,n){if("object"==typeof e&&!(e instanceof DayPilot.Event)){var i=l.events.find(e.id);return l.events.remove(i),void l.events.add(e)}if(n=n||{},l.events.list.find(function(t){return l.Sf(t,e.data)})){l.De.Xf&&(l.De.skip=!0);var a=l.events.Qf(e.data);e.commit(),a=a.concat(l.events.Uf(e.data)),l.events.Yf(a)}},this.events.remove=function(e){if(e){if("string"==typeof e||"number"==typeof e){var t=l.events.find(e);return void l.events.remove(t)}var n=l.Zf(e.data);n&&l.events.list.splice(n.index,1),l.De.Xf&&(l.De.skip=!0);var i=l.events.Qf(e.data);l.events.Yf(i)}},this.events.add=function(e,t,n){var n=n||{},i=n.renderOnly;e instanceof DayPilot.Event||(e=new DayPilot.Event(e)),e.calendar=l,l.events.list||(l.events.list=[]);var a=l.Zf(e);if(i){if(!a)throw new DayPilot.Exception("Unexpected: event not found in list")}else{if(a)throw new DayPilot.Exception("The event you are trying to add using DayPilot.Scheduler.events.add() is already loaded. A unique ID is required.");a||l.events.list.push(e.data)}if(l.Ma){l.De.Xf&&(l.De.skip=!0);var o=l.events.Uf(e.data);l.events.Yf(o)}},this.events.$f=function(e){var t=e instanceof DayPilot.Event?e.data:e,n=new DayPilot.Date(t.start),i=new DayPilot.Date(t.end);return!!l.events.list.find(function(e){if(l.Sf(t,e))return!1;if(t.resource!==e.resource)return!1;var a=new DayPilot.Date(e.start),o=new DayPilot.Date(e.end);return DayPilot.Util.overlaps(n,i,a,o)})},this.events._f={"rows":[]},this.events.ag=null,this.events.Ze=function(){clearTimeout(l.events.ag),l.events.ag=null,l.events._f.rows=[]},this.events.bg=0,this.events.Yf=function(e){var t=l.events._f.rows;e.forEach(function(e){t.push(e)}),l.events._f.rows=DayPilot.ua(t);var n=l.events.xf;l.events.ag||(l.events.ag=setTimeout(n,l.events.bg))},this.events.xf=function(){clearTimeout(l.events.ag),l.events.ag=null;var e=l.events._f.rows;l.events._f.rows=[],l.cg(e),l.dg(),l.Ma&&(l.of&&l.$e(),l.ma(),l.nf(e))},this.De={},this.De.enabled=!1,this.De.skip=!1,this.De.skipUpdate=function(){return l.De.skip},this.De.skipped=function(){l.De.skip=!1},this.De.eg=!1,this.De.Xf=!1,this.ld={},this.ld.reactDOM=null,this.ld.react=null,this.fg=function(e){var t=l.startDate;if(e.ticks===t.ticks)return e;var n=t;if(e.ticks<t.ticks){for(;n.ticks>e.ticks;)n=n.addTime(60*-l.Pe()*1e3);return n}var i=this.Oe(e);if(i.current)return i.current.start;throw new DayPilot.Exception("getBoxStart(): time not found")},this.Lf=function(e){var t=this.Jf(l.coords.y);if("function"!=typeof e.end)throw new DayPilot.Exception("e.end function is not defined");if(!e.end())throw new DayPilot.Exception("e.end() returns null");var i=DayPilot.DateUtil.diff(e.rawend(),e.start());i=DayPilot.Util.atLeast(i,1);var a=f.gg(i),o=0,r=l.coords.x;a&&(o=e.start().getTime()-this.fg(e.start()).getTime());var s=0;if(n.hg)if(a){var d=e.start(),c=this.fg(d);s=n.hg.getTime()-c.getTime();var u=60*l.Pe()*1e3;s=Math.floor(s/u)*u}else s=n.hg.getTime()-e.start().getTime();var h=this.getDate(r,!0).addTime(-s);n.ig&&(h=e.start());var v=l.snapToGrid;v&&(h=this.fg(h)),h=h.addTime(o);var p=h.addTime(i),m=h,y=p,g=this.getPixels(m),b=this.getPixels(y.addTime(-1)),w=v?g.boxLeft:g.left,D=v&&a?b.boxRight-w:b.left-w,x={};return x.top=t.top,x.left=w,x.row=t.element,x.rowIndex=t.i,x.width=D,x.start=h,x.end=p,x.relativeY=l.coords.y-t.top,x},this.Pe=function(){switch(this.scale){case"CellDuration":return this.cellDuration;case"Minute":return 1;case"Hour":return 60;case"Day":return 1440;case"Week":return 10080}throw new DayPilot.Exception("can't guess cellDuration value")},this.jg=function(e){return e.end.ticks-e.start.ticks},this.kg=function(){var e=n.lg.width,t=n.lg.left,i=n.mg,a=n.ig.dpBorder,o=null,r=null,s=l.snapToGrid,d=!s;"left"===a?(o=l.getDate(t,d),r=i.rawend()):"right"===a&&(o=i.start(),r=l.getDate(t+e,d,!0)),n.lg.start=o,n.lg.end=r},this.ng=function(){var e=l.coords,t=n.ig.dpBorder,i=n.ig.event,a=i.part.left;"right"===t&&(a+=i.part.width);var o,r,s=n.ig.event.calendar.cellWidth,d=i.part.width,c=i.part.left,u=0,h=e.x-a,f=l.snapToGrid;if("right"===t){if(o=c,f){var v=l.Me(d+c+h).cell,p=l.Me(c).cell,m=p.left+p.width-c;r=v.left+v.width-c,r<m&&(r=m)}else r=d+h;var y=l.Ee();c+r>y&&(r=y-c),n.lg.left=c,n.lg.width=r,n.lg.style.left=c+"px",n.lg.style.width=r+"px"}else{if("left"!==t)throw new DayPilot.Exception("Invalid dpBorder.");f?(h>=d&&(h=d),o=Math.floor((c+h+0)/s)*s,o<u&&(o=u)):o=c+h,r=d-(o-c);var g=c+d,b=s;f?"Never"===l.useEventBoxes&&(b=d<s?d:1):b=1,r<b&&(r=b,o=g-r),n.lg.left=o,n.lg.width=r,n.lg.style.left=o+"px",n.lg.style.width=r+"px"}l.kg()},this.og=function(){if(l.coords&&n.pg){var e=n.qg,t=this.Lf(n.pg);e.row=t.row,e.style.height=DayPilot.Util.atLeast(t.row.height,0)+"px",e.style.top=t.top+"px",e.style.left=t.left+"px",e.style.width=t.width+"px",e.start=t.start,e.end=t.end}},this.rg=function(){return this.rowHeaderWidth},this.sg=function(){return this.tg(l.progressiveRowRenderingPreload)},this.tg=function(e){e=e||0;var t=0,n=l.rowlist.length;if(l.progressiveRowRendering){var i=l.pe();t=i.yStart,n=i.yEnd+1,t=DayPilot.Util.atLeast(0,t-e),n=Math.min(l.rowlist.length,n+e)}return{"start":t,"end":n}},this._e=function(){function e(){var e=l.divHeader;e&&(e.rows=[]);var t=e;t&&(t.innerHTML="")}this.ug=!0;var t=this.rg();e();var n=this.divHeader;if(n.style.width=t+"px",n.style.height=l.he+"px",l.divHeader=n,l.progressiveRowRendering)i();else for(var a=this.rowlist.length,o=0;o<a;o++)l.vg(o);l.wg(),this.divResScroll.appendChild(n)},this.xg=function(){if(l.progressiveRowRendering)for(var e=this.sg(),t=0;t<l.rowlist.length;t++)e.start<=t&&t<e.end?l.vg(t):l.yg(t)},this.wg=function(){if(!l.ca.zg()){var e=l.divHeader,t=r();t.style.position="absolute",e.appendChild(t),l.nav.resScrollSpace=t;var n=r();n.style.position="relative",n.style.height="100%",n.className=this.L("_rowheader"),t.appendChild(n);var i=this.rg(),t=l.nav.resScrollSpace;t.style.width=i+"px",t.style.top=this.he+"px"}},this.yg=function(e){var t=l.divHeader.rows[e];t&&(DayPilot.de(t),l.divHeader.rows[e]=null)},this.lf=function(e){this.yg(e),this.vg(e)},this.vg=function(e){var t=l.rowlist,n=l.divHeader;if(n&&!n.rows[e]){var i=this.rg(),a=t[e];if(a){var o=this.Ag(a),s=r();s.style.position="absolute",s.style.top=a.top+"px",n.rows[e]=s,s.row=a,s.index=e;var d=o.row,c=this.rowHeaderWidth;s.style.width=c+"px",s.style.border="0px none";var u=d.toolTip||d.toolTip;u&&(s.title=u),"undefined"!=typeof d.ariaLabel?s.setAttribute("aria-label",d.ariaLabel):s.setAttribute("aria-label",d.text||""),s.onclick=l.Bg;var h=r();h.style.width=c+"px",h.className=this.L("_rowheader"),d.cssClass&&DayPilot.Util.addClass(h,d.cssClass),d.cssClass&&DayPilot.Util.addClass(h,d.cssClass);var f=d.backColor||d.backColor;f&&(h.style.background=f);var v=d.fontColor||d.fontColor;v&&(h.style.color=v);var p=d.horizontalAlignment||d.horizontalAlignment;p&&(h.style.textAlign=p),h.style.height=a.height+"px",h.style.overflow="hidden",h.style.position="relative";var m=r();switch(m.className=this.L("_rowheader_inner"),p){case"right":m.style.justifyContent="flex-end";break;case"left":m.style.justifyContent="flex-start";break;case"center":m.style.justifyContent="center"}h.appendChild(m);var y=r();y.style.position="absolute",y.style.bottom="0px",y.style.width="100%",y.style.height="0px",y.style.boxSizing="content-box",y.style.borderBottom="1px solid transparent",y.className=this.L("_resourcedivider"),h.appendChild(y);var g=r(),b=r();b.innerHTML=l.ab(d.text,d.html),b.className=l.L("_rowheader_inner_text"),s.textDiv=b,s.cellDiv=h,g.appendChild(b),m.appendChild(g);var w=d.verticalAlignment||d.verticalAlignment;if(w)switch(m.style.display="flex",w){case"center":m.style.alignItems="center";break;case"top":m.style.alignItems="flex-start";break;case"bottom":m.style.alignItems="flex-end"}s.appendChild(h),n.appendChild(s),h.style.width=i+"px"}}},this.Bg=function(e){var t=this.row,n=l.Cg(t,this.index);l.Ef(n,e)},this.Dg=function(e){if("Disabled"!==l.timeHeaderClickHandling){var t={};t.start=this.cell.start,t.level=this.cell.level,t.end=this.cell.end,t.end||(t.end=new DayPilot.Date(t.start).addMinutes(l.Pe())),l.Gf(t)}},this.Eg=function(e){if("Disabled"!==l.timeHeaderRightClickHandling){e.cancelBubble=!0,e.preventDefault();var t={};t.start=this.cell.start,t.level=this.cell.level,t.end=this.cell.end,t.end||(t.end=new DayPilot.Date(t.start).addMinutes(l.Pe()));var n={};n.header=t,n.preventDefault=function(){this.preventDefault.value=!0},"function"==typeof l.onTimeHeaderRightClick&&(l.onTimeHeaderRightClick(n),n.preventDefault.value)||"function"==typeof l.onTimeHeaderRightClicked&&l.onTimeHeaderRightClicked(n)}},this.Cg=function(e){return new DayPilot.Row(e,l)},this.Rf=function(e){var t=l.rowlist,n=t[e];n.events||n.resetEvents()},this.Fg={},this.ha=function(e){if(e?this.events.list=e:this.events.list||(this.events.list=[]),null!=this.events.list&&!DayPilot.isArray(this.events.list))throw new DayPilot.Exception("DayPilot.Scheduler.events.list expects an array object");u.prepareRows(!0);var t,n=this.events.list,i="function"==typeof this.onBeforeEventRender;l.Fg={};for(var a=0;a<n.length;a++){var o=n[a];if(o){if("object"!=typeof o)throw new DayPilot.Exception("Event data item must be an object");if(!o.start)throw new DayPilot.Exception("Event data item must specify 'start' property");if(o instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Scheduler: DayPilot.Event object detected in events.list array. Use raw event data instead.");if(!("string"==typeof o.id||"number"==typeof o.id))throw new DayPilot.Exception("All events must have an id property (string or number)");var r="_"+o.id;if(l.Fg[r])throw new DayPilot.Exception("Duplicate event IDs are not allowed: "+r);l.Fg[r]=!0,i&&this.Ab(a),t=l.Gg(o.resource);for(var s=0;t&&s<t.length;s++){var d=t[s],c=this.Wf(o,d);c&&i&&(c.cache=this.zb.events[a])}}}l.rowlist.forEach(function(e){l.Hg(e)}),l.dg()},this.Ig={};var u=this.Ig;u.rowcache={},u.prepareRows=function(e){u.rowcache={};for(var t=l.rowlist,n=0;n<t.length;n++){var i=t[n];if(e&&i.resetEvents(),l.Rf(i.index),i.id){var a=typeof i.id+"_"+i.id;u.rowcache[a]||(u.rowcache[a]=[]),u.rowcache[a].push(i)}}},u.loadEvent=function(e){},this.Gg=function(e){var t=typeof e+"_"+e;return u.rowcache[t]||[]},this.Vf=function(){for(var e={},t=0;t<l.rowlist.length;t++){var n=l.rowlist[t],i=n.id;if(e[i])return!0;e[i]=!0}return!1},this.Ab=function(e){var t=this.zb.events,n=this.events.list[e],i={};n instanceof DayPilot.Event&&(n=n.data);for(var a in n)i[a]=n[a];if("string"==typeof i.start&&(i.start=new DayPilot.Date(i.start)),"string"==typeof i.end&&(i.end=new DayPilot.Date(i.end)),"function"==typeof this.onBeforeEventRender){var o={};o.e=i,o.data=i,this.onBeforeEventRender(o)}t[e]=i},this.Hg=function(e){e.lines=[],e.sections=null,e.events.sort(this.Bb);for(var t=0;t<e.events.length;t++){var n=e.events[t];e.putIntoLine(n)}},this.cg=function(e){e=DayPilot.ua(e),e=l.mf(e),e.forEach(function(e){l.Hg(e)}),e.forEach(function(e){l.Jg(e)})},this.Wf=function(e,t){var n=new DayPilot.Date(e.start),i=new DayPilot.Date(e.end);i=l.tf(i);var a=n.ticks,o=i.ticks,r=l.startDate.ticks,s=l.startDate.addDays(l.days).ticks;if(o<a)return null;var d=null;if("function"==typeof l.onBeforeEventRender){var c=DayPilot.indexOf(l.events.list,e);d=l.zb.events[c]}if(t.id!==e.resource||(o<=r||a>=s)&&(a!==o||a!==r))return null;var u=new DayPilot.Event(e,l);u.part.dayIndex=l.rowlist.indexOf(t),u.part.start=r<a?n:l.startDate,u.part.end=s>o?i:l.startDate.addDays(l.days);var h=this.getPixels(u.part.start),v=this.getPixels(u.part.end.addTime(-1));u.part.start===u.part.end&&(v=this.getPixels(u.part.end.addMilliseconds(1)));var p=h.left,m=v.left;if(f.gg(o-a)){var y=h.boxLeft,g=v.boxRight;u.part.left=y,u.part.width=g-y,u.part.barLeft=Math.max(p-u.part.left,0),u.part.barWidth=Math.max(m-p,1)}else u.part.left=p,u.part.width=Math.max(m-p,1),u.part.barLeft=0,u.part.barWidth=Math.max(m-p-1,1);var b=l.eventMinWidth;return u.part.width=Math.max(u.part.width,b),u.part.right=u.part.left+u.part.width,u.cache=d,t.events.push(u),u},this.Bb=function(e,t){if(!(e&&t&&e.start&&t.start))return 0;var n=e.start().ticks-t.start().ticks;return 0!==n?n:t.end().ticks-e.end().ticks},this.rows={},this.rows.all=function(){for(var e=[],t=0;t<l.rowlist.length;t++){var n=l.Cg(l.rowlist[t]);e.push(n)}return e},this.rows.each=function(e){l.rows.all().forEach(e)},this.rows.forEach=function(e){l.rows.all().forEach(e)},this.rows.find=function(e,t){if("string"==typeof e||"number"==typeof e||!e&&t){var n=l.Gg(e);e||(n=l.rowlist);var i=null;return"string"==typeof t||t instanceof DayPilot.Date?(t=new DayPilot.Date(t),i=n.find(function(e){return t===e.start})):i=n[0],i?new DayPilot.Row(i,l):null}if("function"!=typeof e)throw new DayPilot.Exception("Invalid rows.find() argument: id or function expected");var a=t||0,o=l.rowlist.find(function(t,n){return!(n<a)&&e(l.Cg(t))});if(o)return l.Cg(o)},this.rows.load=function(e,t,n){if(!e)throw new DayPilot.Exception("rows.load(): 'url' parameter required");var i=function(e){var t={};t.exception=e.exception,t.request=e.request,"function"==typeof n&&n(t)},a=function(e){var n,a=e.request;try{n=JSON.parse(a.responseText)}catch(e){var o={};return o.exception=e,void i(o)}if(DayPilot.isArray(n)){var r={};if(r.preventDefault=function(){this.preventDefault.value=!0},r.data=n,"function"==typeof t&&t(r),r.preventDefault.value)return;l.resources=r.data,l.Ma&&l.update()}};l.rowsLoadMethod&&"POST"===l.rowsLoadMethod.toUpperCase()?DayPilot.ajax({"method":"POST","url":e,"success":a,"error":i}):DayPilot.ajax({"method":"GET","url":e,"success":a,"error":i})},this.rows.remove=function(e){if("number"==typeof e||"string"==typeof e){var t=l.rows.find(e);if(!t)throw new DayPilot.Exception("The row to be removed was not found");return void l.rows.remove(t)}var n=e.$.row.resource,i=DayPilot.indexOf(l.resources,n);l.resources.splice(i,1),l.update()},this.rows.add=function(e){l.resources.push(e),l.sb()},this.rows.update=function(e){if(!(e instanceof DayPilot.Row||"object"==typeof e))throw new DayPilot.Exception("DayPilot.Scheduler.rows.update() expects a DayPilot.Row object or a data object.");if(!(e instanceof DayPilot.Row)){var t=e;if(e=l.rows.find(t.id),!e)return;e.data=t}var n=e.index,i=l.rowlist,a=i[n],o=e.data,r=e.parent()?e.parent().$.row:null,s=l.Kg(o),e=l.Lg(s,r);e.level=a.level,e.index=n,e.top=a.top,e.height=a.height,i[n]=e,e.resetEvents(),l.Rf(e.index),l.Mg(e),l.Hg(e),l.lf(e.index),l.df(),l.gf()},this.Mg=function(e){for(var t=l.events.list,n=t.length,i="function"==typeof l.onBeforeEventRender,a=0;a<n;a++){var o=t[a];if(o){if(o instanceof DayPilot.Event)throw new DayPilot.Exception("DayPilot.Scheduler: DayPilot.Event object detected in events.list array. Use raw event data instead.");if(o.resource===e.id){i&&this.Ab(a);var r=this.Wf(o,e);r&&i&&(r.cache=this.zb.events[a])}}}},this.Ye=function(){var e=this.resources;if({}.i=0,null!=e&&!DayPilot.isArray(e))throw new DayPilot.Exception("DayPilot.Scheduler.resources expects an array object");e=e||[],l.rowlist=[],this.Ng(e)},this.sa=function(){return new DayPilot.Date(this.startDate)},this.Pf=function(){return new DayPilot.Date(l.startDate).addDays(l.days)},this.visibleStart=function(){return this.sa()},this.visibleEnd=function(){return this.Pf()},this.Lg=function(e){var t={};return t.backColor=e.backColor,t.fontColor=e.fontColor,t.cssClass=e.cssClass,t.name=e.name,t.html=l.ab(e.name,e.html),t.id=e.id,t.toolTip=e.toolTip,t.tags=e.tags,t.height=l.eventHeight,t.level=0,t.resource=e.Ce,t.lines=[],t.isRow=!0,t.getHeight=function(){return Math.max(l.eventHeight,this.lines.length*l.eventHeight)},t.resetEvents=function(){var e=this;e.events=[],e.events.forRange=function(t,n){t=new DayPilot.Date(t),n=n?new DayPilot.Date(n):l.startDate.addDays(l.days);for(var i=[],a=0;a<e.events.length;a++){var o=e.events[a],r=l.tf(o.end());DayPilot.Util.overlaps(o.start(),r,t,n)&&i.push(o)}return i}},t.Og=function(){var e=[];return e.add=function(e){this.push(e)},e.isFree=function(e,t,n){for(var i=e+t-1,a=this.length,o=0;o<a;o++){var r=this[o];if(!(i<r.part.left||e>r.part.left+r.part.width-1)){if(DayPilot.contains(n,r.data))continue;return!1}}return!0},e},t.findFreeLine=function(e,n){for(var i=0;i<this.lines.length;i++){var a=this.lines[i];if(a.isFree(e,n))return i}var a=t.Og();return this.lines.push(a),this.lines.length-1},t.putIntoLine=function(e){var n=t.findFreeLine(e.part.left,e.part.width);return this.lines[n].add(e),n},t},this.Ng=function(e){if(e)for(var t=l.rowlist,n=0;n<e.length;n++)if(e[n]){var i={};i.index=n;var a=this.Kg(e[n],i),o=l.Lg(a,parent);o.index=n,t.push(o)}},this.Ag=function(e){var t={};return t.row=this.Cg(e),DayPilot.Util.copyProps(e,t.row,["html","backColor","fontColor","cssClass","toolTip"]),"function"==typeof this.onBeforeRowHeaderRender&&this.onBeforeRowHeaderRender(t),t},this.Kg=function(e,t){var n={get $data(){return this.Ce}};for(var i in t)n[i]=t[i];for(var i in e)n[i]=e[i];return n.html=l.ab(e.name,e.html),n.Ce=e,n},this.Pg=function(){this.nav.top.dp=this,this.nav.top.innerHTML="",DayPilot.Util.addClass(this.nav.top,this.L("_main")),this.nav.top.setAttribute("role","region"),this.nav.top.setAttribute("aria-label","scheduler"),this.nav.top.style.userSelect="none",this.nav.top.style.webkitUserSelect="none",this.nav.top.style.WebkitTapHighlightColor="rgba(0,0,0,0)",this.nav.top.style.WebkitTouchCallout="none",this.width&&(this.nav.top.style.width=this.width),this.nav.top.style.lineHeight="1.2",this.nav.top.style.position="relative",this.visible||(this.nav.top.style.display="none"),this.nav.top.ontouchstart=h.Qg,this.nav.top.ontouchmove=h.Rg,this.nav.top.ontouchend=h.Sg;var e=this.rowHeaderWidth,t=r();t.style.position="absolute",t.style.left="0px",t.style.width=e+"px";var n=r();n.style.height="0px",n.style.boxSizing="content-box",n.style.borderTop="1px solid transparent",n.className=this.L("_divider_horizontal"),this.nav.dh1=n,this.nav.left=t,t.appendChild(this.gb()),t.appendChild(n),t.appendChild(this.Tg());var i=r();i.style.position="absolute",i.style.left=e+"px",i.style.width="1px",i.style.height=this.Ge()+this.Za()+"px",i.className=this.L("_splitter"),this.nav.divider=i;var a=r();a.style.marginLeft=e+1+"px",a.style.position="relative",this.nav.right=a;var o=r();o.style.position="absolute",o.style.top=this.Ge()+"px",o.style.width="100%",o.style.height="1px",o.style.boxSizing="border-box",o.style.borderBottom="1px solid transparent",o.setAttribute("data-dh2","true"),o.className=this.L("_divider_horizontal"),this.nav.dh2=o,a.appendChild(l.Ug()),a.appendChild(l.Vg()),a.appendChild(o);var s=r();s.style.clear="left";var d=r();d.style.height="1px",d.style.position="absolute",d.style.left="0px",d.style.right="0px",d.style.display="none",d.className=this.L("_divider_horizontal"),this.nav.dividerTop=d;var c=r();c.style.height="1px",c.style.position="absolute",c.style.left="0px",c.style.right="0px",c.style.display="none",c.className=this.L("_divider_horizontal")+" "+this.L("_divider_horizontal_frozen_bottom"),this.nav.dividerBottom=c,this.nav.top.appendChild(t),this.nav.top.appendChild(i),this.nav.top.appendChild(a),this.nav.top.appendChild(s),this.nav.top.appendChild(d),this.nav.top.appendChild(c)},this.ia=function(){var e=this.Ge();this.nav.corner&&(this.nav.corner.style.height=e+"px"),this.divTimeScroll&&(this.divTimeScroll.style.height=e+"px"),this.divNorth&&(this.divNorth.style.height=e+"px"),this.nav.dh1&&this.nav.dh2&&(this.nav.dh1.style.top=e+"px",this.nav.dh2.style.top=e+"px"),this.nav.scroll.style.top=e+1+"px"},this.Wg=function(){var e=this.rowHeaderWidth;this.nav.corner&&(this.nav.corner.style.width=e+"px"),this.divResScroll.style.width=e+"px",this.nav.left.style.width=e+"px",this.nav.divider.style.left=e-1+"px",this.nav.right.style.marginLeft=e+"px"},this.Xg=function(){var e=this.rowHeaderWidth,t=this.divHeader;t.style.width=e+"px";for(var n=l.sg(),i=n.start;i<n.end;i++){var a=t.rows[i];if(a){var o=l.rowHeaderWidth;a.style.width=o+"px";a.firstChild.style.width=o+"px"}}l.nav.resScrollSpace&&(l.nav.resScrollSpace.style.width=e+"px")},this.cf=function(){this.Wg(),this.Xg()},this.gb=function(){var e=this.rowHeaderWidth,t=r();l.nav.corner=t,
t.style.width=e+"px",t.style.height=this.Ge()+"px",t.style.overflow="hidden",t.style.position="relative",t.oncontextmenu=function(){return!1},t.className=this.L("_corner");var n=r();return n.style.position="absolute",n.style.top="0px",n.style.left="0px",n.style.right="0px",n.style.bottom="0px",n.className=this.L("_corner_inner"),n.innerHTML="&nbsp;",this.divCorner=n,t.appendChild(n),t},this.Ge=function(){return l.timeHeaders?l.timeHeaders.length*l.headerHeight:0},this.Yg=null,this.Tg=function(){var e=r();e.style.width=this.rowHeaderWidth+"px",e.style.height=this.Za()+"px",e.style.overflow="hidden",e.style.position="relative",e.className=l.L("_rowheader_scroll");var i=l.ca.zg();i&&(e.style.overflowY="auto"),e.ontouchstart=function(){n.Zg=!0},e.oncontextmenu=function(){return!1},e.onscroll=function(){if(l.Yg&&clearTimeout(l.Yg),i){var a=function(){var t=l.$g()-l.nav.scroll.offsetHeight;e.scrollTop=Math.min(e.scrollTop,t),l.nav.scroll.scrollTop=e.scrollTop};t?n.Zg&&(l.Yg=setTimeout(a,10)):l.Yg=setTimeout(a,10)}else l.Yg=setTimeout(function(){l.nav.scroll.scrollTop=e.scrollTop},500)},e.setAttribute("role","region"),e.setAttribute("aria-label","scheduler rows");var a=r();return this.divHeader=a,e.appendChild(a),this.divResScroll=e,this.He=e,e},this._g=null,this.ah=null,this.bh=function(){var e=function(){l.ch(),l.hf()},t=function(){var t=l.nav.top;if(t){if(!l._g)return l._g={},void(l._g.width=t.offsetWidth);l._g.width!==t.offsetWidth&&(l._g.width=t.offsetWidth,e())}};if(!l.ah){var n=new ResizeObserver(DayPilot.debounce(t,100));n.observe(l.nav.top),l.ah=n}},this.ch=function(){l.Q||(l.ma(),l.dh(),l.zb.drawArea=null)},this.dh=function(){var e=l.Bf;l.clearSelection(),l.Bf=e,l.Cf(e,{"justDraw":!0})},this.Ug=function(){var e=r();e.style.overflow="hidden",e.style.display="block",e.style.position="absolute",e.style.top="0px",e.style.width="100%",e.style.height=this.Ge()+"px",e.style.overflow="hidden",e.className=l.L("_timeheader_scroll"),this.divTimeScroll=e;var t=r();return t.style.width=this.Ee()+5e3+"px",this.divNorth=t,e.appendChild(t),e},this.Za=function(){var e=0,t=l.heightSpec;return"Fixed"===t?this.height?this.height:0:(e=l.$g(),"Max"===t&&e>l.height?l.height:e)},this.$g=function(){var e;return this.he!==-1?(e=this.he,this.he>0&&"auto"===l.nav.scroll.style.overflowX&&(e+=DayPilot.sh(l.nav.scroll)+1)):e=this.rowlist.length*l.eventHeight,e},this.Vg=function(){var e=r();e.style.overflow="auto",e.style.overflowX="auto",e.style.overflowY="auto",e.style.height=this.Za()+"px",e.style.top=this.Ge()+1+"px",e.style.position="absolute",e.style.width="100%",e.className=this.L("_scrollable"),e.oncontextmenu=function(){return!1},this.nav.scroll=e,this.le=r();var t=this.le;t.style.userSelect="none",t.style.webkitUserSelect="none",t.calendar=this,t.style.position="absolute";var n=this.Ee();return n>0&&(t.style.width=n+"px"),t.onmousedown=this.eh,t.onmousemove=this.fh,t.onmouseup=this.gh,t.oncontextmenu=this.hh,t.className=this.L("_matrix"),this.divStretch=r(),this.divStretch.style.position="absolute",this.divStretch.style.height="1px",t.appendChild(this.divStretch),this.divCells=r(),this.divCells.style.position="absolute",this.divCells.oncontextmenu=this.hh,t.appendChild(this.divCells),this.divLines=r(),this.divLines.style.position="absolute",this.divLines.oncontextmenu=this.hh,t.appendChild(this.divLines),this.divSeparators=r(),this.divSeparators.style.position="absolute",this.divSeparators.oncontextmenu=this.hh,t.appendChild(this.divSeparators),this.divRange=r(),this.divRange.style.position="absolute",this.divRange.oncontextmenu=this.hh,t.appendChild(this.divRange),this.divEvents=r(),this.divEvents.style.position="absolute",t.appendChild(this.divEvents),this.divShadow=r(),this.divShadow.style.position="absolute",t.appendChild(this.divShadow),e.appendChild(t),e},this.rb=function(){n.ih||(n.ih=!0,DayPilot.re(document,"mouseup",n.ge),DayPilot.re(document,"touchmove",n.jh),DayPilot.re(document,"touchend",n.kh))},this.lh=function(){this.nav.scroll.root=this,this.nav.scroll.onscroll=this.hf,l.mh=this.nav.scroll.scrollLeft,l.nh=this.nav.scroll.scrollTop,this.divNorth&&(l.oh=this.divNorth.clientWidth)},this.ph={},this.ph.step=300,this.ph.delay=10,this.ph.mode="display",this.ph.layers=!1,this.Jg=function(e){for(var t=0,n=0;n<e.lines.length;n++){for(var i=e.lines[n],a=0;a<i.length;a++){var o=i[a];o.part.line=n,o.part.top=t+l.rowMarginTop,o.part.right=o.part.left+o.part.width}t+=l.eventHeight}},this.Xe=null,this.na=function(e){if(!l.Q){var t=this.ph.step;"display"===this.ph.mode?this.divEvents.style.display="none":"visibility"===this.ph.mode&&(this.divEvents.style.visibility="hidden"),this.divEvents.setAttribute("role","region"),this.divEvents.setAttribute("aria-label","scheduler events");var n="Progressive"===this.dynamicEventRendering,i=this.pe(),a=i.pixels.top,o=i.pixels.bottom;l.rowlist.filter(function(e){var t=e.top-l.dynamicEventRenderingMargin,i=t+e.height+2*l.dynamicEventRenderingMargin;return!n||!(o<=t||a>=i)}).forEach(function(n){l.Jg(n);for(var i=0;i<n.lines.length;i++)for(var a=n.lines[i],o=0;o<a.length;o++){var r=a[o],s=l.Va(r);if(e&&s&&(t--,t<=0))return l.divEvents.style.visibility="",l.divEvents.style.display="",void(l.Xe=setTimeout(function(){l.na(e)},l.ph.delay))}}),this.divEvents.style.display="",this.ve()}},this.qf=function(e){var t=l.rowlist[e];this.Jg(t);for(var n=0;n<t.lines.length;n++)for(var i=t.lines[n],a=0;a<i.length;a++){var o=i[a];this.Va(o)}},this.Y=function(){if(this.elements.events)for(var e=this.elements.events.length,t=0;t<e;t++){var n=this.elements.events[t];this.Pa(n)}this.elements.events=[]},this.pf=function(e){if(this.elements.events){for(var t=this.elements.events.length,n=[],i=0;i<t;i++){var a=this.elements.events[i];a.event.part.dayIndex===e&&(this.Pa(a),n.push(i))}for(var i=n.length-1;i>=0;i--)this.elements.events.splice(n[i],1)}},this.Pa=function(e){e.remove(),e.onclick=null,e.oncontextmenu=null,e.onmouseover=null,e.onmouseout=null,e.onmousemove=null,e.onmousedown=null,e.ondblclick=null,e.event&&(e.event.rendered=null,e.event=null)},this.qh=function(){if("Progressive"===this.dynamicEventRendering){if(l.dynamicEventRenderingCacheSweeping){var e=l.dynamicEventRenderingCacheSize||0;this.divEvents.style.display="none";for(var t=[],n=0,i=this.elements.events.length,a=i-1;a>=0;a--){var o=this.elements.events[a];this.rh(o.event)?e>0?(e--,t.unshift(o)):(this.Pa(o),n++):t.unshift(o)}this.elements.events=t,this.divEvents.style.display=""}}},this.uh=function(e){for(var t=this.pe(),n=this.elements.cells.length,i=n-1;i>=0;i--){var a=this.elements.cells[i];t.xStart<a.coords.x&&a.coords.x<=t.xEnd&&t.yStart<a.coords.y&&a.coords.y<=t.yEnd||(e>0?e--:this.hb(a))}},this.hb=function(e){if(e&&e.coords){var t=e.coords.x,n=e.coords.y;DayPilot.rfa(l.elements.cells,e),DayPilot.de(e),l.zb.cells[t+"_"+n]=null}},this.vh=function(){var e="Progressive"===this.dynamicEventRendering;if(!this.nav.scroll)return!1;for(var t=this.nav.scroll.scrollTop,n=t+this.nav.scroll.clientHeight,i=0;i<this.rowlist.length;i++){var a=this.rowlist[i],o=a.top,r=a.top+a.height;if(!e||!(n<=o||t>=r))for(var s=0;s<a.lines.length;s++)for(var l=a.lines[s],d=0;d<l.length;d++){var c=l[d];if(this.wh(c))return!0}}return!1},this.wh=function(e){if(e.rendered)return!1;var t="Progressive"===this.dynamicEventRendering,n=this.nav.scroll.scrollLeft,i=n+this.nav.scroll.clientWidth,a=e.part.left,o=e.part.left+e.part.width;return!t||!(i<=a||n>=o)},this.rh=function(e){if(!e.rendered)return!0;var t=this.pe(),n=t.pixels.top,i=t.pixels.bottom,a=t.pixels.left-this.dynamicEventRenderingMargin,o=t.pixels.right+this.dynamicEventRenderingMargin,r=e.part.left,s=e.part.right,d=e.part.top,c=e.part.top+l.eventHeight;return o<=r||a>=s||(i<=d||n>=c)},this.Va=function(e,t){t=t||{};var n=t.forced;if(e.rendered)return!1;var i="Progressive"===this.dynamicEventRendering,a=e.part.dayIndex,o=l.divEvents,s=l.rowlist,d=s[a],c=d.top,u=this.pe(),f=u.pixels.left-this.dynamicEventRenderingMargin,v=u.pixels.right+this.dynamicEventRenderingMargin,p=u.pixels.top,m=u.pixels.bottom,y=e.part.left,g=e.part.left+e.part.width,b=e.part.top+c,w=b+l.eventHeight,D=v<=y||f>=g,x=m<=b||p>=w;if(!n&&i&&(D||x))return!1;var _=e.part.width,C=l.eventHeight,k=e.cache||e.data,S=k.borderRadius||l.eventBorderRadius;"number"==typeof S&&(S+="px"),_=DayPilot.Util.atLeast(0,_),C=DayPilot.Util.atLeast(0,C);var P=r();P.style.position="absolute",P.style.left=e.part.left+"px",P.style.top=c+e.part.top+"px",P.style.width=_+"px",P.style.height=C+"px",l.eventTextWrappingEnabled||(P.style.whiteSpace="nowrap"),P.style.overflow="hidden",P.className=this.L("_event"),k.cssClass&&DayPilot.Util.addClass(P,k.cssClass);"number"==typeof e.part.line&&DayPilot.Util.addClass(P,this.L("_event_line"+e.part.line)),S&&(P.style.borderRadius=S),this.showToolTip&&(P.title=e.client.toolTip()||""),P.onmousemove=this.xh,P.onmousedown=this.yh,P.onmouseup=this.zh,P.ontouchstart=h.Ah,P.ontouchmove=h.Bh,P.ontouchend=h.Ch,e.client.clickEnabled()&&(P.onclick=this.Qe),"undefined"!=typeof k.ariaLabel?P.setAttribute("aria-label",k.ariaLabel):P.setAttribute("aria-label",k.text),P.setAttribute("tabindex","-1");var M=r();M.className=l.L("_event_inner"),S&&(M.style.borderRadius=S),k.backColor&&(M.style.background=k.backColor),k.fontColor&&(M.style.color=k.fontColor),"darker"===k.borderColor&&k.backColor?M.style.borderColor=DayPilot.ColorUtil.darker(k.backColor,2):M.style.borderColor=k.borderColor,k.backImage&&(M.style.backgroundImage="url("+k.backImage+")",k.backRepeat&&(M.style.backgroundRepeat=k.backRepeat)),P.appendChild(M);var T=e.start().getTime()===e.part.start.getTime(),A=e.rawend().getTime()===e.part.end.getTime();if(T||DayPilot.Util.addClass(P,this.L("_event_continueleft")),A||DayPilot.Util.addClass(P,this.L("_event_continueright")),e.client.barVisible()&&_>0){var E=100*e.part.barLeft/_,H=Math.ceil(100*e.part.barWidth/_),N=r();N.className=this.L("_event_bar"),N.style.position="absolute",k.barBackColor&&(N.style.backgroundColor=k.barBackColor);var I=r();I.className=this.L("_event_bar_inner"),I.style.left=E+"%",0<H&&H<=1?I.style.width="1px":I.style.width=H+"%",k.barColor&&(I.style.backgroundColor=k.barColor),k.barImageUrl&&(I.style.backgroundImage="url("+k.barImageUrl+")"),N.appendChild(I),P.appendChild(N)}P.row=a,P.event=e,M.innerHTML=e.client.innerHTML();var R=[];return function(){if("Disabled"!==l.eventDeleteHandling&&!k.deleteDisabled){var e=l.durationBarVisible?l.durationBarHeight:0;R.push({v:"Hover",w:17,h:17,top:e+2,right:2,css:l.L("_event_delete"),onClick:function(e){l.ya(e.source)}})}}(),k.areas&&(R=R.concat(k.areas)),DayPilot.Areas.attach(P,e,{areas:R}),this.elements.events.push(P),o.appendChild(P),e.rendered=!0,!0},this.ab=function(e,t){return l.ca.da()?DayPilot.Util.escapeTextHtml(e,t):DayPilot.Util.isNullOrUndefined(t)?DayPilot.Util.isNullOrUndefined(e)?"":e:t},this.rf=function(){for(var e=0;e<this.elements.events.length;e++){var t=this.elements.events[e],n=t.event,i=n.part.dayIndex,a=l.rowlist[i],o=a.top,r=o+n.part.top,s=a.height,d=l.eventHeight,c=!1;n.part.top+d>s&&(d=Math.max(0,s-n.part.top),c=!0),t.style.top=r+"px",t.style.height=d+"px"}},this.Dh=function(e){if(!e)return null;for(var t=0;t<l.elements.events.length;t++){var n=l.elements.events[t];if(n.event===e||n.event.data===e.data)return n}return null},this.Of=function(e){var t=l.events.Tf(e.data);if(!t)return null;var n=l.rowlist[t.part.dayIndex];return l.Jg(n),l.Va(t,{"forced":!0}),l.Dh(t)},this.xh=function(e){for(var t=this;t&&!t.event;)t=t.parentNode;t.event;l.Eh(t,e),l.Ue(e)},this.Se={},this.yh=function(e){l.Ue(e);var t=DayPilot.Util.mouseButton(e);e.preventDefault(),e.stopPropagation(),t.left&&("w-resize"===this.style.cursor||"e-resize"===this.style.cursor?(n.Re=!0,n.ig=this,n.mg=this.event,n.Fh=DayPilot.mc(e),o().style.cursor=this.style.cursor):("move"===this.style.cursor||this.event.client.moveEnabled())&&l.Gh(this,e))},this.zh=function(e){DayPilot.Util.mouseButton(e).right&&l.xa.call(this,e)},this.Gh=function(e,t){var n=l.Se;l.Ue(t),n.Hh=!0,n.Se=e,n.pg=e.event,n.Fh=DayPilot.mc(t),n.Ih=DayPilot.mo3(e,t).x,n.hg=l.getDate(l.coords.x,!0)},this.Jh={};var h=l.Jh;"undefined"==typeof DayPilot.Global.touch&&(DayPilot.Global.touch={}),DayPilot.Global.touch.active=!1,DayPilot.Global.touch.start=!1,h.Kf=[],h.Ah=function(t){if(!DayPilot.Global.touch.active&&!DayPilot.Global.touch.start){t.stopPropagation(),h.Kh(),DayPilot.Global.touch.start=!0,DayPilot.Global.touch.active=!1;var n=this;l.coords=h.Lh(t);var i=l.tapAndHoldTimeout;h.Kf.push(setTimeout(function(){DayPilot.Global.touch.active=!0,DayPilot.Global.touch.start=!1,t.preventDefault();var i=n.event;switch(l.eventTapAndHoldHandling){case"Move":if(i.client.moveEnabled()){var a=e(t);h.Gh(n,a)}}},i))}},h.Bh=function(e){h.Kh(),DayPilot.Global.touch.start=!1},h.Ch=function(e){if(!DayPilot.Util.isMouseEvent(e)){if(h.Kh(),DayPilot.Global.touch.start){DayPilot.Global.touch.start=!1,e.preventDefault(),e.stopPropagation();var t=this;setTimeout(function(){l.Te(t,e)})}setTimeout(function(){DayPilot.Global.touch.start=!1,DayPilot.Global.touch.active=!1},500)}},h.Qg=function(e){if(!(DayPilot.Global.touch.active||DayPilot.Global.touch.start||e.touches.length>1||"Disabled"===l.timeRangeSelectedHandling)){h.Kh(),DayPilot.Global.touch.start=!0,DayPilot.Global.touch.active=!1;var t=l.tapAndHoldTimeout;h.Kf.push(setTimeout(function(){DayPilot.Global.touch.active=!0,DayPilot.Global.touch.start=!1,e.preventDefault(),l.coords=h.Lh(e),h.Mh=l.Nh()},t));l.coords=h.Lh(e)}},h.Rg=function(e){if(h.Kh(),DayPilot.Global.touch.start=!1,n.ig)return e.preventDefault(),void h.Oh();if(DayPilot.Global.touch.active){if(e.preventDefault(),l.coords=h.Lh(e),n.Se)return void h.Ph();if(h.Mh){var t=h.Mh;t.end={x:Math.floor(l.coords.x/l.cellWidth),"time":l.getDate(l.coords.x,!0)},l.Cf(t)}}},h.debug=function(e){},h.Sg=function(e){h.Kh();if(DayPilot.Global.touch.active){if(n.Se){e.preventDefault();var t=n.pg;if(l!==n.qg.calendar)return;var i=n.qg.start,a=n.qg.end,r=n.qg.row.id;DayPilot.Util.removeClass(n.Se,l.L("_event_moving_source")),DayPilot.de(n.qg),n.qg.calendar=null,o().style.cursor="",n.Se=null,n.pg=null,n.qg=null,l.M(t,i,a,r)}if(h.Mh){var s=h.Mh;h.Mh=null;var d=l.elements.range2;d&&d.overlapping?l.clearSelection():l.yf(s)}}else if(DayPilot.Global.touch.start){if(l.coords.x<l.getScrollX())return;var s=l.Nh();l.Cf(s);var d=l.elements.range2;d&&d.overlapping?l.clearSelection():l.yf(s)}setTimeout(function(){DayPilot.Global.touch.start=!1,DayPilot.Global.touch.active=!1},500)},h.Kh=function(){for(var e=0;e<h.Kf.length;e++)clearTimeout(h.Kf[e]);h.Kf=[]},h.Lh=function(e){function t(e,t,n){var i=DayPilot.abs(n);return{x:e-i.x,y:t-i.y,toString:function(){return"x: "+this.x+", y:"+this.y}}}var n=l.le,i=e.touches?e.touches[0]:e;return t(i.pageX,i.pageY,n)},h.Gh=function(e,t){n.Se=e,n.pg=e.event,n.Fh=t;var i=DayPilot.abs(e);n.Ih=t.x-i.x,n.hg=l.getDate(l.coords.x,!0),n.qg=l.ba(e),l.og()},h.Oh=function(){if(!n.lg){var e=n.ig;n.lg=l.ba(e)}l.ng()},h.Ph=function(){if(!n.qg){var e=n.Se;n.qg=l.ba(e)}n.qg.calendar.og()},this.Eh=function(e,t){var i=this.eventResizeMargin,a=e;if("undefined"!=typeof n){var o=DayPilot.mo3(e,t);if(o&&(l.eventOffset=o,!n.ig&&!n.Se)){var r=a.event.part.start.toString()===a.event.start().toString(),s=a.event.part.end.toString()===a.event.rawend().toString();o.x<=i&&a.event.client.resizeEnabled()?r?(e.style.cursor="w-resize",e.dpBorder="left"):e.style.cursor="not-allowed":e.offsetWidth-o.x<=i&&a.event.client.resizeEnabled()?s?(e.style.cursor="e-resize",e.dpBorder="right"):e.style.cursor="not-allowed":n.ig||n.Se||(a.event.client.clickEnabled()?e.style.cursor="pointer":e.style.cursor="default")}}},this.Df=function(){var e=l.Pe();return l.days*(1440/e)},this.zf=function(e){var e=e||n.Mh||l.Bf;if(!e)return null;var t=l.rowlist[e.start.y];if(!t)return null;var i,a,o=e,r=o.end.time>o.start.time,s=t.id,d=r?o.start.x:o.end.x,c=r?o.end.x:o.start.x,u=l.snapToGrid;u?(i=l.Ne(d).start,a=l.Ne(c).end):r?(i=o.start.time,a=o.end.time):(i=o.end.time,a=o.start.time);var h=new DayPilot.Selection(i,a,s,l);return h.allowed=!e.div||!e.div.overlapping,h},this.af=function(){this.zb.timeHeader={},l.elements.timeHeader.length>0&&(l.elements.timeHeader=[]);var e=r();e.style.position="relative",this.nav.timeHeader=e;for(var t=0;t<this.timeHeader.length;t++)for(var n=this.timeHeader[t],i=0;i<n.length;i++)this.Qh(i,t);var a=this.divNorth;1===a.childNodes.length?a.replaceChild(e,a.childNodes[0]):(a.innerHTML="",a.appendChild(e));var o=this.Ee();a.style.width=o+5e3+"px",o>0&&(this.divStretch.style.width=o+"px")},this.Le=function(e,t){var n=null,i=this.ca.Ke(),t=t||this.cellGroupBy,a=e.start;switch(t){case"Minute":n=a.toString("m");break;case"Hour":n="Clock12Hours"===l.ca.Rh()?a.toString("h tt",i):a.toString("H",i);break;case"Day":n=a.toString(i.datePattern);break;case"Week":n=1===f.ea()?a.weekNumberISO():a.weekNumber();break;case"Month":n=a.toString("MMMM yyyy",i);break;case"Quarter":n="Q"+Math.floor(a.getMonth()/3+1);break;case"Year":n=a.toString("yyyy");break;case"None":n="";break;case"Cell":var o=(e.end.ticks-e.start.ticks)/6e4;n=this.Sh(a,o);break;default:throw new DayPilot.Exception("Invalid groupBy value: "+t)}return n},this.Sh=function(e,t){var n=this.ca.Ke(),t=t||this.cellDuration;return t<1?e.toString("ss"):t<60?e.toString("mm"):t<1440?"Clock12Hours"===l.ca.Rh()?e.toString("h tt",n):e.toString("H",n):t<10080?e.toString("d"):10080===t?1===f.ea()?e.weekNumberISO():e.weekNumber():e.toString("MMMM yyyy",n)},this.Je=function(e,t,n){var i,a=l.startDate.addDays(l.days);t=t||this.cellGroupBy;var o=60;switch(t){case"Minute":e.getMinutes()+e.getSeconds()+e.getMilliseconds()>0&&(e=e.getDatePart().addHours(e.getHours()).addMinutes(e.getMinutes())),i=e.addMinutes(1);break;case"Hour":e.getHours()+e.getMinutes()+e.getSeconds()+e.getMilliseconds()>0&&(e=e.getDatePart().addHours(e.getHours())),i=e.addHours(1);break;case"Day":i=e.getDatePart().addDays(1);break;case"Week":for(i=e.getDatePart().addDays(1);i.dayOfWeek()!==f.ea();)i=i.addDays(1);break;case"Month":e=e.getDatePart(),i=e.addMonths(1),i=i.firstDayOfMonth();for(var r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;!r;)i=i.addHours(1),r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;break;case"Quarter":for(e=e.getDatePart(),i=e.addMonths(1),i=i.firstDayOfMonth();i.getMonth()%3;)i=i.addMonths(1);for(var r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;!r;)i=i.addHours(1),r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;break;case"Year":e=e.getDatePart(),i=e.addYears(1),i=i.firstDayOfYear();for(var r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;!r;)i=i.addHours(1),r=DayPilot.DateUtil.diff(i,e)/6e4%o===0;break;case"None":i=a;break;case"Cell":var s=this.Oe(e);s.current&&(i=s.current.end);break;default:throw n?new DayPilot.Exception("Invalid scale value: "+t):new DayPilot.Exception("Invalid groupBy value: "+t)}return i.getTime()>a.getTime()&&(i=a),i},this.Qh=function(e,t){var n=this.nav.timeHeader,i=this.timeHeader[t][e],a=t<this.timeHeader.length-1,o=i.left,s=i.width,d=t*l.headerHeight,c=l.headerHeight,u=r();u.style.position="absolute",u.style.top=d+"px",u.style.left=o+"px",u.style.width=s+"px",u.style.height=c+"px",i.toolTip&&(u.title=i.toolTip),u.setAttribute("aria-hidden","true"),i.cssClass&&DayPilot.Util.addClass(u,i.cssClass),u.style.userSelect="none",u.style.webkitUserSelect="none",u.oncontextmenu=function(){return!1},u.cell={},u.cell.start=i.start,u.cell.end=i.end,u.cell.level=t,u.cell.th=i,u.onclick=this.Dg,u.oncontextmenu=this.Eg,DayPilot.re(u,DayPilot.touch.start,function(e){e.stopPropagation()}),u.style.overflow="hidden",l.timeHeaderTextWrappingEnabled||(u.style.whiteSpace="nowrap");var h=r();h.innerHTML=l.ab(i.text,i.innerHTML),i.backColor&&(h.style.background=i.backColor),i.fontColor&&(h.style.color=i.fontColor);var f=this.L("_timeheadercol"),v=this.L("_timeheadercol_inner");a&&(f=this.L("_timeheadergroup"),v=this.L("_timeheadergroup_inner")),DayPilot.Util.addClass(u,f),DayPilot.Util.addClass(h,v),DayPilot.Util.addClass(u,l.L("_timeheader_cell")),DayPilot.Util.addClass(h,l.L("_timeheader_cell_inner")),u.appendChild(h),this.zb.timeHeader[e+"_"+t]=u,this.elements.timeHeader.push(u),n.appendChild(u)},this.dg=function(){l.rowlist.forEach(function(e){var t=e.getHeight()+l.rowMarginTop+l.rowMarginBottom;e.height!==t&&(l.of=!0),e.height=t}),l.of&&(l.zb.drawArea=null)},this.bf=function(){l.rowlist.forEach(function(e){var t=l.divHeader;if(t){var n=e.index;if(t.rows[n]){var i=t.rows[n];l.ug&&(i.style.top=e.top+"px");var a=e.height;i&&i.firstChild&&parseInt(i.firstChild.style.height,10)!==a&&(i.firstChild.style.height=a+"px")}}}),l.ug&&l.nav.resScrollSpace&&(l.nav.resScrollSpace.style.top=l.he+"px")},this.eh=function(e){if(!DayPilot.Global.touch.start&&!DayPilot.Global.touch.active){n.Th,1,l.Ue(e);var t=DayPilot.Util.mouseButton(e);if(l.Uh())return!1;if(t.middle||t.right)return!1;if(l.Vh(l.coords))return!1;var i=t.left?"left":t.right?"right":t.middle?"middle":"unknown";return l.Wh=i,n.Mh=l.Nh(),n.Mh&&(n.Xh=l),!1}},this.Nh=function(){var e={},t=l.Me(l.coords.x).x,n=l.getDate(l.coords.x,!0);return e.start={y:l.Jf(l.coords.y).i,x:t,"time":n},e.end={x:t,"time":n},e.calendar=l,l.Cf(e),e},this.gh=function(e){if(l.Se={},l.Bf){if(DayPilot.Util.mouseButton(e).left){var t=l.Bf;if(l.Vh(l.coords)){var i=function(e){return function(){n.Th=null;var t=l.zf(e);if(t){var i={};i.start=t.start,i.end=t.end,i.resource=t.resource,i.preventDefault=function(){i.preventDefault.value=!0},"function"==typeof l.onTimeRangeClick&&l.onTimeRangeClick(i),i.preventDefault.value||"function"==typeof l.onTimeRangeClicked&&l.onTimeRangeClicked(i)}}};"Disabled"!==l.timeRangeClickHandling&&i(t)()}}}},this.Uh=function(){return!!(n.ig||n.Se||n.Mh)||!!l.Se.Hh},this.dragInProgress=function(){return l.Uh()},this.Ue=function(e){var t=DayPilot.mo3(l.le,e);t=t||{},t.stamp=t.x+"_"+t.y,l.coords&&l.coords.stamp===t.stamp||(l.coords=t)},this.fh=function(e){if(!DayPilot.Global.touch.active){var t=DayPilot.mc(e);if(l.Ue(e),l.Se.Hh){DayPilot.distance(l.Se.Fh,t)>2&&(DayPilot.Util.copyProps(l.Se,n),o().style.cursor="move",l.Se={})}n.ig&&n.mg.calendar===l?(n.ig.event||(n.ig.event=n.mg),l.Yh()):n.pg?l.Zh():n.Mh&&n.Mh.calendar===l&&(n.Mh.moved=!0,l.$h())}},this.$h=function(){var e=n.Mh,t=l.Me(l.coords.x).x,i=l.getDate(l.coords.x,!0);e.end={x:t,"time":i},l.Cf(e)},this.Yh=function(){n.lg||(n.lg=l.ba(n.ig)),l.ng()},this.Zh=function(){if(!n.qg){DayPilot.Util.addClass(n.Se,l.L("_event_moving_source"));var e=n.pg;n.qg=l.ba(e)}l.og()},this.hh=function(e){return e.cancelBubble=!0,!1},this.Vh=function(e){var t=l.Bf;if(!t||!t.start||!t.end)return!1;var n=this.Mf(t.start.y),i=t.start.x<t.end.x,a=(i?t.start.x:t.end.x)*this.cellWidth,o=(i?t.end.x:t.start.x)*this.cellWidth+this.cellWidth,r=n.top,s=n.bottom;return e.x>=a&&e.x<=o&&e.y>=r&&e.y<=s},this.Cf=function(e){function t(e){var t,n,s,d,c=e.end.time>e.start.time,u=e.start.y,h=e.start.time&&e.end.time;if(a||!h){var f=c?e.start.x:e.end.x,v=c?e.end.x:e.start.x,p=l.Ne(f),m=l.Ne(v);t=p.left,n=m.left+m.width}else c?(s=e.start.time,d=e.end.time):(s=e.end.time,d=e.start.time),t=l.getPixels(s).left,n=l.getPixels(d).left;var y=n-t,g=l.elements.range2;if(!g){g=r(),g.style.position="absolute";var b=r();b.className=l.L("_shadow_inner"),i&&(g.style.borderRadius=i,b.style.borderRadius=i),g.appendChild(b),l.divShadow.appendChild(g)}return g.className=l.L("_shadow"),g.firstChild.innerHTML="",g.style.left=t+"px",g.style.top=o[u].top+"px",g.style.width=y+"px",g.style.height=o[u].height-1+"px",g.calendar=l,l.elements.range2=g,g}if(e=e||n.Mh){var i=l.eventBorderRadius;"number"==typeof i&&(i+="px");var a=l.snapToGrid,o=l.rowlist;t(e)}},this._h=function(e){return{"start":{"x":e.start.x,"y":e.start.y,"time":e.start.time},"end":{"x":e.end.x,"time":e.end.time},"calendar":e.calendar,"args":e.args}},this.Kf={},this.Kf.na=null,this.Kf.drawCells=null,this.Kf.drawRows=null,this.Kf.click=null,this.Kf.resClick=[],this.Kf.updateFloats=null,this.hf=function(){if(!l.Q){l.ff();var e=l.nav.scroll;if(l.mh=e.scrollLeft,l.nh=e.scrollTop,l.oh=e.clientWidth,l.divTimeScroll&&(l.divTimeScroll.scrollLeft=l.mh),t&&n.Zg?i():l.divResScroll.scrollTop=l.nh,l.progressiveRowRendering&&(l.Kf.drawRows&&(clearTimeout(l.Kf.drawRows),l.Kf.drawRows=null),l.scrollDelayRows>0?l.Kf.drawRows=setTimeout(function(){l.xg()},l.scrollDelayRows):l.xg()),l.Kf.drawCells&&(clearTimeout(l.Kf.drawCells),l.Kf.drawCells=null),l.scrollDelayCells>0)l.Kf.drawCells=setTimeout(l.ai(),l.scrollDelayCells);else{l.ai()()}l.Kf.na&&(clearTimeout(l.Kf.na),l.Kf.na=null),l.scrollDelayEvents>0?l.Kf.na=setTimeout(l.bi(),l.scrollDelayEvents):l.na(),l.Kf.updateFloats&&(clearTimeout(l.Kf.updateFloats),l.Kf.updateFloats=null),l.scrollDelayFloats>0?l.Kf.updateFloats=setTimeout(function(){l.te()},l.scrollDelayFloats):l.te(),l.onScrollCalled=!0}},this.ai=function(){return function(){l&&l.gf()}},this.bi=function(){return function(){l&&(l.vh()?setTimeout(function(){l.qh(),setTimeout(function(){l.na(!0)},50)},50):l.ve())}},this.ff=function(){l.zb.drawArea=null},this.show=function(){l.visible=!0,l.ie=!0,l.nav.top.style.display="",l.u(),l.ch(),l.hf()},this.hide=function(){l.visible=!1,l.ie=!1,l.nav.top.style.display="none"},this.Zf=function(e){if(!l.events.list)return null;for(var t=0;t<this.events.list.length;t++){var n=this.events.list[t];if(l.Sf(n,e)){var i={};return i.ex=n,i.index=t,i}}return null},this.ci=function(){var e=this.pe(),t=e.xStart,n=e.xEnd-e.xStart,i=e.yStart,a=e.yEnd-e.yStart;this.cellProperties||(this.cellProperties={});for(var o=0;o<=n;o++){for(var r=t+o,s=0;s<a;s++){var l=i+s;this.kd(r,l)}this.di(r)}for(var d=this.sg(),l=d.start;l<d.end;l++)this.ei(l)},this.gf=function(){if(!l.Q){var e=l.rowlist;if(e&&e.length>0){if(this.cellSweeping){var t=this.cellSweepingCacheSize;this.uh(t)}this.ci()}this.of=!1}},this.pe=function(){if(l.zb.drawArea)return l.zb.drawArea;if(!this.nav.scroll)return null;var e=l.nh,t={},n=null!=this.dynamicEventRenderingMarginX?this.dynamicEventRenderingMarginX:this.dynamicEventRenderingMargin,i=null!=this.dynamicEventRenderingMarginY?this.dynamicEventRenderingMarginY:this.dynamicEventRenderingMargin,a=l.mh-n,o=a+l.oh+2*n,r=0,s=0;r=l.Me(a).x,s=l.Me(o,!0).x;var d=this.Df();s=Math.min(s,d-1),r=DayPilot.Util.atLeast(r,0);var c=e-i,u=e+this.nav.scroll.offsetHeight+2*i,h=this.Jf(c).i,f=this.Jf(u).i;f<this.rowlist.length&&f++,t.xStart=r,t.xEnd=s,t.yStart=h,t.yEnd=f;var v=l.nav.scroll;return 0===v.clientWidth&&(v=l.divTimeScroll),t.pixels={},t.pixels.left=v.scrollLeft,t.pixels.right=v.scrollLeft+v.clientWidth,t.pixels.top=v.scrollTop,t.pixels.bottom=v.scrollTop+v.clientHeight,t.pixels.width=v.scrollWidth,t.sw=DayPilot.sw(l.nav.scroll),l.zb.drawArea=t,t},this.Ee=function(){return l.Df()*l.cellWidth},this.ei=function(e){var t=l.rowlist,n=l.divLines,i="y_"+e;if(!this.zb.linesHorizontal[i]){var a=t[e],o=a.height,s=a.top+o-1,d=this.Ee(),c=r();c.style.left="0px",c.style.top=s+"px",c.style.width=d+"px",c.style.height="1px",c.style.fontSize="1px",c.style.lineHeight="1px",c.style.overflow="hidden",c.style.position="absolute",c.className=this.L("_matrix_horizontal_line"),n.appendChild(c),this.zb.linesHorizontal[i]=c}},this.di=function(e){var t=l.Ne(e);if(t){var n=l.divLines,i=l.he,a="x_"+e;if(!this.zb.linesVertical[a]){var o=t.left+t.width-1,s=r();s.style.left=o+"px",s.style.top="0px",s.style.width="1px",s.style.height=i+"px",s.style.fontSize="1px",s.style.lineHeight="1px",s.style.overflow="hidden",s.style.position="absolute",s.className=this.L("_matrix_vertical_line"),n.appendChild(s),this.elements.linesVertical.push(s),this.zb.linesVertical[a]=s}}},this.$e=function(){l.he=l.fi(l.rowlist)},this.fi=function(e){for(var t=0,n=0;n<e.length;n++){var i=e[n];i.top=t,t+=i.height}return t},this.df=function(){l.elements.cells=[],l.zb.cells=[],l.divCells.innerHTML="",l.gi()},this.gi=function(){l.divLines.innerHTML="",l.zb.linesVertical={},l.zb.linesHorizontal={},l.elements.linesVertical=[]},this.sf=function(e){var t=[];for(var n in l.zb.cells)t.push(l.zb.cells[n]);t.filter(function(t){return t&&t.coords&&t.coords.y===e}).forEach(function(e){l.hb(e)})},this.kd=function(e,t){if(this.Ma){var n=l.Ne(e);if(n){var i=l.rowlist,a=l.divCells,o=e+"_"+t;if(!this.zb.cells[o]){var s=this.hi(e,t),d=l.ii(e,t),c=r();if(c.style.left=n.left+"px",c.style.top=i[t].top+"px",c.style.width=n.width+"px",c.style.height=i[t].height+"px",c.style.position="absolute",s&&s.backColor&&(c.style.backgroundColor=s.backColor),c.className=this.L("_cell"),c.coords={},c.coords.x=e,c.coords.y=t,s){if(s.cssClass&&DayPilot.Util.addClass(c,s.cssClass),c.innerHTML=l.ab(s.text,s.html),s.backImage&&(c.style.backgroundImage='url("'+s.backImage+'")'),s.backRepeat&&(c.style.backgroundRepeat=s.backRepeat),s.business&&l.cellsMarkBusiness&&DayPilot.Util.addClass(c,l.L("_cell_business")),s.disabled&&DayPilot.Util.addClass(c,l.L("_cell_disabled")),s.backColor&&(c.style.backgroundColor=s.backColor),s.fontColor&&(c.style.color=s.fontColor),s.horizontalAlignment||s.verticalAlignment){switch(c.style.display="flex",s.horizontalAlignment){case"right":c.style.justifyContent="flex-end";break;case"left":c.style.justifyContent="flex-start";break;case"center":c.style.justifyContent="center"}switch(s.verticalAlignment){case"center":c.style.alignItems="center";break;case"top":c.style.alignItems="flex-start";break;case"bottom":c.style.alignItems="flex-end"}}DayPilot.Areas.attach(c,d.cell,{"areas":s.areas||d.cell.areas})}a.appendChild(c),this.elements.cells.push(c),this.zb.cells[o]=c}}}},this.ii=function(e,t){var n=l.Ne(e);if(!n)return null;var i=l.rowlist[t],a=i.id,o=n.start,r=n.end,s={};if(s.cell={x:e,y:t,start:o,end:r,resource:a,row:l.Cg(i),properties:l.hi(e,t)},s.control=l,"function"==typeof this.onBeforeCellRender){var d=e+"_"+t;if(l.beforeCellRenderCaching&&l.ef[d])return s;l.ef[d]=!0,this.onBeforeCellRender(s)}return s},this.clearSelection=function(){this.ji()},this.ki=function(e,t,n){e=new DayPilot.Date(e),t=new DayPilot.Date(t);var i=l.ne(n),a=l.Oe(e);if(!a.current)throw new DayPilot.Exception("Time range selection 'start' out of timeline");var o=l.Oe(new DayPilot.Date(t).addMilliseconds(-1));if(!o.current)throw new DayPilot.Exception("Time range selection 'end' out of timeline");var r={};return r.start={y:i.index,x:a.i,"time":e},r.end={x:o.i,"time":t},r.calendar=this,r},this.selectTimeRange=function(e,t,n,i){var a=l.ki(e,t,n);l.Cf(a),i||setTimeout(function(){l.yf(a)},0)},this.wf=function(){var e=n.qg&&n.qg.source;e&&DayPilot.Util.removeClass(e,l.L("_event_moving_source")),DayPilot.de(n.qg),n.qg=null,DayPilot.Global.movingLink&&(DayPilot.Global.movingLink.clear(),DayPilot.Global.movingLink=null)},this.ji=function(){l.divShadow&&(l.divShadow.innerHTML=""),l.elements.range=[],l.elements.range2=null,l.Bf=null,l.li=null},this.ca={};var f=this.ca;f.Od=function(){delete l.zb.headerHeight},f.da=function(){return"Disabled"!==l.xssProtection},f.Ke=function(){return DayPilot.Locale.find(l.locale)},f.Rh=function(){return"Auto"!==l.timeFormat?l.timeFormat:f.Ke().timeFormat},f.ea=function(){if("Auto"===l.weekStarts){var e=f.Ke();return e?e.weekStarts:0}return l.weekStarts||0},f.zg=function(){var e=navigator.userAgent.toLowerCase();return e.indexOf("mobile")!==-1||e.indexOf("android")!=-1},f.gg=function(e){return"Always"===l.useEventBoxes||"Never"!==l.useEventBoxes&&e<60*l.Pe()*1e3},this.hi=function(e,t){var n=e+"_"+t,i=l.rowlist;if(this.cellProperties||(this.cellProperties={}),this.cellProperties[n])return this.cellProperties[n];if(!this.cellProperties[n]){var a=i[t],o=a.id,r=l.Ne(e),s=r.start,d=r.end,c={};c.start=s,c.end=d,c.resource=o;var u={};u.business=l.isBusiness(c),this.cellProperties[n]=u}return this.cellProperties[n]},this.isBusiness=function(e,t){var n=e.start,i=e.end,a=(i.getTime()-n.getTime())/6e4;if(a<=1440&&!(l.businessWeekends||t||0!==e.start.dayOfWeek()&&6!==e.start.dayOfWeek()))return!1;if(a<720){var o=n.getHours();o+=n.getMinutes()/60,o+=n.getSeconds()/3600,o+=n.getMilliseconds()/36e5;var r=this.businessBeginsHour,s=this.businessEndsHour;if(0===s&&(s=24),r===s)return!1;if(!(r<s))return o<s||o>=r;if(o<r)return!1;if(s>=24)return!0;if(o>=s)return!1;
}return!0},this.u=function(){"hidden"===this.nav.top.style.visibility&&(this.nav.top.style.visibility="visible")},this.mi=function(e){this.heightSpec="Fixed",this.height=e-(this.Ge()+2),this.ma()},this.setHeight=this.mi,this.ne=function(e){return l.Gg(e)[0]},this.yb=function(){if(this.id&&this.id.tagName)this.nav.top=this.id;else{if("string"!=typeof this.id)throw new DayPilot.Exception("DayPilot.Scheduler() constructor requires the target element or its ID as a parameter");if(this.nav.top=document.getElementById(this.id),!this.nav.top)throw new DayPilot.Exception("DayPilot.Scheduler: The placeholder element not found: '"+a+"'.")}},this.init=function(){if(this.Ma)throw new DayPilot.Exception("This instance is already initialized. Use update() to change properties.");if(this.yb(),this.nav.top.dp){if(this.nav.top.dp===l)return l;throw new DayPilot.Exception("The target placeholder was already initialized by another DayPilot component instance.")}return this.ni(),this.bh(),this},this.ni=function(){this.Pg(),this.rb(),this.aa(),this.lh(),this.sb();var angular=l.De.enabled;l.scrollToDate?l.scrollTo(l.scrollToDate):l.scrollX||l.scrollY?l.setScroll(l.scrollX,l.scrollY):angular||l.hf(),l.scrollToResourceId&&(l.scrollToResource(l.scrollToResourceId),l.scrollToResourceId=null);var e=function(){l.scrollY&&l.setScroll(l.scrollX,l.scrollY)};setTimeout(e,200),this.ff(),this.xb(),this.Ma=!0;var t=l.ke;t?l.scrollTo(t):l.hf()},this.wb=null,this.ub=function(e){if(e){var t={"resources":{"preInit":function(){var e=this.data;e&&(l.resources=e)}},"events":{"preInit":function(){var e=this.data;e&&(DayPilot.isArray(e.list)?l.events.list=e.list:l.events.list=e)},"postInit":function(){}},"scrollTo":{"preInit":function(){},"postInit":function(){this.data&&l.je(this.data)}},"scrollX":{"postInit":function(){this.data&&l.me(this.data)}},"scrollY":{"postInit":function(){this.data&&l.Fe(this.data)}}};l.wb=t,l.De.scrollToRequested&&(t.scrollTo.data=l.De.scrollToRequested,l.De.scrollToRequested=null),l.De.scrollXRequested&&(t.scrollX.data=l.De.scrollXRequested,l.De.scrollXRequested=null),l.De.scrollYRequested&&(t.scrollY.data=l.De.scrollYRequested,l.De.scrollYRequested=null);for(var n in e)t[n]||(l[n]=e[n]);for(var n in e)if(t[n]){var i=t[n];i.data=e[n],i.preInit&&i.preInit()}}},this.xb=function(){var e=l.wb;for(var t in e){var n=e[t];n.postInit&&n.postInit()}l.wb={}},this.Sa={},this.Sa.Ra=null,this.Sa.Ob=function(e,t,n){var i=l.Sa.Ra;if("function"==typeof i.createVNode&&"function"==typeof i.render){var a=i.createVNode(e,n);i.render(a,t)}},this.Sa.Wa=function(e,t){var n=l.Sa.Ra;if("function"==typeof n.render){var i=e;DayPilot.isArray(e)&&(i=n.h("div",null,e)),n.render(i,t)}},this.Sa.Ua=function(e){var t=l.Sa.Ra;"function"==typeof t.render&&t.render(null,e)},this.internal={},this.internal.adjustEndIn=l.tf,this.internal.adjustEndNormalize=l.uf,this.internal.xssTextHtml=l.ab,this.internal.touch=l.Jh,this.internal.skipUpdate=l.De.skipUpdate,this.internal.skipped=l.De.skipped,this.internal.loadOptions=l.ub,this.internal.postInit=l.xb,this.internal.enableAngular2=function(){l.De.enabled=!0},this.internal.eventsFromAttr=function(){l.De.Xf=!0},this.internal.resourcesFromAttr=function(){l.De.eg=!0},this.internal.evImmediateRefresh=function(){l.events.xf()},this.internal.enableReact=function(e,t){l.ld.react=e,l.ld.reactDOM=t},this.internal.reactRefs=function(){return DayPilot.Util.copyProps(l.ld,{},["react","reactDOM"])},this.internal.enableVue=function(e){l.Sa.Ra=e},this.internal.vueRef=function(){return l.Sa.Ra},this.internal.vueRendering=function(){return l.Sa.Ta},this.ub(s)},a="2025.3.696"===(new DayPilot.Scheduler).v,DayPilot.Row=function(e,t){if(!e)throw new DayPilot.Exception("Now row object supplied when creating DayPilot.Row");if(!t)throw new DayPilot.Exception("No parent control supplied when creating DayPilot.Row");this.oi={};var n=this.oi;n.id=e.id,n.name=e.name,n.data=e.resource,n.tags=e.tags;var i=this;i.isRow=!0,i.menuType="resource",i.name=e.name,i.id=e.id,i.tags=e.tags,i.index=e.index,i.calendar=t,i.data=e.resource,i.pi=e,i.$={},i.$.row=e,i.toJSON=function(e){var t={};return t.start=this.start,t.name=this.name,t.id=this.id,t.index=this.index,t},i.events={},i.events.all=function(){for(var e=[],t=0;t<i.pi.events.length;t++)e.push(i.pi.events[t]);return e},i.events.isEmpty=function(){return 0===i.pi.events.length},i.events.forRange=function(e,t){return i.pi.events.forRange(e,t)},i.events.totalDuration=function(){var e=0;return i.events.all().forEach(function(t){e+=t.part.end.getTime()-t.part.start.getTime()}),new DayPilot.Duration(e)},i.remove=function(){t.rows.remove(i)},i.addClass=function(e){var n=t.divHeader,a=n.rows[i.index];DayPilot.Util.addClass(a,e),i.$.row.cssClass=DayPilot.Util.addClassToString(i.$.row.cssClass,e),i.data.cssClass=e},i.removeClass=function(e){var n=t.divHeader,a=n.rows[i.index];DayPilot.Util.removeClass(a,e),i.$.row.cssClass=DayPilot.Util.removeClassFromString(i.$.row.cssClass,e),i.data.cssClass=DayPilot.Util.removeClassFromString(i.data.cssClass,e)}},n.Se=null,n.pg=null,n.Fh=null,n.ig=null,n.mg=null,n.Re=!1,n.ih=!1,n.Th=null,n.qi=null,n.sc=function(e){DayPilot.ue(document,"mouseup",n.ge),DayPilot.ue(document,"touchmove",n.jh),DayPilot.ue(document,"touchend",n.kh),n.ih=!1},n.jh=function(e){if(n.ig){var t=n.ig.event.calendar;t.coords=t.Jh.Lh(e),t.Jh.Oh(),e.preventDefault()}if(n.Se){e.preventDefault();var t=n.pg.calendar;t.coords=t.Jh.Lh(e),t.Jh.Ph()}},n.kh=function(e){n.Zg=!1,n.ge(e)},n.ge=function(e){if(n.ig){var t=function(){var e=n.mg,t=e.calendar;o().style.cursor="",n.ig=null,n.mg=null,DayPilot.de(n.lg),n.lg=null,t&&(t.ri=null)};if(setTimeout(function(){n.Re=!1}),!n.lg)return void t();var a=n.mg,r=a.calendar,s=n.lg.start,l=n.lg.end,d="left"===n.ig.dpBorder?"start":"end";t(),r.K(a,s,l,d)}else if(n.pg){var t=function(){DayPilot.Global.movingAreaData=null;var e=n.qg&&n.qg.calendar;n.qg&&(DayPilot.de(n.qg),n.qg.calendar=null),o().style.cursor="",n.Se=null,n.pg=null,e&&(e.vf=null)};if(!n.qg)return void t();var a=n.pg,r=n.qg.calendar;if(!r)return void t();if(n.qg.source=n.Se,!n.qg.row)return void t();var s=n.qg.start,l=n.qg.end,c=n.qg.row.id;n.qg.calendar=null,o().style.cursor="",n.Se=null,n.pg=null,r.M(a,s,l,c,e),DayPilot.Global.movingAreaData=null}else if(n.Mh){var u=DayPilot.Util.mouseButton(e),h=n.Mh,r=h.calendar,t=function(){};r.li=null;e.ctrlKey||e.metaKey;if(n.Th)return clearTimeout(n.Th),n.Th=null,void t();r.Bf=h,n.Mh=null;var f=function(e){return function(){n.Th=null,r.yf(e),"Hold"!==r.timeRangeSelectedHandling&&"HoldForever"!==r.timeRangeSelectedHandling?i():r.Bf=e}},v=r._h(h);return t(),u.left?(f(v)(),e.cancelBubble=!0,!1):void(n.Th=null)}n.Ih=null,n.hg=null}}}(DayPilot);